{"timestamp":"2025-09-05T07:00:02.322297","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:00:35.413088","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:01:05.538398","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:01:36.542956","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:02:06.777208","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:02:37.555954","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:03:12.691311","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:03:43.790142","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:04:15.003649","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:04:46.211604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:05:17.253594","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:05:48.425938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:06:19.576860","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:06:51.134304","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:07:22.251096","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:07:53.618382","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:08:24.631250","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:08:55.848117","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:09:26.870909","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:09:58.087376","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:10:29.080025","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:11:00.052554","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:11:31.242013","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:12:02.557304","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:12:33.706092","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:13:04.930041","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:13:36.030043","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:14:07.243247","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:14:38.434617","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:15:09.947522","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:15:41.171303","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:16:12.153094","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:16:43.519275","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:17:14.816082","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:17:46.037380","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:18:17.371089","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:18:48.393956","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:19:19.877922","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:19:50.956673","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:20:22.233386","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:20:53.389851","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:21:24.146264","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:21:55.637561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:22:26.747512","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:22:58.022561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:23:29.332307","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:24:00.313611","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:24:30.554624","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:25:02.307986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:25:33.676225","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:26:04.938512","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:26:36.405000","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:27:07.449939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:27:38.940282","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:28:10.795585","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:28:41.783463","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:29:13.244810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:29:43.827502","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:30:15.242783","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:30:46.695564","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:31:18.062588","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:31:49.386508","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:32:20.432101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:32:51.897661","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:33:23.128493","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:33:54.701665","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:34:26.179914","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:34:57.436585","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:35:28.682604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:35:59.997308","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:36:31.305150","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:37:02.399740","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:37:33.636273","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:38:04.674753","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:38:34.338559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:39:05.871475","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:39:37.067585","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:40:08.272609","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:40:39.668509","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:41:10.731578","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:41:42.148152","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:42:13.405467","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:42:44.682643","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:43:16.006797","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:44:05.470521","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:44:36.684929","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:45:07.976848","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:45:39.113957","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:46:10.352697","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:46:41.284872","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:47:12.391813","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:47:43.449188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:48:14.671180","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:48:45.763110","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:49:17.240379","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:49:48.361828","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:50:19.030366","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:50:50.116669","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:51:21.328028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:51:52.443769","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:52:23.459456","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:52:54.788653","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:53:25.964982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:53:57.036436","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:54:28.227652","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:54:59.223616","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:55:30.375637","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:56:01.475259","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:56:32.561445","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:57:03.716231","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:57:34.931725","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:58:06.064655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:58:37.562327","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:59:08.714755","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:59:39.854124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:00:10.921626","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:00:42.025472","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:01:13.411188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:01:44.492314","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:02:15.649665","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:02:46.797208","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:03:18.051798","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:03:49.184753","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:04:19.843124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:04:51.422255","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:05:22.026048","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:05:53.452787","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:06:24.855366","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:06:55.244098","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:07:26.780738","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:07:58.081325","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:08:29.278941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:09:00.621837","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:09:32.001241","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:10:03.356517","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:10:34.588926","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:11:04.922028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:11:35.354804","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:12:06.815270","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:12:37.279880","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:13:08.220585","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:13:39.537454","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:14:10.149508","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:14:40.851875","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:15:11.180673","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:15:42.398694","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:16:12.967573","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:16:43.553755","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:17:14.365460","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:17:44.838226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:32:57.031900","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:33:28.295127","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:33:59.404989","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:34:30.690812","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:35:01.953424","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:35:33.144788","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:36:04.172211","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:36:35.650219","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:37:07.025280","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:37:38.209783","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:38:09.542385","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:38:40.641631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:39:12.065197","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:39:43.535329","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:40:15.026531","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:40:46.536896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:41:17.947180","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:41:49.671120","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:42:21.035488","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:42:52.387698","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:43:23.601944","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:43:54.981187","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:44:26.653298","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:44:58.002259","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:45:29.317849","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:46:00.533477","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:46:31.605473","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:47:02.976223","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:47:34.365176","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:48:05.963632","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:48:37.323197","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:49:08.750112","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:49:41.010923","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:50:12.441804","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:50:43.814000","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:51:15.057306","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:51:46.490788","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:52:17.853409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:52:49.043827","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:53:19.563423","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:53:51.429020","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:54:22.683555","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:54:53.897089","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:55:25.208186","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:55:56.396358","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:56:27.565024","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:56:58.824299","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:57:30.092005","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:58:01.314550","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:58:32.690104","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:59:03.852495","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:59:35.137519","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:00:06.427002","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:00:36.961557","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:01:07.365772","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:01:37.768435","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:02:08.206597","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:02:39.546295","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:03:10.033976","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:03:40.283825","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:04:10.834194","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:04:42.059788","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:05:12.289402","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:05:42.610279","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:06:12.908024","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:06:43.318139","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:07:13.727034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:07:43.925594","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:08:14.421515","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:08:44.835713","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:09:16.308572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:09:46.942573","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:10:17.733529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:10:48.133414","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:11:18.603299","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:11:49.047932","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:12:19.496765","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:12:50.125920","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:13:20.352485","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:13:50.697123","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:14:21.128669","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:14:51.720850","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:15:22.487028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:15:53.021967","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:16:23.774272","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:16:54.727097","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:17:25.230381","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:17:55.787553","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:18:26.236627","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:18:56.878214","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:19:27.536276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:19:58.174353","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:20:28.644278","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:20:59.502762","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:21:30.177816","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:22:00.939043","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:22:31.751166","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:23:02.692146","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:23:33.106696","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:24:03.697508","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:24:34.055632","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:25:04.636398","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:25:35.209563","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:26:05.641656","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:26:36.206984","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:27:07.052939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:27:37.594683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:28:08.218388","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:28:39.105377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:29:09.633694","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:29:40.580680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:30:11.088591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:30:42.031731","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:31:12.393795","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:31:42.576972","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:32:13.497782","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:32:44.234707","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:33:14.700981","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:33:45.678918","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:34:16.156821","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:34:46.849530","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:35:17.275834","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:35:47.590852","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:36:18.325949","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:36:49.257833","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:37:19.521961","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:37:50.173543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:38:20.728394","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:38:51.452160","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:39:21.844986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:39:52.707341","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:40:23.137626","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:40:53.597674","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:41:24.335598","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:41:54.941139","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:42:25.061510","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:42:55.623242","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:43:25.895869","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:43:56.464382","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:44:28.069393","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:44:58.914202","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:45:29.425376","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:45:59.770538","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:46:29.977690","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:47:01.527040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:47:31.657424","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:48:02.020083","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:48:32.673593","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:49:03.431925","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:49:33.931050","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:50:04.588329","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:50:34.931590","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:51:05.383668","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:51:35.816787","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:52:06.765732","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:52:37.090734","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:53:07.382714","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:53:38.066040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:54:08.671546","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:54:39.181438","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:55:10.125610","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:55:40.882727","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:56:11.430131","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:56:42.176403","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:57:12.525838","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:57:43.227925","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:58:13.915551","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:58:44.659419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:59:15.316418","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:59:46.028431","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:00:16.886939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:00:47.553985","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:01:18.451210","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:01:49.411448","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:02:20.167587","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:02:50.687184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:03:21.609149","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:03:52.244809","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:04:22.990764","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:04:53.564477","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:05:23.962707","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:05:54.631307","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:06:25.471179","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:06:56.479488","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:07:26.967189","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:07:57.393087","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:08:28.170188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:08:58.693675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:09:29.391464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:09:59.911808","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:10:30.499705","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:11:01.628629","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:11:32.475059","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:12:03.041669","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:12:33.436603","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:13:04.106191","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:13:34.922591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:14:05.412006","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:14:35.982428","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:15:06.690037","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:15:37.593670","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:16:08.155262","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:16:39.205561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:17:09.865691","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:17:40.717042","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:18:11.301010","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:18:42.050323","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:19:12.613210","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:19:43.345583","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:20:14.073563","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:20:45.238226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:21:16.027250","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:21:46.682236","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:22:17.230721","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:22:48.213987","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:23:19.109052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:23:49.849046","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:24:20.810950","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:24:51.484263","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:25:22.609863","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:25:53.402767","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:26:24.209553","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:26:55.217970","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:27:26.582539","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:27:56.941159","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:28:27.509740","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:28:57.851879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:29:28.772349","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:29:59.599356","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:30:30.124574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:31:00.811612","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:31:31.670170","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:32:02.226328","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:32:33.244534","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:33:03.706563","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:33:34.092291","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:34:04.711252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:34:35.386970","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:35:06.117352","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:35:36.732715","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:36:07.730687","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:36:38.601033","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:37:09.221363","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:37:40.397924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:38:11.680919","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:38:42.533618","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:39:13.201779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:39:43.556072","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:40:14.346390","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:40:44.561294","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:41:15.092775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:41:45.739414","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:42:16.935779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:42:47.756185","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:43:18.402726","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:43:48.762725","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:44:19.146398","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:44:49.710538","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:45:20.531533","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:45:51.248440","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:46:23.088450","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:46:53.558626","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:47:24.658261","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:47:55.587002","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:08:51.732015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:09:23.140700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:09:53.428913","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:10:23.834315","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:10:55.220419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:11:26.567873","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:11:57.712175","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:12:28.081867","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:12:59.729292","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:13:29.858850","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:14:00.212934","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:14:31.812662","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:15:03.359276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:15:34.737377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:16:05.889331","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:16:36.360553","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:17:07.670798","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:17:38.989843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:18:09.457681","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:18:40.481154","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:19:10.785069","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:19:41.191848","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:20:12.780654","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:20:43.591942","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:21:13.889831","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:21:44.621769","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:22:15.002398","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:22:45.556115","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:23:15.868540","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:23:46.702620","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:24:17.176507","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:24:47.523261","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:25:18.854039","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:25:50.008534","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:26:20.416433","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:26:51.825631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:27:23.197731","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:51:17.425448","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:51:47.534722","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:52:18.758326","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:52:49.526689","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:53:20.004259","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:53:51.273804","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:54:22.537992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:54:53.692999","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:55:24.774132","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:55:55.976740","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:56:27.418544","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:56:58.752095","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:57:30.169182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:58:01.292332","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:58:31.575119","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:59:03.165096","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:59:33.366148","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:00:05.042663","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:00:35.610815","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:01:06.933991","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:10:25.020499","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:10:55.321126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:11:26.950368","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:11:58.451853","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:12:28.744793","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:12:59.712535","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:13:31.318327","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:14:02.847460","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:14:34.159443","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:15:04.572363","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:15:35.293839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:16:05.653755","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:16:36.128538","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:17:06.990972","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:17:37.538802","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:18:07.864650","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:18:38.469941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:19:08.975454","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:19:39.398099","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:20:09.900822","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:20:40.534773","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:21:11.111835","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:21:42.460094","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:22:13.402364","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:22:45.033239","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:23:15.512277","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:23:51.038131","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:24:23.417468","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:24:54.515871","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:25:25.448370","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:25:56.271024","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:26:27.121342","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:26:57.550143","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:27:28.299030","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:27:58.989917","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:28:29.775694","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:29:00.523839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:29:31.533296","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:30:02.099539","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:46:32.886293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:47:03.575472","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:47:33.886361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:48:05.206640","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:48:36.220704","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:49:06.520331","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:49:37.086782","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:50:07.574200","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:50:37.928783","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:51:08.987026","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:51:40.081653","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:52:11.264068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:52:42.125568","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:53:12.681575","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:53:43.422640","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:54:14.568672","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:54:45.012889","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:55:15.774310","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:55:46.343610","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:56:16.692408","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:56:47.562748","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:57:18.061658","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:57:48.648259","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:58:18.955042","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:58:49.549462","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:59:20.015319","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:59:50.650572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:00:20.990893","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:00:52.696815","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:01:23.346283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:01:53.846218","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:02:33.958686","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:03:06.843251","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:03:37.605938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:04:08.334213","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:04:39.372712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:05:10.059298","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:05:40.831341","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:06:11.586566","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:06:42.262616","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:07:13.339260","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:07:43.758792","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:08:14.431015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:08:44.928553","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:50:32.215897","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:51:04.114729","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:51:34.567188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:52:05.922439","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:52:37.186171","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:53:08.740094","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:53:40.031098","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:54:11.174558","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:54:41.952443","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:55:13.419644","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:55:44.866183","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:56:16.031763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:56:47.604387","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:57:19.011309","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:57:50.319976","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:58:21.413337","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:58:52.471810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:59:22.770497","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:59:53.530745","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:00:24.898435","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:00:55.537563","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:01:26.646663","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:01:57.439746","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:02:28.882044","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:02:59.806951","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:03:31.627192","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:04:02.228387","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:04:33.785655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:05:04.961896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:05:36.641785","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:06:07.127141","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:06:38.001845","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:07:08.786574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:07:39.441516","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:08:10.496229","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:08:42.163780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:09:12.705931","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:09:43.554385","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:10:14.610138","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:10:46.563738","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:11:17.443730","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:11:47.767091","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:12:18.273783","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:12:50.692634","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:13:21.538247","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:13:53.128397","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:14:24.103234","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:14:55.811773","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:15:26.403206","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:15:57.515252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:16:29.391092","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:16:59.932127","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:17:31.704657","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:18:03.556161","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:18:35.009461","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:19:06.840613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:19:37.677037","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:20:09.155080","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:20:40.002332","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:21:10.912665","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:21:41.370681","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:22:12.159062","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:22:43.990013","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:23:15.441553","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:23:47.764265","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:24:18.624519","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:24:50.413073","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:25:21.059810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:25:52.585144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:26:23.177662","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:26:54.088515","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:27:24.708492","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:27:55.751634","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:28:26.204464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:28:56.933473","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:29:28.453228","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:29:59.045404","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:30:29.860156","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:31:00.262861","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:31:31.321113","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:32:02.388169","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:32:32.817522","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:33:03.297838","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:33:34.933291","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:34:05.762812","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:34:36.090589","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:49:35.600353","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:50:05.711312","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:50:36.449852","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:51:08.059164","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:51:38.481544","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:52:09.276188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:52:39.988861","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:53:10.206135","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:53:40.782445","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:54:12.635687","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:54:44.148471","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:55:14.296086","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:55:46.043173","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:56:17.625234","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:56:48.037541","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:57:18.423171","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:57:48.926823","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:58:19.222346","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:58:49.846103","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:59:21.639699","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:59:52.310948","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:00:22.676132","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:00:54.353477","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:01:24.961151","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:01:55.196273","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:02:27.061769","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:02:57.505432","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:03:28.888615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:03:59.009761","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:04:30.331786","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:05:00.833994","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:05:32.063407","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:06:02.657044","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:06:34.133772","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:07:05.414140","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:07:36.618401","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:08:08.136788","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:08:39.543226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:09:11.019120","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:09:41.699810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:11:09.552092","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:11:39.964204","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:12:11.281381","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:12:42.821699","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:13:13.217341","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:13:45.015027","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:14:15.529910","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:14:47.168633","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:15:17.707184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:15:49.356290","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:16:21.191829","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:16:52.530246","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:17:23.950570","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:17:55.617299","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:18:26.458798","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:18:58.098823","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:19:29.432041","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:20:01.003459","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:20:31.909129","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:21:02.719484","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:21:33.254299","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:22:04.879326","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:22:35.762014","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:23:07.120818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:23:37.834569","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:24:09.241337","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:24:40.360430","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:25:11.062142","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:25:42.394119","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:26:14.116047","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:26:45.147612","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:27:16.583635","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:27:48.374169","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:28:20.214022","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:28:51.657287","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:29:23.444533","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:29:54.542288","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:30:26.116204","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:30:57.878145","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:31:29.606934","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:32:00.996177","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:32:32.668350","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:33:04.084710","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:33:35.841500","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:34:06.126658","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:34:36.874911","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:35:08.115926","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:35:40.021792","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:36:10.731669","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:36:42.210923","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:37:14.020638","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:37:44.671652","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:38:15.525524","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:38:47.244123","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:39:18.722316","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:39:49.172664","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:40:19.808933","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:40:50.706557","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:41:21.446235","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:41:52.277535","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:42:24.126722","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:42:54.891040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:43:25.731395","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:43:56.509738","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:44:27.131248","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:44:57.937163","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:45:28.987769","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:46:00.879749","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:46:31.405953","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:47:02.214020","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:47:32.743709","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:48:04.118177","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:48:34.964939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:49:05.813535","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:49:36.316766","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:50:06.978554","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:50:37.506167","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:51:07.978014","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:51:38.568024","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:52:09.273121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:52:39.904208","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:53:10.635967","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:53:41.586400","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:54:12.255811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:54:43.062173","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:55:13.656737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:55:44.282762","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:56:14.810286","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:56:45.416414","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:57:15.814447","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:57:46.526894","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:58:17.122733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:58:47.568474","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:59:18.790117","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:59:49.961255","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:00:20.711787","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:00:51.504723","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:01:22.311052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:01:53.202466","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:02:23.946764","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:02:54.662396","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:03:25.144511","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:03:56.182896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:04:26.878298","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:04:57.303944","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:05:28.187910","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:05:58.989740","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:06:29.980778","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:07:00.571388","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:07:31.462272","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:08:01.923800","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:08:33.139155","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:09:03.943039","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:09:34.949266","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:10:05.442383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:10:36.553485","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:11:07.277726","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:11:37.738333","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:12:08.500967","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:12:39.273428","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:13:09.734557","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:13:39.900936","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:14:10.672272","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:14:41.453263","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:15:12.563621","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:15:43.872322","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:16:14.454014","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:16:45.366450","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:17:16.138448","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:17:46.708951","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:18:17.502002","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:18:48.416826","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:19:18.953784","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:19:49.889451","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:20:20.511337","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:20:52.691578","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:21:23.429661","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:21:53.577140","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:22:24.159916","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:22:55.323013","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:23:26.355285","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:23:56.931658","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:24:27.832939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:24:58.543241","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:25:29.852826","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:26:00.772302","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:26:31.772121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:27:02.291544","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:27:33.548691","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:28:04.562732","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:28:35.009812","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:29:06.067902","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:29:37.087240","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:30:07.735296","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:30:38.927378","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:31:09.793158","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:31:40.390062","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:32:11.184823","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:32:41.974423","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:33:14.007322","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:33:44.991012","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:34:15.553477","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:34:46.457466","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:35:16.606960","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:35:48.363355","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:36:18.789508","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:36:49.718020","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:37:20.719722","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:37:50.953498","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:38:21.982422","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:38:52.614849","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:39:23.424837","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:39:54.279505","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:40:25.032334","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:40:56.135697","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:41:26.936885","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:41:57.463514","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:42:28.382380","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:42:59.246780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:43:29.904241","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:44:00.154375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:44:31.035677","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:45:02.148712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:45:33.091748","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:46:03.785303","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:46:34.621430","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:47:05.771738","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:47:36.262952","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:48:07.939873","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:48:39.157881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:49:09.807224","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:49:40.773286","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:50:11.565655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:50:42.716933","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:51:13.434402","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:51:45.255396","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:52:16.199715","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:52:47.193634","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:53:17.985825","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:53:49.126467","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:54:19.998437","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:54:50.865341","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:55:21.982535","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:55:52.789212","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:56:23.744607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:56:54.832993","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:57:25.409676","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:57:56.612127","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:58:27.547371","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:58:57.969916","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:59:29.069450","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:59:59.563061","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:00:30.377763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:01:01.294323","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:01:31.869317","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:02:02.538124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:02:33.336297","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:03:03.842618","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:03:34.635975","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:04:07.432380","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:04:40.460662","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:05:12.029230","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:05:43.054496","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:06:20.520335","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:06:51.927817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:07:24.462103","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:07:55.833277","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:08:26.282209","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:09:00.607569","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:09:31.050824","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:10:03.993562","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:10:36.032807","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:11:06.452727","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:11:37.013283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:12:07.657987","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:12:38.625173","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:13:09.893013","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:13:41.127233","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:14:11.562260","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:14:42.462924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:15:13.183392","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:15:43.826427","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:16:14.963049","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:16:46.276728","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:17:16.749601","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
