{"timestamp":"2025-09-05T07:00:01.596067","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:00:32.985375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:01:04.235980","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:01:35.115137","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:02:06.359192","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:02:37.382181","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:03:12.583063","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:03:43.599857","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:04:14.815832","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:04:45.820310","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:05:17.240981","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:05:48.387539","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:06:19.557823","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:06:51.108263","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:07:22.241652","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:07:53.590755","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:08:24.598492","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:08:55.837625","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:09:26.850213","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:09:58.063010","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:10:29.062591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:11:00.028823","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:11:31.224795","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:12:02.532395","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:12:33.682109","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:13:04.904608","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:13:36.009797","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:14:07.222397","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:14:38.401205","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:15:09.920889","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:15:41.145632","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:16:12.118691","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:16:43.496367","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:17:14.796397","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:17:46.012317","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:18:17.342091","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:18:48.370334","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:19:19.846775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:19:50.931598","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:20:22.206778","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:20:53.366019","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:21:24.125803","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:21:55.557694","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:22:26.723325","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:22:57.764535","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:23:28.930864","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:24:00.116738","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:24:30.519249","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:25:02.198272","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:25:33.645660","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:26:04.911536","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:26:36.377469","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:27:07.426032","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:27:38.919814","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:28:10.742121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:28:41.758389","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:29:13.220710","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:29:43.807318","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:30:15.132919","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:30:46.672733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:31:18.031210","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:31:49.358976","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:32:20.408319","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:32:51.829345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:33:23.106201","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:33:54.676079","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:34:26.152009","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:34:57.402367","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:35:28.657258","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:35:59.966337","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:36:31.273705","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:37:02.374547","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:37:33.609690","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:38:04.652663","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:38:34.271728","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:39:05.827581","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:39:37.047545","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:40:08.241405","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:40:39.643243","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:41:10.706992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:41:42.127138","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:42:13.382457","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:42:44.644966","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:43:15.987129","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:44:05.264057","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:44:36.662822","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:45:07.955091","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:45:39.086973","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:46:10.327853","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:46:41.265046","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:47:12.368796","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:47:43.429677","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:48:14.649041","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:48:45.753756","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:49:17.179679","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:49:48.184901","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:50:18.887919","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:50:50.096635","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:51:21.294761","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:51:52.424890","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:52:23.441472","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:52:54.766420","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:53:25.945078","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:53:57.016930","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:54:28.191238","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:54:59.204912","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:55:30.366017","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:56:01.458119","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:56:32.542013","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:57:03.693751","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:57:34.920869","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:58:06.043051","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:58:37.542150","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:59:08.696959","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:59:39.833293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:00:10.904961","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:00:42.003239","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:01:13.373402","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:01:44.474973","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:02:15.629027","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:02:46.774000","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:03:18.026701","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:03:49.154622","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:04:19.820667","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:04:51.378274","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:05:21.996970","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:05:53.413650","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:06:24.854289","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:06:55.214732","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:07:26.752815","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:07:58.032409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:08:29.232239","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:09:00.462558","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:09:31.651376","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:10:02.972669","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:10:34.367543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:11:04.724711","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:11:35.173320","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:12:05.346751","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:12:35.544854","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:13:05.907536","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:13:36.197956","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:14:06.557117","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:14:37.129883","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:15:08.711796","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:15:40.081697","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:16:10.629998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:16:41.196626","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:17:11.761310","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:17:42.355020","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:32:57.010897","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:33:28.284186","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:33:59.382944","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:34:30.667612","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:35:01.931533","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:35:33.132204","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:36:04.150853","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:36:35.628210","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:37:07.001629","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:37:38.182467","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:38:09.514785","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:38:40.618597","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:39:12.042924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:39:43.513841","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:40:15.007637","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:40:46.505949","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:41:17.875221","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:41:49.638643","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:42:21.013655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:42:52.367577","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:43:23.576217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:43:54.953053","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:44:26.628129","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:44:57.970427","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:45:29.285581","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:46:00.508514","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:46:31.579928","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:47:02.945469","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:47:34.327373","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:48:05.927544","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:48:37.289709","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:49:08.729559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:49:40.945401","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:50:12.408386","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:50:43.788905","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:51:15.017929","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:51:46.460876","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:52:17.828489","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:52:49.013749","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:53:19.544988","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:53:51.403957","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:54:22.651444","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:54:53.884645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:55:25.171124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:55:56.369620","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:56:27.540191","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:56:58.800795","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:57:30.055510","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:58:01.289229","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:58:32.663131","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:59:03.833287","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:59:35.111173","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:00:06.398630","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:00:36.597198","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:01:07.172626","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:01:37.574369","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:02:07.993042","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:02:39.150197","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:03:09.715830","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:03:40.093350","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:04:10.609056","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:04:41.188079","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:05:12.243466","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:05:42.604139","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:06:12.907184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:06:43.313456","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:07:13.723422","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:07:43.925594","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:08:14.421515","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:08:44.829648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:09:15.186375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:09:45.770149","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:10:16.437015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:10:47.015121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:11:17.514482","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:11:47.931922","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:12:18.373495","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:12:49.005604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:13:19.213417","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:13:49.620953","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:14:19.945611","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:14:50.608238","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:15:21.363520","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:15:51.880858","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:16:22.626489","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:16:53.617203","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:17:24.101825","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:17:54.673084","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:18:25.124791","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:18:55.762684","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:19:26.416610","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:19:57.054261","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:20:27.383772","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:20:58.377020","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:21:29.065782","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:21:59.774864","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:22:30.629835","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:23:01.553620","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:23:31.965247","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:24:02.570543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:24:32.939305","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:25:03.554555","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:25:34.083956","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:26:04.519367","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:26:35.084886","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:27:05.934771","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:27:36.472362","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:28:06.873573","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:28:37.981195","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:29:08.510763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:29:39.452786","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:30:09.973835","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:30:40.913305","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:31:11.277685","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:31:41.480435","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:32:12.365596","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:32:43.127022","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:33:13.428805","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:33:44.553274","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:34:15.060897","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:34:45.725784","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:35:16.154389","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:35:46.458374","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:36:17.228709","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:36:48.125571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:37:18.397472","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:37:49.052124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:38:19.453851","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:38:50.338656","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:39:20.721221","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:39:51.582478","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:40:22.019733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:40:52.318562","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:41:23.214295","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:41:53.814681","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:42:23.936076","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:42:55.619702","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:43:25.831447","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:43:56.245553","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:44:26.781062","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:44:57.211375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:45:27.959704","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:45:58.367973","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:46:28.665781","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:46:59.123909","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:47:29.865302","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:48:00.876215","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:48:31.538162","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:49:02.291219","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:49:32.782521","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:50:03.454160","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:50:33.801493","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:51:04.303680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:51:34.660562","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:52:05.612649","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:52:35.952703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:53:06.251634","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:53:36.903876","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:54:07.550646","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:54:38.034196","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:55:08.982343","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:55:39.759709","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:56:10.286028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:56:41.053687","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:57:11.412453","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:57:41.904040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:58:12.798164","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:58:43.526367","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:59:14.212931","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:59:44.795913","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:00:15.528273","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:00:46.396966","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:01:17.261438","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:01:48.214952","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:02:18.986405","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:02:49.518826","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:03:20.468769","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:03:51.037453","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:04:21.856127","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:04:52.377265","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:05:22.796307","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:05:53.469922","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:06:24.340943","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:06:55.384475","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:07:25.791705","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:07:56.244901","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:08:27.035999","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:08:57.549609","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:09:28.224497","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:09:58.755514","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:10:29.358419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:10:59.506305","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:11:30.345497","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:12:00.818968","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:12:31.276929","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:13:01.946757","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:13:32.768773","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:14:03.224333","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:14:33.822778","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:15:04.438363","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:15:35.438718","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:16:05.997362","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:16:36.738872","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:17:07.692602","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:17:38.480865","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:18:09.122527","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:18:39.810524","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:19:10.477545","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:19:41.042793","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:20:11.919832","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:20:42.990709","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:21:13.729853","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:21:44.553127","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:22:15.056608","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:22:46.036251","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:23:16.962719","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:23:47.467944","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:24:18.568382","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:24:49.328266","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:25:20.376385","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:25:51.213937","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:26:21.888043","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:26:52.715677","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:27:23.143489","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:27:53.668452","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:28:24.217542","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:28:54.609161","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:29:25.357394","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:29:56.364965","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:30:26.913839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:30:57.301264","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:31:28.414023","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:31:58.970427","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:32:29.994487","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:33:00.501385","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:33:30.869434","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:34:01.444606","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:34:32.102824","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:35:02.873319","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:35:33.466293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:36:04.487819","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:36:35.399715","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:37:05.980983","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:37:36.751796","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:38:07.311483","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:38:38.251413","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:39:08.928879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:39:39.327809","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:40:09.713817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:40:40.365385","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:41:10.822239","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:41:41.497317","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:42:11.693085","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:42:42.260289","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:43:12.516024","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:43:43.219197","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:44:13.702224","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:44:44.181875","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:45:14.630686","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:45:45.669971","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:46:16.469439","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:46:46.912163","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:47:17.647570","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:47:48.623300","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:08:51.343337","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:09:22.715749","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:09:53.241456","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:10:23.641740","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:10:54.873987","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:11:26.193473","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:11:57.507465","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:12:27.879194","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:12:59.265253","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:13:29.839501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:14:00.194787","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:14:31.627031","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:15:02.887240","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:15:34.366147","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:16:05.686794","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:16:36.170202","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:17:07.267162","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:17:38.653130","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:18:09.229413","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:18:40.061866","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:19:10.489712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:19:40.977885","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:20:11.495724","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:20:42.072014","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:21:12.583159","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:21:43.338934","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:22:13.663770","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:22:44.006081","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:23:14.583606","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:23:45.456045","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:24:15.925192","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:24:47.134283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:25:18.483215","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:25:49.817793","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:26:20.198440","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:26:51.400744","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:27:22.826561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:51:17.041182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:51:47.430901","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:52:18.720106","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:52:49.506134","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:53:19.960289","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:53:51.224874","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:54:22.521433","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:54:53.678529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:55:24.730532","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:55:55.922531","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:56:27.363395","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:56:58.581573","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:57:29.784163","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:58:01.108049","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:58:31.333977","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:59:02.741355","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:59:33.348273","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:00:04.861338","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:00:35.427972","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:01:06.531991","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:10:24.596327","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:10:55.128909","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:11:26.735287","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:11:58.050005","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:12:28.526578","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:12:59.435355","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:13:29.602353","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:14:01.136068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:14:31.595992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:15:02.374502","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:15:33.162442","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:16:03.488050","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:16:33.961794","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:17:04.829115","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:17:35.370374","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:18:05.704684","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:18:36.316197","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:19:06.832006","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:19:37.235086","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:20:07.742311","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:20:38.377652","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:21:08.926691","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:21:39.601164","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:22:09.900378","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:22:40.855110","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:23:11.309465","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:23:50.581031","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:24:22.232404","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:24:54.237172","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:25:25.226623","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:25:55.575967","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:26:26.563867","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:26:57.334534","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:27:28.080635","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:27:58.561641","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:28:29.555902","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:29:00.331239","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:29:31.290592","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:30:01.671168","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:30:32.643266","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:47:01.675530","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:47:32.170826","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:48:02.471665","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:48:33.140403","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:49:04.087640","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:49:34.615842","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:50:04.862852","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:50:35.460788","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:51:06.578580","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:51:37.583169","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:52:08.056041","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:52:39.282766","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:53:10.118141","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:53:40.535933","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:54:11.465036","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:54:42.446824","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:55:13.048469","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:55:43.645395","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:56:14.180674","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:56:45.064483","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:57:15.624335","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:57:45.943881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:58:16.551138","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:58:47.073145","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:59:17.360310","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:59:47.756357","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:00:18.566649","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:00:48.809861","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:01:19.563014","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:01:50.140238","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:02:20.577869","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:02:50.901231","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:03:21.981353","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:03:52.551942","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:04:22.953839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:04:53.166484","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:05:23.975905","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:05:54.458469","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:06:25.416706","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:06:56.370185","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:07:27.854612","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:07:58.385569","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:08:29.216360","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:08:59.833542","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:50:32.018326","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:51:02.912935","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:51:34.472816","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:52:05.881817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:52:37.146703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:53:08.705825","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:53:40.001554","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:54:11.147132","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:54:41.926121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:55:13.382823","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:55:44.833707","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:56:15.998928","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:56:47.569805","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:57:18.987547","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:57:50.157186","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:58:21.368158","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:58:52.394426","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:59:22.738081","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:59:53.498774","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:00:24.871808","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:00:55.500207","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:01:26.590953","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:01:57.407037","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:02:28.837505","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:02:59.759040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:03:31.587349","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:04:02.186775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:04:33.650724","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:05:04.921457","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:05:36.601892","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:06:07.087436","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:06:37.964921","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:07:08.741316","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:07:39.409204","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:08:10.454513","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:08:42.127657","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:09:12.654082","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:09:43.503946","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:10:14.562870","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:10:45.403296","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:11:16.246591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:11:47.708951","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:12:18.232656","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:12:50.628296","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:13:21.498735","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:13:53.082867","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:14:24.054011","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:14:55.769228","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:15:26.352705","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:15:57.478469","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:16:29.352353","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:16:59.898445","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:17:31.672264","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:18:03.510329","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:18:34.970518","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:19:06.802589","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:19:37.641175","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:20:09.040118","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:20:39.857664","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:21:10.796130","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:21:41.307092","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:22:12.127288","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:22:43.949786","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:23:15.401482","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:23:47.724091","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:24:18.511869","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:24:50.219053","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:25:20.537861","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:25:52.030707","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:26:22.870016","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:26:53.819546","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:27:24.230268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:27:55.229242","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:28:25.954933","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:28:56.682367","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:29:27.313230","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:29:58.748419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:30:29.090919","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:31:00.016464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:31:31.106011","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:32:02.193139","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:32:32.434599","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:33:03.051730","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:33:33.616891","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:34:04.148464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:34:34.788302","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:49:35.219361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:50:05.604778","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:50:36.269836","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:51:07.602529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:51:38.299612","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:52:09.067772","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:52:39.426689","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:53:10.154683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:53:40.738292","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:54:12.434130","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:54:43.684067","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:55:14.181481","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:55:45.862032","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:56:17.126550","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:56:47.793611","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:57:18.187033","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:57:48.379531","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:58:18.927363","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:58:49.739849","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:59:21.448911","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:59:51.878714","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:00:22.484522","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:00:54.158487","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:01:24.494035","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:01:54.949013","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:02:25.719045","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:02:57.282500","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:03:28.417626","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:03:58.884893","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:04:30.044013","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:05:00.366291","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:05:31.866992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:06:02.593663","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:06:34.096034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:07:05.381560","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:07:36.590506","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:08:08.106784","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:08:39.512884","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:09:10.980069","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:09:41.665180","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:11:09.122427","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:11:39.756219","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:12:11.112548","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:12:42.318546","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:13:13.035239","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:13:43.833050","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:14:15.501544","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:14:47.160487","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:15:17.749283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:15:49.309899","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:16:21.151333","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:16:52.490908","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:17:23.914987","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:17:55.576476","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:18:26.413126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:18:58.063516","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:19:29.397939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:20:00.985077","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:20:31.840721","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:21:02.678617","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:21:33.224734","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:22:04.849737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:22:35.727724","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:23:07.085564","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:23:37.788037","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:24:09.204515","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:24:40.258630","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:25:11.024362","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:25:42.355649","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:26:14.080512","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:26:45.111046","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:27:16.551566","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:27:48.345261","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:28:20.184486","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:28:51.627948","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:29:23.413769","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:29:54.501769","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:30:26.073608","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:30:57.848344","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:31:29.572661","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:32:00.958361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:32:32.640492","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:33:04.054434","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:33:35.737144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:34:05.882023","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:34:36.592410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:35:08.084938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:35:39.975927","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:36:10.701204","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:36:42.175985","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:37:13.685564","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:37:44.331464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:38:15.427343","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:38:47.118166","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:39:18.251680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:39:48.925423","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:40:19.658810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:40:50.209572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:41:21.140532","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:41:52.032851","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:42:22.504014","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:42:53.502975","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:43:24.359637","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:43:54.884743","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:44:25.841628","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:44:56.768072","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:45:28.612962","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:45:59.330634","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:46:30.108149","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:47:00.912390","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:47:31.459310","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:48:01.662008","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:48:32.480952","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:49:03.060252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:49:33.865727","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:50:04.550354","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:50:34.916481","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:51:05.544007","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:51:37.222399","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:52:07.723097","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:52:38.561875","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:53:09.296697","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:53:39.985613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:54:10.929811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:54:41.718694","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:55:12.206364","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:55:42.957797","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:56:13.529272","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:56:43.939270","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:57:14.515061","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:57:45.238477","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:58:15.647613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:58:46.269260","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:59:17.270139","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:59:48.194385","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:00:19.389588","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:00:50.212732","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:01:20.710219","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:01:51.814784","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:02:22.663035","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:02:53.122057","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:03:23.828631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:03:54.838006","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:04:25.319885","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:04:56.012166","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:05:26.884065","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:05:57.463219","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:06:28.296186","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:06:59.304363","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:07:30.172680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:08:00.633797","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:08:31.822201","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:09:02.640476","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:09:33.312211","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:10:04.124300","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:10:35.098416","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:11:05.743438","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:11:36.443962","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:12:07.212424","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:12:37.721782","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:13:08.447355","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:13:38.899160","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:14:10.222751","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:14:41.241067","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:15:12.154589","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:15:43.155784","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:16:14.226590","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:16:45.141413","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:17:15.712076","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:17:46.459755","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:18:16.954400","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:18:47.940025","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:19:18.713016","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:19:49.638483","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:20:20.255404","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:20:51.386553","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:21:22.130680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:21:52.820155","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:22:23.939453","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:22:54.902418","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:23:25.789981","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:23:56.717185","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:24:27.622765","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:24:58.315475","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:25:29.579696","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:26:00.214513","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:26:31.246779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:27:02.085986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:27:33.131686","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:28:04.075275","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:28:34.802226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:29:05.815915","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:29:36.559815","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:30:07.471947","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:30:38.138853","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:31:09.368689","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:31:40.176260","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:32:10.973871","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:32:41.503010","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:33:12.688597","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:33:43.687846","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:34:14.253500","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:34:45.107818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:35:15.811405","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:35:47.028304","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:36:17.407992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:36:48.199899","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:37:19.024613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:37:49.627017","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:38:20.672865","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:38:51.302186","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:39:22.203557","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:39:53.112499","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:40:23.874683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:40:54.933610","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:41:25.784340","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:41:56.302360","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:42:27.236038","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:42:58.084873","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:43:28.742419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:44:00.112694","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:44:31.003286","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:45:02.114135","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:45:33.060740","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:46:03.744737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:46:34.570878","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:47:05.734918","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:47:36.222941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:48:07.899043","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:48:39.110431","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:49:09.757468","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:49:40.732726","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:50:11.512724","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:50:42.673054","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:51:13.389675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:51:44.093341","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:52:15.034827","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:52:46.039375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:53:16.728040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:53:47.959481","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:54:19.993397","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:54:50.851805","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:55:21.855710","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:55:52.575213","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:56:23.504261","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:56:54.275586","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:57:25.185161","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:57:56.362777","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:58:27.093703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:58:57.686675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:59:28.387593","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:59:59.304082","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:00:30.142484","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:01:00.762356","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:01:31.591255","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:02:02.323089","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:02:32.816821","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:03:03.584115","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:03:34.341637","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:04:07.402906","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:04:40.455419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:05:11.641871","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:05:43.013589","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:06:20.465540","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:06:51.886351","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:07:24.413294","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:07:55.356336","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:08:26.150228","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:09:00.237983","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:09:30.702923","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:10:03.854374","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:10:35.139296","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:11:06.171230","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:11:36.799612","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:12:07.208939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:12:38.038241","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:13:09.689771","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:13:40.586893","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:14:11.334158","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:14:42.166610","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:15:12.547361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:15:43.537200","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:16:14.695445","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:16:45.665850","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:17:16.466051","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
