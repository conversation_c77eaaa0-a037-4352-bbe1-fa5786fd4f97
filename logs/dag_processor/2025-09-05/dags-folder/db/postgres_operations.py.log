{"timestamp":"2025-09-05T07:00:03.161606","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:00:33.230164","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:01:04.464541","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:01:35.467735","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:02:06.793380","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:02:37.570443","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:03:12.740620","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:03:43.802700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:04:15.015425","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:04:46.225054","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:05:17.316027","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:05:48.731792","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:06:19.642688","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:06:51.241055","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:07:22.330046","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:07:53.717809","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:08:24.710329","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:08:55.914393","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:09:26.927325","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:09:58.150532","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:10:29.141302","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:11:00.128095","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:11:31.344196","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:12:02.631089","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:12:33.777963","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:13:05.003179","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:13:36.093293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:14:07.324684","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:14:38.489141","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:15:10.016920","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:15:41.238550","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:16:12.208804","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:16:43.592986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:17:14.887994","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:17:46.105919","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:18:17.423080","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:18:48.461154","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:19:19.948614","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:19:51.022401","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:20:22.312358","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:20:53.822526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:21:24.200679","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:21:55.582804","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:22:26.812004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:22:58.057270","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:23:29.372874","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:24:00.245109","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:24:30.602620","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:25:02.402791","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:25:33.748124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:26:05.008238","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:26:36.481573","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:27:07.516534","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:27:39.005688","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:28:10.916529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:28:41.857648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:29:13.314762","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:29:44.106564","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:30:15.337432","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:30:46.766511","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:31:18.137773","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:31:49.462708","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:32:20.504808","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:32:51.985875","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:33:23.196257","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:33:54.775016","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:34:26.259245","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:34:57.510263","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:35:28.758884","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:36:00.076350","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:36:31.389862","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:37:02.704109","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:37:33.709566","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:38:04.746326","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:38:34.430011","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:39:05.945103","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:39:37.119246","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:40:08.352298","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:40:39.732101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:41:10.791331","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:41:42.217839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:42:13.470031","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:42:44.762561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:43:16.072726","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:44:05.509395","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:44:36.746473","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:45:08.039517","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:45:39.182683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:46:10.414737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:46:41.356936","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:47:12.453776","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:47:43.512518","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:48:14.753425","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:48:45.820796","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:49:17.292810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:49:48.411057","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:50:19.078018","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:50:50.177246","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:51:21.395599","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:51:52.503055","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:52:23.520373","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:52:54.851547","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:53:26.027108","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:53:57.093234","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:54:28.293920","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:54:59.283655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:55:30.431942","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:56:01.534353","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:56:32.621826","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:57:03.791086","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:57:35.162851","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:58:06.236217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:58:37.632695","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:59:08.774429","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:59:39.915966","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:00:10.979933","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:00:42.089366","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:01:13.467882","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:01:44.551018","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:02:15.713206","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:02:46.869180","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:03:18.114863","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:03:49.240613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:04:19.901018","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:04:51.493948","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:05:22.086970","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:05:53.518897","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:06:25.085671","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:06:55.465732","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:07:26.855449","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:07:58.113076","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:08:29.304733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:09:00.655084","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:09:32.033457","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:10:03.400419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:10:34.627837","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:11:04.962965","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:11:35.387659","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:12:05.441764","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:12:35.776815","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:13:06.098199","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:13:36.393100","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:14:06.904541","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:14:37.712772","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:15:09.074447","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:15:40.281095","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:16:10.859018","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:16:41.428591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:17:12.126545","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:17:42.717470","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:32:57.089963","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:33:28.361665","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:33:59.472468","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:34:30.745162","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:35:02.022021","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:35:33.227979","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:36:04.400541","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:36:35.720362","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:37:07.094754","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:37:38.276449","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:38:09.608278","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:38:40.710366","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:39:12.117259","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:39:43.605980","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:40:15.101946","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:40:46.605572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:41:18.067615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:41:49.755458","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:42:21.127228","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:42:52.455964","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:43:23.675093","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:43:55.051294","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:44:26.725939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:44:58.084135","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:45:29.388285","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:46:00.800920","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:46:31.677666","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:47:03.048026","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:47:34.423675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:48:06.055844","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:48:37.382121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:49:08.810598","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:49:41.141847","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:50:12.510635","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:50:43.885069","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:51:15.136434","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:51:46.562992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:52:17.936147","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:52:49.381925","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:53:19.811286","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:53:51.496692","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:54:22.769105","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:54:53.975836","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:55:25.268445","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:55:56.463628","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:56:27.636303","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:56:58.891902","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:57:30.154402","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:58:01.418202","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:58:32.757068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:59:03.921439","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:59:35.202161","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:00:06.493067","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:00:37.015751","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:01:07.419603","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:01:37.812967","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:02:08.261975","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:02:39.613288","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:03:10.092644","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:03:40.359026","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:04:11.949881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:04:42.260268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:05:12.332984","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:05:42.686297","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:06:12.978920","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:06:44.463778","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:07:14.859505","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:07:46.062168","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:08:16.605959","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:08:47.994554","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:09:18.433745","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:09:49.148415","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:10:20.118443","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:10:51.270150","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:11:21.773951","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:11:52.204968","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:12:22.672548","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:12:53.235023","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:13:23.496114","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:13:54.838703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:14:25.318786","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:14:55.969518","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:15:26.684285","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:15:57.203826","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:16:27.973712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:16:58.890919","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:17:29.413293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:17:59.964557","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:18:30.415696","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:19:01.059981","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:19:31.711596","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:20:02.343370","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:20:32.984675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:21:03.672642","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:21:34.371536","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:22:05.154570","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:22:35.986198","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:23:06.879569","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:23:37.283532","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:24:07.856730","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:24:38.220535","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:25:08.794887","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:25:39.356871","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:26:09.765144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:26:40.370143","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:27:11.217818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:27:41.790717","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:28:12.545327","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:28:43.280148","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:29:13.816661","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:29:44.753264","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:30:15.262198","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:30:46.166748","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:31:16.584145","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:31:48.052118","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:32:18.741525","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:32:49.404427","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:33:20.105394","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:33:50.908372","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:34:21.322301","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:34:52.016914","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:35:22.413755","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:35:52.955058","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:36:23.530382","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:36:54.430475","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:37:24.702116","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:37:55.360339","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:38:25.999856","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:38:56.630558","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:39:27.025019","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:39:57.896800","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:40:28.279626","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:40:58.925999","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:41:29.519522","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:42:00.185487","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:42:31.255591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:43:01.832953","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:43:32.271536","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:44:02.660398","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:44:33.216523","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:45:04.107339","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:45:34.597166","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:46:04.917347","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:46:35.140001","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:47:05.756118","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:47:35.966258","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:48:06.630779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:48:36.958204","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:49:07.706897","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:49:38.198111","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:50:08.902969","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:50:39.423886","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:51:09.708144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:51:40.158270","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:52:11.079989","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:52:41.371665","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:53:11.832314","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:53:42.379539","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:54:12.983466","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:54:43.555165","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:55:13.804752","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:55:44.376341","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:56:14.736833","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:56:45.460511","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:57:15.819268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:57:46.875926","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:58:17.566334","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:58:48.003563","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:59:18.621090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:59:49.338989","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:00:19.476922","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:00:50.148944","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:01:20.801073","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:01:51.738701","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:02:22.513817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:02:53.299083","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:03:24.092232","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:03:54.598269","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:04:25.262981","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:04:55.853138","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:05:26.499040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:05:57.295952","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:06:27.787664","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:06:58.765986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:07:29.222255","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:07:59.911263","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:08:30.457068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:09:01.010463","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:09:31.703198","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:10:02.422683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:10:33.029471","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:11:04.018456","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:11:34.777827","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:12:05.316000","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:12:35.958770","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:13:06.395450","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:13:37.205612","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:14:07.752207","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:14:38.456614","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:15:08.991704","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:15:39.866327","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:16:10.444915","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:16:40.587611","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:17:11.346018","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:17:42.296549","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:18:12.604814","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:18:43.398163","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:19:13.885955","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:19:44.884819","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:20:15.335009","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:20:45.482552","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:21:17.386285","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:21:48.162490","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:22:18.559495","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:22:49.660930","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:23:20.387368","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:23:50.442510","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:24:22.354537","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:24:52.744379","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:25:22.842572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:25:54.702025","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:26:25.838940","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:26:56.345015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:27:26.592366","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:27:57.215347","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:28:28.760493","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:28:59.323876","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:29:30.314021","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:30:00.869873","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:30:31.384204","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:31:01.494827","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:31:33.161618","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:32:03.501916","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:32:34.513352","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:33:04.969457","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:33:35.516393","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:34:06.210401","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:34:36.707317","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:35:07.390559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:35:38.255814","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:36:09.347886","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:36:39.916621","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:37:10.442674","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:37:40.903094","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:38:12.286544","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:38:42.719379","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:39:13.382029","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:39:43.737526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:40:14.535081","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:40:44.945364","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:41:15.578322","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:41:45.931839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:42:17.139470","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:42:47.809398","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:43:17.946668","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:43:48.714365","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:44:19.182870","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:44:49.744364","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:45:20.582495","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:45:51.292382","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:46:21.948503","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:46:52.427012","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:47:23.514577","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:47:54.343832","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:08:51.790173","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:09:23.189738","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:09:53.483084","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:10:23.891598","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:10:55.270292","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:11:26.625897","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:11:57.770483","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:12:28.119335","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:12:59.822150","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:13:29.939091","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:14:00.324616","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:14:31.868164","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:15:03.446898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:15:34.791474","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:16:05.940911","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:16:36.411329","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:17:07.773071","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:17:39.038723","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:18:09.899693","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:18:40.546643","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:19:12.237963","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:19:42.356043","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:20:12.790784","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:20:43.608272","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:21:13.893527","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:21:44.635919","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:22:15.007291","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:22:45.569443","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:23:15.876205","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:23:46.720018","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:24:17.189607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:24:47.566425","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:25:18.894087","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:25:50.047470","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:26:20.464800","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:26:51.864404","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:27:23.232589","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:51:17.483786","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:51:47.743220","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:52:19.384019","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:52:49.862067","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:53:21.147974","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:53:52.418379","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:54:22.608706","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:54:53.758125","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:55:24.814682","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:55:56.041944","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:56:27.470354","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:56:58.802280","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:57:30.225355","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:58:01.343768","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:58:31.634432","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:59:03.264345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:59:33.445601","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:00:05.103462","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:00:35.677554","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:01:07.021732","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:10:25.074988","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:10:55.380471","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:11:27.012448","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:11:58.521546","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:12:28.826533","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:12:59.793854","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:13:31.351230","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:14:02.878277","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:14:34.195568","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:15:04.782469","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:15:35.544201","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:16:06.046246","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:16:37.423220","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:17:08.290245","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:17:38.800146","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:18:09.329156","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:18:39.792106","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:19:10.242374","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:19:40.665629","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:20:11.401726","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:20:41.800885","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:21:12.380701","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:21:42.793309","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:22:14.006598","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:22:45.261841","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:23:15.740435","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:23:51.183634","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:24:23.606943","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:24:54.587583","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:25:25.520770","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:25:56.402268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:26:27.193401","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:26:57.618045","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:27:29.533885","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:28:00.155973","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:28:31.129085","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:29:01.686557","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:29:32.670409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:30:03.191576","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:46:33.005811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:47:03.592244","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:47:33.922285","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:48:05.391511","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:48:36.411925","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:49:06.870480","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:49:37.264742","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:50:07.741229","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:50:38.504698","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:51:09.001886","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:51:40.290251","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:52:11.535476","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:52:42.562542","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:53:12.858615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:53:43.653167","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:54:14.720317","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:54:45.441765","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:55:15.955651","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:55:46.533563","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:56:17.201966","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:56:48.029279","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:57:18.337528","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:57:48.851270","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:58:19.369879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:58:49.746988","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:59:20.194890","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:59:50.750662","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:00:21.011067","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:00:52.389663","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:01:23.282817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:01:54.316694","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:02:36.650921","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:03:07.165958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:03:38.152645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:04:09.612771","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:04:39.734218","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:05:11.633046","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:05:42.107281","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:06:12.949810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:06:43.986928","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:07:14.849358","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:07:45.050204","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:08:15.739665","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:08:46.466565","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:50:32.282712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:51:02.981474","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:51:34.495063","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:52:06.037425","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:52:37.248233","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:53:08.806097","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:53:40.092142","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:54:11.243455","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:54:42.027234","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:55:13.485957","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:55:44.934294","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:56:16.091066","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:56:47.676943","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:57:19.084314","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:57:50.654724","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:58:21.485064","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:58:52.512219","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:59:22.848803","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:59:53.596502","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:00:24.970334","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:00:55.632883","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:01:26.713945","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:01:57.526918","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:02:28.983536","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:02:59.911747","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:03:31.709510","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:04:02.340252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:04:33.698673","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:05:05.037245","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:05:36.718738","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:06:07.202486","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:06:38.083119","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:07:08.866566","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:07:39.525547","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:08:10.570906","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:08:42.243810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:09:12.811118","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:09:44.811255","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:10:15.759513","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:10:46.573303","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:11:17.474525","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:11:47.841982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:12:19.412518","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:12:50.822792","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:13:21.617016","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:13:53.206006","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:14:24.178077","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:14:55.886957","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:15:26.475481","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:15:57.607860","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:16:29.482323","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:17:00.018588","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:17:31.787658","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:18:03.668837","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:18:35.088458","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:19:06.941382","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:19:37.781534","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:20:09.180618","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:20:40.031729","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:21:10.942951","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:21:41.431192","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:22:12.251116","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:22:44.076069","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:23:15.523026","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:23:47.852572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:24:18.642106","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:24:50.481535","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:25:21.186394","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:25:52.658792","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:26:23.267737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:26:54.150651","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:27:24.818472","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:27:55.817400","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:28:26.327649","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:28:57.003159","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:29:28.578419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:29:59.105890","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:30:30.045702","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:31:00.936345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:31:31.369935","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:32:02.427987","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:32:32.872243","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:33:03.329039","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:33:33.829571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:34:05.961652","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:34:37.873624","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:49:35.659663","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:50:05.898497","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:50:36.503739","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:51:08.133938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:51:38.541332","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:52:09.333836","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:52:40.105703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:53:10.284518","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:53:40.847347","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:54:12.690820","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:54:44.201801","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:55:14.472960","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:55:46.097313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:56:17.698537","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:56:48.333100","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:57:18.391779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:57:49.112555","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:58:19.691692","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:58:50.025706","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:59:21.701969","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:59:52.374388","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:00:22.739674","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:00:54.430329","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:01:25.020110","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:01:55.296255","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:02:25.895079","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:02:57.451356","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:03:28.962016","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:03:59.238733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:04:30.394951","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:05:00.938858","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:05:32.120236","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:06:02.693559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:06:34.250549","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:07:05.742246","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:07:36.696439","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:08:08.214353","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:08:39.615121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:09:11.084887","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:09:41.800831","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:11:09.609733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:11:40.028435","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:12:11.333670","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:12:42.874749","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:13:13.275286","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:13:43.892767","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:14:15.605306","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:14:47.461907","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:15:17.815258","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:15:49.422568","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:16:21.266807","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:16:52.602577","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:17:24.021182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:17:55.698166","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:18:26.523387","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:18:58.171869","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:19:29.836816","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:20:01.101044","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:20:31.990560","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:21:02.789508","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:21:33.324584","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:22:04.991917","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:22:35.844391","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:23:07.188195","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:23:37.900082","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:24:09.316976","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:24:40.293653","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:25:10.994409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:25:42.465366","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:26:14.187208","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:26:45.238280","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:27:16.664102","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:27:48.450570","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:28:20.289360","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:28:51.754406","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:29:23.559242","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:29:54.668801","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:30:26.185249","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:30:57.955591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:31:29.682393","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:32:01.062905","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:32:32.747454","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:33:04.161843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:33:35.959356","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:34:06.567086","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:34:36.993679","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:35:08.187434","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:35:40.103845","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:36:10.807539","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:36:42.279108","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:37:14.077904","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:37:44.927529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:38:15.551237","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:38:47.161402","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:39:18.792023","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:39:49.280720","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:40:20.012590","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:40:50.784472","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:41:21.562767","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:41:53.413174","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:42:24.138006","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:42:54.898851","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:43:25.745608","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:43:56.511880","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:44:27.141690","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:44:58.165045","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:45:30.141479","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:46:00.893386","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:46:31.416578","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:47:02.227802","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:47:32.758764","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:48:03.015880","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:48:33.847261","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:49:04.708748","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:49:35.212608","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:50:05.864409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:50:36.382608","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:51:06.694896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:51:37.450843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:52:09.284394","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:52:39.914177","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:53:10.645055","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:53:41.596124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:54:12.267437","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:54:43.079160","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:55:13.667848","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:55:44.294055","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:56:14.822662","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:56:45.428224","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:57:15.836587","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:57:46.529968","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:58:17.132191","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:58:47.579529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:59:18.790119","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:59:49.988998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:00:20.717941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:00:51.518049","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:01:22.325444","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:01:53.202467","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:02:23.956096","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:02:54.677110","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:03:25.156247","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:03:56.187520","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:04:26.889163","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:04:57.315117","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:05:28.197091","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:05:58.991610","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:06:29.990483","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:07:00.579872","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:07:31.475316","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:08:01.947469","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:08:32.027042","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:09:03.957409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:09:34.955626","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:10:05.452077","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:10:36.564845","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:11:07.277726","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:11:37.753800","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:12:08.515081","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:12:39.291283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:13:09.742000","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:13:39.857769","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:14:10.742369","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:14:41.518769","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:15:12.624635","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:15:43.950290","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:16:14.530280","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:16:45.477979","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:17:16.194411","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:17:46.778098","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:18:17.609321","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:18:48.475764","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:19:19.018185","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:19:49.962464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:20:20.585639","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:20:51.606700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:21:23.443659","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:21:53.537524","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:22:24.220391","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:22:55.389349","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:23:26.439872","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:23:56.991094","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:24:27.893309","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:24:59.012402","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:25:29.908187","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:26:00.889290","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:26:31.824590","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:27:02.344249","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:27:33.678353","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:28:04.642218","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:28:35.075711","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:29:06.172263","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:29:37.157992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:30:07.798473","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:30:39.048260","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:31:09.844294","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:31:40.452140","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:32:11.238119","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:32:43.368292","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:33:14.019008","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:33:45.008843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:34:15.565143","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:34:46.470781","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:35:16.636068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:35:47.255246","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:36:18.712830","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:36:49.721754","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:37:20.719723","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:37:50.969542","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:38:21.999206","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:38:52.628341","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:39:23.448479","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:39:54.301505","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:40:25.050238","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:40:56.135697","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:41:26.946750","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:41:57.474049","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:42:28.399143","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:42:59.246824","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:43:29.919936","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:44:00.231875","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:44:32.189560","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:45:02.301310","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:45:33.178465","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:46:03.858185","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:46:34.687721","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:47:05.884730","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:47:36.334741","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:48:08.021020","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:48:39.301508","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:49:09.895626","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:49:40.884826","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:50:11.675075","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:50:42.787658","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:51:14.612979","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:51:45.274373","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:52:16.208294","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:52:47.209601","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:53:18.023429","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:53:49.143482","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:54:20.102442","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:54:52.011236","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:55:22.090975","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:55:52.862560","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:56:23.808670","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:56:54.914948","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:57:25.474924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:57:56.669549","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:58:27.608784","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:58:58.234119","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:59:29.272045","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:00:00.102228","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:00:30.485858","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:01:01.582843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:01:32.339543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:02:02.774413","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:02:33.610445","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:03:04.342115","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:03:34.969183","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:04:07.747180","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:04:40.629547","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:05:12.436171","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:05:43.130885","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:06:20.606217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:06:52.010800","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:07:24.556172","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:07:55.897592","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:08:26.469695","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:09:00.997893","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:09:31.455097","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:10:04.173821","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:10:36.099210","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:11:06.543348","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:11:37.100928","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:12:07.741886","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:12:38.671808","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:13:09.979270","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:13:41.189818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:14:11.634959","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:14:42.554892","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:15:13.241132","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:15:43.935199","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:16:15.046388","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:16:46.360658","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:17:16.851891","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
