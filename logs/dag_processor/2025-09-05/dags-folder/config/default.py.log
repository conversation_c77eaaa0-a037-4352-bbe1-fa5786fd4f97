{"timestamp":"2025-09-05T07:08:14.021630","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:08:45.063316","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:09:15.154237","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:09:45.237188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:10:15.355988","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:10:46.312971","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:11:16.396316","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:11:46.598082","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:12:16.829253","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:12:46.963300","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:13:17.270192","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:13:48.284157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:14:18.543868","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:14:48.759293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:15:19.197461","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:15:49.391966","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:16:20.429114","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:16:50.797801","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:17:21.057076","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:17:51.227028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:18:21.557101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:18:51.684770","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:19:22.054394","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:19:52.167817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:20:22.357193","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:20:53.871685","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:21:24.104133","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:21:54.960294","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:22:26.166897","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:22:57.279947","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:23:27.657101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:23:58.086876","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:24:29.040278","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:24:59.299016","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:25:29.981023","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:26:00.260675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:26:30.570692","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:27:00.926815","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:27:31.032713","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:28:01.504614","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:28:31.885321","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:29:02.347923","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:29:33.241982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:30:03.427259","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:30:33.692722","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:31:03.979575","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:31:34.381817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:32:04.738962","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:32:35.810368","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:33:06.273310","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:33:36.498412","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:34:06.959298","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:34:37.520741","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:35:07.729314","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:35:37.960511","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:36:08.281408","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:36:38.497923","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:37:09.060053","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:37:39.916565","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:38:31.830383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:39:03.980816","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:39:34.389372","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:40:04.471953","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:40:34.762400","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:41:05.224306","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:41:35.358412","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:42:05.762083","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:42:36.028967","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:43:06.288255","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:44:03.392915","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:44:35.116796","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:45:05.191705","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:45:35.417428","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:46:05.568198","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:46:35.802101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:47:06.709689","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:47:36.799928","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:48:06.950046","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:48:37.127167","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:49:07.258589","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:49:37.721036","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:50:08.780752","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:50:39.427261","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:51:10.490507","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:51:40.721837","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:52:10.807661","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:52:41.787772","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:53:12.150395","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:53:42.307990","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:54:13.354033","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:54:43.530576","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:55:14.575313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:55:44.678110","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:56:14.735000","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:56:44.856968","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:57:15.041780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:57:45.360506","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:58:15.433163","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:58:45.831794","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:59:15.963986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:59:46.076344","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:00:17.146312","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:00:47.242912","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:01:17.684353","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:01:48.690152","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:02:18.826865","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:02:48.984983","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:03:19.212913","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:03:49.276287","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:04:19.939134","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:04:50.576832","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:05:21.343379","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:05:51.774931","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:06:22.157168","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:06:52.769329","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:07:23.050615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:07:53.392505","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:08:23.597635","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:08:53.812983","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:09:24.188844","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:09:54.499329","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:10:24.786092","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:10:55.038980","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:11:25.416652","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:11:55.820778","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:12:26.178192","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:12:56.645464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:13:26.895710","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:13:57.048740","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:14:27.780141","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:14:58.407360","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:15:28.720131","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:15:59.158368","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:16:29.838133","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:17:00.173949","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:17:30.903336","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:32:56.086779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:33:26.616442","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:33:56.865196","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:34:27.013631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:34:57.258986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:35:27.459574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:35:57.597516","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:36:27.852764","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:36:58.199169","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:37:28.468823","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:37:58.747241","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:38:28.994259","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:38:59.116875","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:39:29.483618","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:39:59.967417","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:40:30.410683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:41:00.991965","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:41:31.380069","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:42:02.033833","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:42:32.403559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:43:02.701419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:43:32.885171","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:44:03.262367","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:44:33.970063","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:45:04.260007","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:45:34.513383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:46:04.958140","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:46:35.858472","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:47:06.185061","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:47:36.566391","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:48:07.185223","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:48:37.429678","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:49:08.068410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:49:39.383889","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:50:09.650276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:50:40.024369","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:51:10.338818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:51:40.624973","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:52:11.115650","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:52:41.335889","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:53:11.872648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:53:42.210736","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:54:12.875748","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:54:43.155901","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:55:13.360551","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:55:43.599656","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:56:13.886531","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:56:44.002485","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:57:14.212637","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:57:44.420395","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:58:14.685884","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:58:45.088332","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:59:16.177489","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T08:59:46.401792","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:00:16.702551","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:00:47.253564","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:01:17.697804","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:01:48.107146","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:02:18.514223","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:02:48.926464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:03:19.408318","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:03:49.576590","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:04:20.177407","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:04:50.548405","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:05:20.919633","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:05:51.232117","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:06:21.549085","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:06:51.891455","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:07:22.248337","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:07:52.616966","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:08:23.162549","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:08:53.348277","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:09:23.745971","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:09:54.583414","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:10:25.340077","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:10:55.763355","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:11:26.192182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:11:56.529187","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:12:26.987014","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:12:57.510471","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:13:27.937364","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:13:58.238704","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:14:28.656203","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:14:59.383437","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:15:30.088234","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:16:00.709039","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:16:31.317662","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:17:02.214974","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:17:32.792504","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:18:03.317216","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:18:33.949375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:19:04.623673","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:19:35.045662","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:20:05.707855","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:20:36.630120","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:21:07.257541","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:21:37.913119","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:22:08.651253","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:22:39.378928","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:23:10.236036","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:23:40.840916","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:24:11.217972","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:24:41.548010","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:25:12.118537","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:25:42.731301","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:26:13.343631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:26:43.811183","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:27:14.517843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:27:45.144392","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:28:16.204517","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:28:46.761722","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:29:17.182775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:29:48.143273","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:30:18.783182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:30:49.528199","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:31:19.676829","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:31:50.553281","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:32:21.116636","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:32:51.733452","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:33:22.754234","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:33:53.430348","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:34:23.691888","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:34:54.337447","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:35:24.769865","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:35:55.499379","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:36:26.063393","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:36:57.058369","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:37:28.084809","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:37:58.740442","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:38:29.660044","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:39:00.057351","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:39:30.317529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:40:01.238055","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:40:31.641191","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:41:02.571261","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:41:33.000615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:42:03.713817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:42:33.792535","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:43:04.125516","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:43:34.766718","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:44:05.238362","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:44:35.528767","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:45:06.523587","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:45:36.955131","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:46:07.226143","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:46:37.666403","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:47:07.946249","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:47:38.129252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:48:08.797574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:48:39.131269","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:49:09.932820","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:49:40.550979","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:50:11.066943","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:50:41.601657","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:51:11.883872","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:51:42.374642","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:52:13.242491","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:52:43.555532","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:53:14.015309","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:53:44.538482","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:54:15.154816","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:54:45.919446","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:55:16.007501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:55:46.519198","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:56:16.990183","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:56:47.624484","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:57:18.011759","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:57:49.099584","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:58:19.708036","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:58:50.196745","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:59:20.842826","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:59:51.513529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:00:21.630291","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:00:52.312429","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:01:23.003491","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:01:53.919516","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:02:24.703802","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:02:55.466611","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:03:26.253611","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:03:56.789209","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:04:27.462149","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:04:58.049271","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:05:28.672591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:05:59.506963","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:06:29.977370","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:07:00.950498","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:07:31.413761","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:08:02.141710","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:08:32.624070","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:09:03.195628","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:09:33.901046","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:10:04.602302","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:10:35.647335","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:11:06.338568","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:11:36.988605","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:12:07.482756","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:12:38.123052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:13:08.544530","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:13:39.384779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:14:09.990437","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:14:40.642600","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:15:11.174139","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:15:42.049505","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:16:12.639506","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:16:42.792112","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:17:13.580014","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:17:44.778369","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:18:15.802738","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:18:46.610468","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:19:17.122239","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:19:48.089143","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:20:18.569968","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:20:48.676864","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:21:19.632143","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:21:50.353184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:22:20.747018","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:22:50.841982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:23:21.485214","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:23:52.536977","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:24:23.447591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:24:53.831215","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:25:23.924214","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:25:54.695385","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:26:24.746313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:26:54.867345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:27:25.261896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:27:55.830687","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:28:26.367135","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:28:56.755319","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:29:27.538168","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:29:58.497870","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:30:29.041740","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:30:59.690585","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:31:30.557072","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:32:01.130023","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:32:32.121607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:33:02.604310","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:33:33.005325","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:34:03.601638","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:34:34.258989","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:35:05.019645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:35:35.633734","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:36:06.624356","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:36:37.534091","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:37:08.121891","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:37:39.062293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:38:09.511901","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:38:40.401881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:39:11.082121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:39:41.459408","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:40:12.009393","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:40:42.468824","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:41:12.970856","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:41:43.637083","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:42:13.837489","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:42:44.427807","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:43:14.644926","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:43:45.383252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:44:15.821378","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:44:46.358188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:45:16.789924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:45:47.823014","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:46:18.626984","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:46:49.055211","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:47:19.954309","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T10:47:50.743714","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:08:51.291275","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:09:21.624937","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:09:52.136861","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:10:23.581874","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:10:54.799536","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:11:25.110787","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:11:56.396349","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:12:27.822232","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:12:59.189177","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:13:29.801217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:14:00.156475","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:14:31.553138","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:15:02.827138","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:15:33.267569","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:16:03.597581","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:16:35.049339","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:17:06.162223","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:17:36.549925","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:18:06.866602","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:18:38.003413","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:19:08.324899","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:19:38.785326","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:20:09.339241","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:20:39.823941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:21:10.458545","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:21:41.127297","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:22:11.506265","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:22:41.853840","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:23:12.415818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:23:43.318540","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:24:13.771026","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:24:43.975826","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:25:14.315547","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:25:44.638699","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:26:14.991930","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:26:45.225276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:27:15.608482","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:51:16.941322","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:51:47.370189","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:52:18.634680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:52:49.444479","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:53:19.929439","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:53:51.195228","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:54:22.484694","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:54:53.640435","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:55:24.698583","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:55:55.888813","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:56:27.339286","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:56:58.532243","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:57:29.712095","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:57:59.994227","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:58:31.290709","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:59:02.676040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T11:59:33.306014","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:00:04.763644","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:00:35.370431","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:01:06.470582","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:10:24.514174","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:10:55.042505","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:11:26.651055","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:11:57.995924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:12:28.473099","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:12:59.376544","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:13:29.528885","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:13:59.995284","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:14:30.435662","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:15:01.247773","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:15:32.048401","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:16:02.381980","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:16:32.848599","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:17:03.698270","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:17:34.246819","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:18:04.601130","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:18:35.180241","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:19:05.698780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:19:36.119542","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:20:06.599733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:20:37.260606","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:21:07.824499","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:21:38.187775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:22:08.822302","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:22:39.734113","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:23:10.171812","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:23:50.369714","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:24:20.846109","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:24:53.966051","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:25:25.080861","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:25:55.505961","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:26:26.484287","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:26:57.232865","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:27:27.908744","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:27:58.455173","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:28:29.434163","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:29:00.228457","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:29:31.185524","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:30:01.610448","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:30:32.457774","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:46:32.762359","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:47:03.536521","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:47:33.693073","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:48:05.147750","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:48:35.962207","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:49:06.442402","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:49:37.003850","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:50:07.134733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:50:37.851472","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:51:08.941438","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:51:39.989803","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:52:11.198899","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:52:42.047996","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:53:12.601219","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:53:43.329496","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:54:14.216876","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:54:44.949336","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:55:15.690142","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:55:46.282408","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:56:16.634978","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:56:47.517292","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:57:18.014643","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:57:48.582408","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:58:18.877313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:58:49.486338","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:59:19.606555","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T13:59:50.025091","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:00:20.942225","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:00:52.268717","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:01:23.234955","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:01:53.775462","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:02:31.481064","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:03:01.885850","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:03:33.131562","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:04:03.929018","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:04:34.510267","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:05:05.691295","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:05:36.507565","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:06:07.034826","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:06:37.942231","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:07:08.979419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:07:39.421087","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:08:09.809157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:08:40.601184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:50:31.927983","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:51:02.609845","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:51:32.748515","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:52:03.080131","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:52:33.586169","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:53:03.938787","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:53:34.314713","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:54:04.603234","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:54:34.838073","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:55:05.586808","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:55:35.894289","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:56:06.395527","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:56:36.578597","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:57:06.996484","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:57:37.631968","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:58:08.207022","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:58:39.000327","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:59:09.932740","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T14:59:40.296322","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:00:11.024382","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:00:41.521407","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:01:12.002604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:01:42.132891","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:02:12.888336","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:02:43.379212","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:03:14.302774","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:03:45.098253","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:04:15.860998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:04:46.216944","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:05:17.342657","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:05:48.059114","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:06:18.547927","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:06:49.494172","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:07:20.193714","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:07:51.106313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:08:21.890844","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:08:52.636998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:09:23.238295","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:09:54.210962","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:10:25.080354","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:10:55.965390","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:11:26.961840","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:11:57.244510","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:12:27.676435","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:12:58.067065","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:13:28.880162","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:13:59.471829","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:14:30.442710","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:15:01.178953","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:15:31.729650","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:16:02.831651","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:16:33.718853","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:17:04.206072","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:17:34.949983","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:18:05.826079","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:18:36.239573","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:19:07.021572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:19:37.753700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:20:08.551661","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:20:38.961546","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:21:10.043343","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:21:40.729818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:22:11.294131","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:22:42.006370","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:23:12.772419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:23:43.191551","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:24:13.611338","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:24:44.287832","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:25:15.014682","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:25:45.904247","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:26:16.279477","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:26:47.025843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:27:17.747907","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:27:48.589855","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:28:19.414194","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:28:50.042403","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:29:20.755821","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:29:51.186232","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:30:21.813523","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:30:52.749236","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:31:23.712285","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:31:53.864843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:32:24.107014","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:32:54.576046","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:33:25.255706","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:33:55.598552","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:34:26.465222","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:49:35.157386","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:50:05.566862","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:50:36.170146","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:51:07.510838","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:51:38.165506","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:52:08.972620","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:52:39.344894","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:53:10.110252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:53:40.697225","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:54:12.368779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:54:43.610305","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:55:14.148806","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:55:45.767848","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:56:17.055517","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:56:47.691502","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:57:17.905964","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:57:48.261156","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:58:18.857124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:58:49.701335","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:59:21.351484","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T15:59:51.776738","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:00:22.392263","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:00:54.062481","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:01:24.405250","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:01:54.915159","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:02:25.659681","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:02:57.191392","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:03:28.326923","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:03:58.850374","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:04:30.004114","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:05:00.329601","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:05:30.752437","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:06:02.307509","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:06:32.428589","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:07:03.926393","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:07:34.822480","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:08:05.377531","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:08:35.825226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:09:06.389038","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:09:36.845565","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:11:09.020242","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:11:39.672557","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:12:11.025490","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:12:42.225060","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:13:12.951906","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:13:43.552953","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:14:13.706515","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:14:44.219361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:15:15.055274","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:15:45.408330","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:16:15.966277","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:16:46.864016","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:17:17.125273","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:17:47.647957","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:18:18.762048","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:18:49.104694","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:19:19.747980","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:19:50.433521","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:20:20.785088","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:20:51.590564","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:21:22.431792","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:21:52.818778","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:22:23.478262","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:22:54.266620","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:23:24.688838","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:23:55.385384","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:24:25.947079","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:24:56.868686","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:25:27.511145","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:25:57.869954","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:26:28.655861","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:26:59.624094","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:27:29.999755","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:28:00.758977","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:28:31.581150","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:29:02.069519","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:29:32.891960","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:30:03.945698","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:30:34.462221","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:31:05.241208","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:31:35.888371","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:32:06.289233","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:32:36.942238","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:33:07.332076","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:33:38.129663","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:34:08.869188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:34:39.842278","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:35:10.401033","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:35:41.293025","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:36:11.905927","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:36:42.346800","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:37:13.068683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:37:43.853023","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:38:14.978310","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:38:45.335986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:39:15.823601","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:39:46.536334","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:40:16.950994","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:40:47.830910","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:41:18.623051","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:41:49.389291","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:42:20.096662","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:42:50.900951","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:43:21.694109","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:43:52.486793","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:44:23.398341","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:44:54.122338","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:45:24.872952","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:45:55.769963","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:46:26.540622","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:46:57.128804","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:47:27.969685","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:47:58.771584","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:48:29.703086","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:49:00.678498","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:49:31.508522","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:50:01.929023","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:50:32.525938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:51:03.163109","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:51:33.603626","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:52:04.194039","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:52:34.943709","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:53:05.568171","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:53:36.397962","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:54:07.358273","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:54:37.978345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:55:08.749475","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:55:39.437535","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:56:09.903096","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:56:40.503684","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:57:11.029625","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:57:41.493696","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:58:12.186859","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:58:42.801692","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:59:13.400132","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T16:59:44.630122","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:00:14.806913","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:00:45.352020","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:01:16.141753","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:01:46.944426","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:02:17.867007","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:02:48.670740","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:03:19.261915","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:03:49.840922","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:04:20.820015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:04:51.526634","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:05:22.142027","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:05:52.944012","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:06:23.826986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:06:54.866747","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:07:25.289379","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:07:56.176926","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:08:26.869927","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:08:57.900073","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:09:28.790613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:09:59.666231","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:10:30.212052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:11:01.261035","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:11:31.927733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:12:02.461649","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:12:33.235974","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:13:03.970112","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:13:34.404965","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:14:04.690136","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:14:35.650985","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:15:06.144502","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:15:37.283658","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:16:07.567635","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:16:38.151371","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:17:09.088320","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:17:39.863851","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:18:10.376300","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:18:41.254196","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:19:12.117950","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:19:42.723305","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:20:13.630773","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:20:43.984751","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:21:14.231360","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:21:45.061986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:22:16.141259","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:22:46.860918","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:23:17.947973","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:23:49.035211","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:24:19.617021","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:24:50.563742","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:25:21.024338","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:25:51.505987","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:26:22.524867","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:26:53.422158","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:27:23.917857","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:27:54.295403","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:28:25.123177","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:28:55.630615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:29:26.682993","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:29:57.782796","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:30:28.454335","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:30:58.614599","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:31:29.480130","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:31:59.963547","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:32:30.763607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:33:00.978488","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:33:31.572755","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:34:02.508519","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:34:33.647001","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:35:05.121969","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:35:35.293362","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:36:06.128845","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:36:37.195444","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:37:08.246723","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:37:38.558842","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:38:09.536244","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:38:40.629410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:39:11.422807","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:39:41.993610","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:40:12.891686","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:40:43.860924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:41:14.655772","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:41:45.520929","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:42:15.972721","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:42:46.995017","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:43:17.855781","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:43:48.510941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:44:18.868622","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:44:49.932274","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:45:20.780916","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:45:51.879978","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:46:22.454951","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:46:53.390165","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:47:24.382877","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:47:54.811042","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:48:25.554057","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:48:55.790245","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:49:26.414270","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:49:57.441151","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:50:28.254578","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:50:59.344875","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:51:30.076112","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:52:00.878055","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:52:31.675528","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:53:02.722898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:53:33.659065","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:54:04.672615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:54:35.721126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:55:06.436047","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:55:36.633846","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:56:07.302763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:56:38.273579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:57:09.380191","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:57:40.017769","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:58:11.137411","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:58:42.193812","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:59:12.703874","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T17:59:43.795186","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:00:14.622264","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:00:45.159385","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:01:16.042496","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:01:46.838201","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:02:17.227477","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:02:48.085817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:03:18.806611","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:03:49.554816","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:04:20.169326","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:04:51.381771","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:05:22.005595","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:05:53.137774","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:06:24.063965","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:06:54.487025","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:07:24.641813","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:07:55.000498","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:08:25.874004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:08:56.998722","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:09:29.540653","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:10:00.398447","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:10:31.345773","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:11:01.752226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:11:32.109847","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:12:02.726776","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:12:33.431749","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:13:04.627144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:13:35.674543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:14:06.850989","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:14:37.207840","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:15:08.122139","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:15:39.032745","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:16:09.688927","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:16:40.908416","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T18:17:12.048250","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
