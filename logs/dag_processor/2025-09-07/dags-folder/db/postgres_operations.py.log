{"timestamp":"2025-09-07T04:56:55.361624","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:57:26.023644","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:57:56.088358","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:58:26.617210","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:58:57.441053","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:59:28.193878","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:59:58.981054","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:00:29.560551","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:01:00.168010","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:01:30.403246","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:02:01.040428","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:02:32.639721","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:03:03.162970","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:03:33.483055","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:04:03.883891","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:04:34.498962","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:05:04.932939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:05:36.109152","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:06:06.488097","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:06:36.548554","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:07:07.570151","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:07:37.910383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:08:08.516472","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:08:40.143617","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:09:10.572510","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:09:41.326341","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:10:13.104521","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:10:43.897298","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:11:14.655441","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:11:45.418122","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:12:16.377983","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:12:47.086570","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:13:17.149364","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:13:47.791164","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:14:19.559246","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:14:50.612797","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:15:21.536533","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:15:53.129226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:16:23.607210","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:16:54.018690","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:17:24.800173","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:17:56.512493","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:18:27.386187","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:18:57.992126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:19:29.417831","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:19:59.943178","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:20:30.754017","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:21:00.941636","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:21:31.905265","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:22:02.751746","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:22:33.340667","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:23:04.487640","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:23:35.205025","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:24:05.767966","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:24:36.578696","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:25:07.370236","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:25:38.152443","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:26:08.754979","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:26:39.523982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:27:10.689478","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:27:41.231918","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:28:11.816854","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:28:42.479703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:29:13.178198","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:29:43.631346","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:30:14.541745","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:30:45.072221","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:31:15.765611","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:31:46.233549","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:32:16.947453","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:32:47.701839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:33:18.110092","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:33:49.284201","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:34:19.825506","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:34:50.354326","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:35:20.921124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:35:51.615939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:36:22.259007","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:36:52.760500","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:37:23.794850","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:37:54.591285","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:38:25.790883","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:38:56.379774","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:39:26.933325","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:39:57.594586","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:40:28.401054","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:40:58.914335","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:41:29.732715","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:41:59.927993","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:42:30.737451","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:43:02.248765","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:44:51.144849","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:45:22.075452","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:45:52.763512","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:46:24.404814","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:46:54.489844","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:47:24.884769","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:47:55.489034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:48:26.464243","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:48:56.541015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:49:27.149892","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:49:58.554287","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:50:30.096440","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:51:00.335757","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:51:31.251300","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:52:02.864897","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:52:34.620608","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:53:05.974409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:53:37.076176","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:54:07.774488","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:54:39.420503","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:55:11.194665","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:55:42.862169","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:56:13.546132","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:56:44.196378","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:57:15.798547","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:57:46.320031","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:58:17.872443","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:58:49.654140","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:59:20.349722","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:59:50.863907","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:00:22.482697","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:00:53.792375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:01:24.700233","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:01:55.638631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:02:26.958474","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:02:58.531331","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:03:30.209077","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:04:00.632876","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:04:31.801717","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:05:02.384505","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:05:33.932199","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:06:04.746686","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:06:36.425027","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:07:07.760696","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:07:39.240994","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:08:10.712474","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:08:42.351348","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:09:13.593157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:09:45.187843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:10:16.723726","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:10:48.047959","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:11:20.668519","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:11:51.267460","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:12:22.876914","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:12:54.204892","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:13:25.700209","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:13:57.563489","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:14:28.021706","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:14:59.411305","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:15:30.867681","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:16:01.970542","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:16:32.335851","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:17:03.782377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:19:11.416383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:19:42.981522","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:20:16.460589","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:20:48.227641","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:21:18.970009","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:21:49.817740","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:22:21.403764","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:22:52.207045","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:23:23.039724","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:23:54.151213","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:24:24.838203","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:24:55.324600","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:25:26.550782","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:25:57.359902","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:26:28.058277","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:26:59.157259","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:27:29.778621","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:43:56.650883","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:44:28.372619","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:44:59.287090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:45:29.731406","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:46:00.970332","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:46:32.636117","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:47:04.237823","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:47:34.697301","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:48:06.134024","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:48:37.707015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:49:08.947888","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:49:40.347800","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:50:11.856807","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:50:43.086361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:51:14.587101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:51:46.209631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:52:17.719616","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:52:48.523863","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:53:20.330039","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:53:51.107912","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:54:22.652092","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:54:54.130043","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:55:25.483089","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:55:56.940492","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:56:28.583464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:56:59.614797","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:57:31.252329","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:58:02.885016","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:58:34.329408","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:59:05.928616","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:59:37.814881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:00:09.300450","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:00:40.008379","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:01:11.520170","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:01:42.016604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:02:13.861427","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:02:45.404768","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:03:16.603566","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:03:48.262709","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:04:19.595506","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:04:50.613499","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:05:22.154839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:05:55.542560","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:06:26.366661","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:06:57.788077","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:07:28.556601","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:07:59.615847","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:08:30.432843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:09:01.064639","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:09:31.792763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:10:02.856529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:10:33.925574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:11:05.565718","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:11:36.536163","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:12:07.272586","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:12:38.180811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:19:41.126753","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:20:12.690986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:20:42.976485","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:21:13.946630","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:21:44.079213","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:22:14.787555","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:22:45.383145","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:23:15.742736","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:23:46.302221","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:24:18.318511","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:24:48.746385","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:25:19.620392","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:25:50.579002","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:26:22.158081","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:26:52.560167","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:27:23.398100","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:27:54.333172","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:28:24.983811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:28:56.042438","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:29:26.856571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:29:57.404959","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:30:28.154476","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:30:58.768992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:31:29.206679","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:32:00.014690","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:32:31.929563","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:33:02.269707","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:33:33.058758","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:34:04.012006","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:34:34.385865","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:35:05.181410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:35:35.920589","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:36:06.732432","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:36:37.561920","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:37:08.497765","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:37:38.957152","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:38:09.741949","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:38:40.517636","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:39:11.022572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:39:41.991202","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:40:12.578604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:40:43.813291","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:41:14.231636","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:41:45.303572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:42:15.831775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:42:46.562444","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:43:19.139258","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:43:34.747551","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:44:06.257501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:44:38.301804","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
