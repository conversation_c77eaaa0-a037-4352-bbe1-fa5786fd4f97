{"timestamp":"2025-09-07T04:56:54.512112","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:57:25.144269","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:57:55.722254","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:58:26.239040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:58:56.685410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:59:27.375493","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:59:58.610099","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:00:29.153743","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:00:59.515676","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:01:30.047377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:02:00.654272","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:02:31.940304","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:03:02.614919","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:03:33.103298","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:04:03.562217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:04:33.870189","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:05:04.376436","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:05:34.716678","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:06:05.052347","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:06:35.335527","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:07:05.929181","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:07:36.489336","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:08:07.162756","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:08:37.352659","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:09:08.148481","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:09:38.935035","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:10:09.581748","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:10:40.034971","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:11:10.186461","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:11:41.933056","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:12:12.374313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:12:43.393087","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:13:16.728470","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:13:47.387930","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:14:18.676677","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:14:49.617283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:15:20.062958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:15:50.609127","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:16:20.953902","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:16:51.381210","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:17:22.409318","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:17:53.083297","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:18:23.436338","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:18:54.117213","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:19:24.622718","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:19:54.968150","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:20:25.620063","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:20:56.347734","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:21:26.991442","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:21:57.869158","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:22:28.755560","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:22:59.503465","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:23:30.333307","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:24:01.045343","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:24:32.038515","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:25:02.647170","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:25:33.183028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:26:04.163612","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:26:34.956562","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:27:05.382790","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:27:36.384713","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:28:07.291845","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:28:37.910944","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:29:08.254603","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:29:39.017985","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:30:09.941682","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:30:40.499707","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:31:10.857157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:31:41.584824","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:32:12.370225","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:32:42.732253","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:33:13.465615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:33:44.390198","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:34:15.270268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:34:45.554441","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:35:16.267463","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:35:46.992771","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:36:17.707786","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:36:47.986577","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:37:19.092538","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:37:49.951048","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:38:20.379631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:38:51.619748","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:39:22.365917","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:39:53.029718","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:40:23.493106","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:40:54.311563","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:41:25.018038","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:41:55.589996","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:42:26.962600","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:42:57.633775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:43:28.417917","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:44:50.344764","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:45:21.279795","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:45:51.929855","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:46:23.459394","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:46:54.103579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:47:24.518042","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:47:54.821579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:48:25.554550","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:48:56.139250","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:49:26.601469","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:49:57.796553","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:50:29.272778","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:50:59.748765","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:51:30.084364","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:52:00.989675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:52:31.536953","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:53:02.243943","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:53:33.078811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:54:03.843492","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:54:34.794767","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:55:06.136978","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:55:36.853482","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:56:07.748916","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:56:38.221081","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:57:08.842802","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:57:39.632961","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:58:09.950322","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:58:40.522088","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:59:11.462037","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:59:41.905168","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:00:12.442423","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:00:43.061259","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:01:13.371547","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:01:44.264800","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:02:15.173995","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:02:45.509206","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:03:15.993149","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:03:46.753274","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:04:17.307461","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:04:48.415366","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:05:18.803898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:05:49.648481","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:06:20.318581","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:06:50.828989","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:07:21.230897","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:07:51.423472","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:08:23.222222","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:08:53.718352","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:09:23.954615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:09:54.660749","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:10:25.113216","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:10:55.483534","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:11:26.171375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:11:56.617730","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:12:27.148075","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:12:57.453483","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:13:27.920639","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:13:58.870962","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:14:29.206050","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:14:59.479610","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:15:30.084680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:16:00.425164","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:16:31.573028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:17:02.968817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:19:10.904505","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:19:42.138034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:20:13.132359","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:20:45.273879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:21:16.093081","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:21:46.610615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:22:17.154855","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:22:47.348148","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:23:17.882615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:23:48.858534","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:24:19.956600","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:24:51.067618","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:25:22.056846","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:25:52.253250","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:26:22.930738","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:26:53.754526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:27:24.891981","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:43:56.087001","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:44:27.258388","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:44:58.198670","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:45:28.954410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:45:59.308827","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:46:29.605669","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:47:00.180261","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:47:30.882392","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:48:01.294918","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:48:31.715677","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:49:02.250879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:49:32.435291","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:50:02.846975","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:50:33.353941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:51:03.513414","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:51:34.104441","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:52:04.768898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:52:35.233421","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:53:06.020158","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:53:37.186346","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:54:07.540416","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:54:38.209160","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:55:08.512185","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:55:38.881591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:56:09.387143","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:56:40.362824","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:57:11.071672","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:57:41.637779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:58:12.260576","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:58:42.625261","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:59:13.198261","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:59:44.113354","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:00:14.521599","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:00:45.191182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:01:15.804625","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:01:46.260860","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:02:17.064257","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:02:47.573182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:03:17.744986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:03:48.325657","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:04:19.015253","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:04:49.635877","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:05:20.279213","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:05:51.079552","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:06:21.713482","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:06:52.742722","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:07:23.452895","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:07:53.747957","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:08:24.325077","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:08:55.050853","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:09:25.752876","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:09:56.662682","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:10:27.523367","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:10:58.517635","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:11:29.153949","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:12:00.127958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:12:30.926898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:19:40.307628","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:20:11.825939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:20:42.405865","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:21:12.897308","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:21:43.682707","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:22:14.367024","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:22:44.717402","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:23:15.341234","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:23:45.908924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:24:16.287413","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:24:47.244987","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:25:18.033601","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:25:48.583009","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:26:19.266620","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:26:49.808156","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:27:20.284782","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:27:51.183116","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:28:22.233109","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:28:52.709326","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:29:23.895757","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:29:54.516824","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:30:25.144469","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:30:55.878124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:31:26.469011","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:31:56.914280","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:32:27.838346","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:32:58.552571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:33:29.054664","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:33:59.977787","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:34:30.653476","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:35:01.160163","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:35:31.975927","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:36:02.721831","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:36:33.488616","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:37:04.362955","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:37:35.165272","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:38:05.545261","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:38:36.401266","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:39:07.089750","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:39:37.770727","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:40:08.917606","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:40:39.335608","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:41:09.671666","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:41:40.046873","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:42:10.806524","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:42:41.307075","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:43:11.965063","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:43:33.506368","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:44:05.444829","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:44:36.480558","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
