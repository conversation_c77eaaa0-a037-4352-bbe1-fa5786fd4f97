{"timestamp":"2025-09-07T04:56:55.104030","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:57:25.460976","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:57:55.988787","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:58:26.520728","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:58:57.331949","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:59:27.658926","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:59:58.885221","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:00:29.455694","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:01:00.042735","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:01:30.311446","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:02:00.939177","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:02:32.496267","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:03:02.682251","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:03:33.204750","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:04:03.797068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:04:34.360357","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:05:04.449297","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:05:34.782522","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:06:06.190577","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:06:36.457564","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:07:07.054387","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:07:37.633363","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:08:08.276181","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:08:39.599787","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:09:10.331577","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:09:41.099672","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:10:11.752746","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:10:42.257543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:11:13.366555","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:11:44.117637","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:12:14.719890","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:12:45.531974","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:13:17.052341","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:13:47.684610","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:14:19.386856","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:14:49.749625","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:15:20.137281","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:15:51.767187","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:16:22.101480","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:16:53.540056","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:17:24.582481","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:17:55.235905","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:18:25.634011","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:18:56.274529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:19:26.806685","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:19:57.113457","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:20:27.785526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:20:58.496655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:21:29.306053","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:22:00.050100","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:22:30.908504","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:23:01.677960","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:23:32.502284","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:24:03.248395","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:24:34.182691","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:25:04.867804","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:25:35.356683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:26:06.339735","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:26:37.073426","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:27:07.716362","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:27:38.544535","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:28:09.418369","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:28:40.063747","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:29:10.400488","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:29:41.200071","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:30:12.094814","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:30:42.650101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:31:13.012476","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:31:43.732313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:32:14.514051","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:32:44.892671","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:33:15.633322","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:33:46.693042","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:34:17.402414","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:34:47.713892","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:35:18.440814","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:35:49.147270","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:36:19.848521","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:36:50.145678","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:37:21.305701","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:37:52.112272","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:38:22.803767","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:38:53.746254","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:39:24.521910","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:39:55.172832","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:40:25.659728","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:40:56.457226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:41:27.194028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:41:58.016002","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:42:29.147115","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:42:59.792426","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:43:30.554266","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:44:50.620147","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:45:21.757736","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:45:52.667601","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:46:23.766948","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:46:54.393682","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:47:24.784012","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:47:55.375839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:48:25.871805","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:48:56.413961","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:49:26.869513","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:49:58.305557","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:50:29.603744","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:51:00.029096","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:51:31.143962","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:52:02.762542","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:52:34.499885","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:53:05.873157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:53:36.940991","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:54:07.665144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:54:39.318779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:55:11.061507","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:55:42.739603","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:56:13.370871","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:56:44.095397","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:57:15.695262","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:57:46.219740","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:58:17.771087","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:58:49.380394","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:59:20.118984","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:59:50.760607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:00:22.375123","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:00:53.695446","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:01:24.568844","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:01:55.532070","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:02:26.854865","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:02:58.433028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:03:30.083198","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:04:00.519159","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:04:31.670921","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:05:02.286713","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:05:33.555402","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:06:04.633276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:06:36.322165","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:07:07.673606","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:07:39.134686","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:08:10.609239","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:08:42.229821","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:09:13.498092","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:09:45.087423","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:10:16.625506","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:10:47.908507","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:11:20.542297","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:11:51.173761","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:12:22.782409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:12:54.098313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:13:25.591999","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:13:57.284701","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:14:27.893305","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:14:59.154880","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:15:30.404191","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:16:01.728941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:16:32.104972","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:17:03.326859","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:19:11.167688","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:19:42.741820","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:20:15.167926","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:20:47.319679","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:21:18.833470","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:21:49.563405","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:22:20.685587","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:22:51.908745","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:23:22.805441","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:23:54.022458","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:24:24.650402","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:24:55.119479","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:25:26.441155","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:25:57.245111","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:26:27.920502","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:26:59.025896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:27:29.353898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:43:56.370005","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:44:28.252494","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:44:59.156432","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:45:29.608588","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:46:00.835466","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:46:32.528595","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:47:03.725653","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:47:34.586625","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:48:06.024418","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:48:37.589353","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:49:08.854181","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:49:40.246088","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:50:11.751426","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:50:42.986910","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:51:14.486578","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:51:46.080548","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:52:17.611673","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:52:48.412332","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:53:19.881811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:53:51.001388","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:54:22.530718","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:54:54.033653","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:55:25.361870","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:55:56.842645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:56:28.141778","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:56:59.516298","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:57:31.145393","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:58:02.778584","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:58:34.221124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:59:05.767538","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:59:37.659980","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:00:09.193635","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:00:39.907770","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:01:11.418034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:01:41.907575","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:02:13.747377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:02:45.302967","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:03:16.501422","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:03:48.153588","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:04:19.341689","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:04:50.504977","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:05:22.050052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:05:54.724639","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:06:26.190974","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:06:57.617013","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:07:28.185164","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:07:59.496410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:08:30.319773","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:09:00.955944","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:09:31.671906","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:10:02.742409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:10:33.775622","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:11:04.311849","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:11:35.320226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:12:05.817130","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:12:38.065643","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:19:40.867896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:20:12.190908","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:20:42.712198","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:21:13.629302","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:21:43.968554","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:22:14.673952","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:22:45.282467","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:23:15.641877","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:23:46.195511","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:24:17.108268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:24:47.542094","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:25:18.355529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:25:49.181633","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:26:19.530959","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:26:50.106225","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:27:20.797194","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:27:51.466792","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:28:22.507746","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:28:53.369230","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:29:24.171610","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:29:55.960223","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:30:26.813355","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:30:57.587769","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:31:27.898516","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:31:58.556829","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:32:30.350238","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:33:00.977590","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:33:31.742293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:34:02.466401","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:34:33.063794","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:35:03.866209","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:35:34.398366","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:36:05.322476","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:36:36.255518","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:37:06.767700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:37:37.632653","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:38:08.383273","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:38:38.905986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:39:09.640354","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:39:40.651982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:40:11.751373","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:40:43.427982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:41:13.929559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:41:44.624928","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:42:15.541599","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:42:46.201609","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:43:17.540166","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:43:34.656483","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:44:05.942469","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:44:36.758110","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
