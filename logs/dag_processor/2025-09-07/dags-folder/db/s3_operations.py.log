{"timestamp":"2025-09-07T04:56:55.290361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:57:25.935613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:57:56.038925","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:58:26.573626","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:58:57.384075","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:59:27.947424","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:59:58.934342","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:00:29.508225","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:01:00.069893","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:01:30.326004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:02:00.991719","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:02:32.593271","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:03:03.130342","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:03:33.428828","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:04:03.815600","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:04:34.450694","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:05:04.565984","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:05:35.914901","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:06:06.458142","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:06:36.877572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:07:07.528104","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:07:37.945958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:08:08.481534","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:08:40.178222","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:09:10.537315","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:09:42.438708","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:10:13.087062","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:10:43.897298","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:11:14.642678","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:11:45.413064","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:12:16.368856","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:12:47.073941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:13:17.207403","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:13:47.756217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:14:19.488128","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:14:50.280282","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:15:21.304150","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:15:51.992279","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:16:23.595936","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:16:53.951357","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:17:25.903464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:17:56.565057","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:18:27.159679","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:18:57.578953","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:19:28.148121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:19:58.652806","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:20:28.916075","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:20:59.650785","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:21:30.524679","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:22:01.211512","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:22:32.047847","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:23:03.067594","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:23:33.635308","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:24:04.441246","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:24:35.301658","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:25:06.039839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:25:36.524082","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:26:07.453700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:26:38.239722","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:27:09.049002","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:27:39.662716","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:28:10.560532","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:28:41.225767","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:29:11.568135","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:29:42.359401","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:30:13.237956","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:30:43.788114","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:31:14.157880","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:31:44.923643","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:32:15.658886","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:32:46.023457","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:33:16.785124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:33:47.894993","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:34:18.552473","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:34:48.852793","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:35:19.623975","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:35:50.304251","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:36:20.985808","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:36:51.281921","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:37:22.468864","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:37:53.280847","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:38:23.931264","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:38:54.893296","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:39:25.628838","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:39:56.328121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:40:26.811889","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:40:57.605792","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:41:28.401000","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:41:59.311707","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:42:30.314011","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:43:00.942426","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:43:31.704265","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:44:51.080845","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:45:21.990852","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:45:52.735007","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:46:24.310724","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:46:54.446111","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:47:24.809081","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:47:55.436941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:48:26.368511","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:48:56.465685","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:49:26.964432","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:49:58.502001","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:50:30.033529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:51:00.158382","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:51:31.203106","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:52:02.796513","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:52:34.547150","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:53:05.910184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:53:36.999777","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:54:07.714783","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:54:39.333174","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:55:11.089823","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:55:42.789054","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:56:13.431704","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:56:44.141417","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:57:15.728361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:57:46.259881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:58:17.804010","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:58:49.388945","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:59:20.246501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:59:50.799301","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:00:22.413838","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:00:53.734416","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:01:24.604427","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:01:55.578855","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:02:26.887004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:02:58.470580","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:03:30.117491","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:04:00.551996","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:04:31.710450","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:05:02.325094","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:05:33.590825","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:06:04.683997","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:06:36.356771","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:07:07.700274","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:07:39.186115","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:08:10.641298","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:08:42.276407","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:09:13.525917","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:09:45.121211","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:10:16.659932","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:10:47.935913","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:11:20.542164","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:11:51.203065","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:12:22.810619","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:12:54.132905","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:13:25.624958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:13:57.326342","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:14:27.995759","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:14:59.352829","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:15:30.818125","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:16:01.920696","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:16:32.282393","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:17:03.717668","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:19:11.351127","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:19:42.919190","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:20:16.245263","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:20:47.699006","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:21:18.947458","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:21:49.750464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:22:21.335161","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:22:52.151655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:23:22.973694","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:23:54.125304","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:24:25.956889","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:24:56.479684","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:25:26.623371","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:25:57.270259","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:26:27.966969","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:26:59.135707","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:27:30.898080","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:43:56.579839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:44:28.324216","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:44:59.217980","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:45:29.642663","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:46:00.866341","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:46:32.568015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:47:03.990343","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:47:34.630763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:48:06.054248","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:48:37.625043","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:49:08.878928","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:49:40.281549","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:50:11.785483","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:50:43.014786","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:51:14.515076","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:51:46.140674","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:52:17.648025","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:52:48.460085","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:53:19.920931","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:53:51.035096","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:54:22.570769","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:54:54.064502","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:55:25.396502","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:55:56.876216","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:56:28.175423","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:56:59.544642","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:57:31.182958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:58:02.816836","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:58:34.257101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:59:05.845161","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:59:37.723346","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:00:09.240599","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:00:39.939184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:01:11.482474","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:01:41.949470","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:02:13.787596","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:02:45.337575","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:03:16.529496","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:03:48.200304","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:04:19.525760","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:04:50.560242","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:05:22.086525","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:05:55.464730","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:06:26.303725","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:06:57.669478","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:07:28.229564","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:07:59.528803","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:08:30.351260","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:09:00.992072","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:09:31.706591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:10:02.779168","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:10:33.804615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:11:04.367340","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:11:35.360946","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:12:05.852840","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:12:38.072739","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:19:41.060182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:20:12.621861","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:20:42.907203","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:21:13.843234","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:21:44.029122","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:22:14.723636","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:22:45.305755","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:23:15.660577","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:23:46.250959","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:24:17.137155","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:24:47.577910","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:25:18.426682","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:25:50.376385","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:26:20.695600","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:26:51.282201","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:27:21.984484","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:27:52.666080","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:28:23.683293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:28:54.570888","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:29:25.337709","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:29:56.229435","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:30:28.139435","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:30:58.747385","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:31:29.193920","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:31:59.762110","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:32:30.773055","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:33:02.258653","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:33:33.041469","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:34:04.000370","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:34:34.379663","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:35:05.165287","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:35:35.905395","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:36:06.718475","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:36:37.550166","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:37:08.485342","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:37:38.935091","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:38:09.732177","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:38:40.501150","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:39:11.010812","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:39:41.972634","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:40:12.501970","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:40:43.738114","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:41:14.353833","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:41:45.232074","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:42:15.769386","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:42:46.502524","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:43:18.723317","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:43:34.727800","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:44:06.141857","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:44:38.127631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
