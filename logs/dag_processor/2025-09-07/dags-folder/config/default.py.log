{"timestamp":"2025-09-07T04:56:55.036464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:57:25.395981","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:57:55.953378","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:58:26.486811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:58:57.304764","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:59:27.603893","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:59:58.850315","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:00:29.416677","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:00:59.987935","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:01:30.261168","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:02:00.907285","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:02:32.437456","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:03:02.614919","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:03:33.103297","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:04:03.760794","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:04:34.303156","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:05:04.376916","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:05:34.727253","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:06:05.052347","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:06:35.342587","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:07:05.929180","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:07:36.489388","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:08:07.162756","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:08:37.359695","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:09:08.148481","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:09:38.935041","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:10:09.581748","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:10:40.034971","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:11:10.179095","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:11:40.799850","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:12:11.297080","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:12:42.291885","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:13:17.011863","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:13:47.655412","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:14:19.312981","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:14:49.617309","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:15:20.062958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:15:50.609127","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:16:20.953902","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:16:51.381210","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:17:22.409318","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:17:53.083298","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:18:23.436338","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:18:54.117213","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:19:24.622718","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:19:54.968150","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:20:25.620063","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:20:56.347733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:21:26.991444","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:21:57.869158","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:22:28.755561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:22:59.503465","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:23:30.333304","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:24:01.045343","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:24:32.038515","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:25:02.647170","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:25:33.183028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:26:04.163612","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:26:34.956562","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:27:05.382790","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:27:36.384714","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:28:07.291845","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:28:37.910944","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:29:08.254603","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:29:39.017984","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:30:09.941682","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:30:40.499707","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:31:10.857157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:31:41.584824","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:32:12.370226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:32:42.732253","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:33:13.465615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:33:44.390906","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:34:15.270268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:34:45.554441","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:35:16.267463","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:35:46.992771","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:36:17.714330","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:36:47.986577","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:37:19.092538","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:37:49.951048","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:38:20.379631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:38:50.517341","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:39:21.247425","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:39:51.924372","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:40:22.344757","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:40:53.232473","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:41:23.911477","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:41:54.498972","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:42:24.838075","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:42:55.518459","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:43:26.288532","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:44:50.540415","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:45:21.666776","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:45:52.379162","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:46:23.689094","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:46:54.359230","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:47:24.745770","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:47:54.939035","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:48:25.797316","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:48:56.381422","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:49:26.836953","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:49:58.238687","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:50:29.522154","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:50:59.975228","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:51:30.800241","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:52:00.989503","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:52:31.536953","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:53:02.243944","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:53:33.078811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:54:03.843492","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:54:34.794767","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:55:05.025119","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:55:35.756819","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:56:06.650855","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:56:37.131356","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:57:07.740155","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:57:38.478990","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:58:08.845994","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:58:39.406907","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:59:10.356841","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:59:40.819308","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:00:11.345770","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:00:41.983159","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:01:12.230865","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:01:43.180685","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:02:14.068120","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:02:44.403219","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:03:14.894440","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:03:45.650030","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:04:16.202611","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:04:47.310679","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:05:17.741411","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:05:48.433428","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:06:19.113061","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:06:49.730690","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:07:20.126642","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:07:51.364623","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:08:22.123998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:08:52.621266","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:09:22.847444","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:09:53.567691","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:10:24.015082","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:10:54.367500","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:11:25.060049","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:11:55.526473","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:12:26.058532","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:12:56.344519","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:13:26.828842","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:13:57.764648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:14:27.924054","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:14:58.744258","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:15:30.313644","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:16:00.616971","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:16:32.023775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:17:03.257410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:19:11.092222","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:19:42.657487","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:20:14.810751","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:20:45.943712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:21:18.485704","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:21:49.485025","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:22:19.552413","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:22:50.779222","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:23:22.721236","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:23:53.448994","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:24:24.542166","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:24:54.863276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:25:25.646399","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:25:56.462524","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:26:27.220455","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:26:58.293698","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:27:29.098901","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:43:56.287665","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:44:27.967544","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:44:58.197627","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:45:28.955657","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:45:59.311848","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:46:29.605669","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:47:00.186439","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:47:30.891557","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:48:01.301327","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:48:31.717526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:49:02.250879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:49:32.435291","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:50:02.851918","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:50:33.353967","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:51:03.513414","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:51:34.104442","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:52:04.768898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:52:35.233421","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:53:06.020158","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:53:37.186346","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:54:07.540416","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:54:38.209160","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:55:08.512185","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:55:38.881591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:56:09.387143","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:56:40.362824","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:57:11.071672","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:57:41.637779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:58:12.260576","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:58:42.632986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:59:13.207570","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:59:44.123813","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:00:14.526508","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:00:45.209130","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:01:15.813022","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:01:46.270960","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:02:17.079640","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:02:47.583831","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:03:17.753799","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:03:48.381373","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:04:19.253642","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:04:50.209333","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:05:20.357207","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:05:54.104723","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:06:25.181043","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:06:56.030495","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:07:26.636416","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:07:56.943240","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:08:27.505850","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:08:58.226824","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:09:29.048935","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:09:59.866409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:10:30.703444","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:11:01.681597","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:11:32.371623","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:12:03.295579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:12:33.463122","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:19:40.773512","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:20:12.100595","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:20:42.641930","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:21:13.165463","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:21:43.935788","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:22:14.638533","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:22:45.222024","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:23:15.589112","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:23:46.165510","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:24:17.052170","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:24:47.503068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:25:18.315927","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:25:49.139058","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:26:19.492461","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:26:50.064019","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:27:20.764186","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:27:51.429139","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:28:22.474813","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:28:53.321873","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:29:24.142948","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:29:54.807499","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:30:26.727986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:30:57.189021","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:31:27.826313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:31:58.550482","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:32:29.202680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:32:59.842710","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:33:30.618681","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:34:01.340432","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:34:31.941160","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:35:02.715541","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:35:33.272745","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:36:04.119111","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:36:35.135308","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:37:05.666526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:37:36.509881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:38:07.259603","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:38:37.588685","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:39:08.266671","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:39:38.971939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:40:10.098594","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:40:40.563638","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:41:12.148268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:41:43.285877","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:42:14.078799","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:42:44.532359","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:43:15.446811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:43:34.375984","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:44:05.872626","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:44:36.722252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
