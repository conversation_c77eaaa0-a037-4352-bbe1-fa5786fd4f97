{"timestamp":"2025-09-07T04:56:54.512112","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:56:54.519667","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T04:56:54.935177","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T04:56:54.940246","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T04:57:25.162342","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:57:25.170260","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T04:57:25.334653","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T04:57:25.338784","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T04:57:55.722254","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:57:55.727834","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T04:57:55.893918","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T04:57:55.898606","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T04:58:26.239052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:58:26.251093","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T04:58:26.427095","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T04:58:26.432234","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T04:58:56.685410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:58:56.696090","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T04:58:57.223049","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T04:58:57.227266","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T04:59:27.382445","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:59:27.387317","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T04:59:27.540415","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T04:59:27.544527","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T04:59:58.610099","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T04:59:58.617594","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T04:59:58.790465","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T04:59:58.794783","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:00:29.153743","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:00:29.165452","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:00:29.355675","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:00:29.361057","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:00:59.520134","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:00:59.528274","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:00:59.924476","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:00:59.928228","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:01:30.047377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:01:30.052322","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:01:30.201845","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:01:30.206203","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:02:00.654149","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:02:00.660077","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:02:00.834650","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:02:00.838894","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:02:31.940304","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:02:31.946206","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:02:32.372876","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:02:32.376824","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:03:02.713978","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:03:02.718614","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:03:03.050856","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:03:03.054618","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:03:33.170753","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:03:33.175755","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:03:33.359994","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:03:33.366927","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:04:03.555965","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:04:03.560863","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:04:03.706082","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:04:03.710411","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:04:33.870189","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:04:33.876221","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:04:34.242133","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:04:34.246940","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:05:04.520211","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:05:04.523572","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:05:04.868240","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:05:04.873208","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:05:35.908781","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:05:35.913423","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:05:36.053889","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:05:36.058084","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:06:06.190269","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:06:06.194957","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:06:06.374635","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:06:06.379269","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:06:36.505760","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:06:36.509778","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:06:36.820195","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:06:36.825370","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:07:07.041971","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:07:07.046247","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:07:07.452093","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:07:07.456150","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:07:37.619184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:07:37.625155","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:07:37.833047","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:07:37.838504","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:08:08.267618","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:08:08.272267","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:08:08.425059","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:08:08.429085","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:08:39.599787","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:08:39.607011","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:08:40.051928","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:08:40.057374","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:09:10.326343","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:09:10.331117","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:09:10.479509","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:09:10.483909","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:09:41.088259","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:09:41.092683","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:09:41.244540","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:09:41.248566","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:10:11.742372","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:10:11.748091","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:10:11.939621","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:10:11.948057","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:10:42.256965","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:10:42.266604","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:10:42.742001","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:10:42.746601","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:11:13.356676","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:11:13.360966","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:11:13.531584","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:11:13.535748","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:11:44.099666","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:11:44.104902","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:11:44.264793","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:11:44.269897","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:12:14.712410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:12:14.716540","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:12:15.068110","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:12:15.073037","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:12:45.517410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:12:45.521990","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:12:45.845464","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:12:45.850810","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:13:16.728470","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:13:16.733773","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:13:16.924073","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:13:16.930733","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:13:47.387909","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:13:47.394317","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:13:47.569847","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:13:47.579337","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:14:18.676677","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:14:18.683197","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:14:19.246981","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:14:19.251664","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:14:49.718077","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:14:49.723091","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:14:49.913314","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:14:49.918610","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:15:21.292572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:15:21.296949","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:15:21.468749","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:15:21.474457","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:15:51.763520","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:15:51.768095","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:15:51.932006","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:15:51.936074","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:16:22.095733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:16:22.101235","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:16:22.468014","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:16:22.472384","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:16:53.535658","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:16:53.541394","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:16:53.729737","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:16:53.741521","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:17:24.571390","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:17:24.575743","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:17:24.735535","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:17:24.739595","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:17:55.231050","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:17:55.235566","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:17:55.381643","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:17:55.386070","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:18:25.623987","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:18:25.628845","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:18:26.014707","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:18:26.018812","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:18:56.270520","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:18:56.274826","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:18:56.441678","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:18:56.446462","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:19:26.798978","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:19:26.804026","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:19:26.997607","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:19:27.007663","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:19:57.129676","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:19:57.133617","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:19:57.503716","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:19:57.507966","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:20:28.907555","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:20:28.911619","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:20:29.064764","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:20:29.069087","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:20:59.638595","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:20:59.643493","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:20:59.795380","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:20:59.800329","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:21:30.551594","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:21:30.556327","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:21:30.733689","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:21:30.743512","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:22:01.197187","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:22:01.201890","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:22:01.603793","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:22:01.608269","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:22:32.038514","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:22:32.043158","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:22:32.194551","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:22:32.198773","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:23:03.084541","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:23:03.090767","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:23:03.301275","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:23:03.313980","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:23:33.624228","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:23:33.631296","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:23:34.034166","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:23:34.044347","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:24:04.410863","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:24:04.416762","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:24:04.600090","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:24:04.604778","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:24:35.291406","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:24:35.295504","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:24:35.444229","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:24:35.448337","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:25:06.038445","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:25:06.043491","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:25:06.203181","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:25:06.208727","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:25:36.507841","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:25:36.513431","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:25:36.956839","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:25:36.966793","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:26:07.441330","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:26:07.446461","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:26:07.607828","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:26:07.612180","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:26:38.232205","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:26:38.237759","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:26:38.400262","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:26:38.404812","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:27:09.043909","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:27:09.047927","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:27:09.390558","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:27:09.395643","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:27:39.652020","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:27:39.659398","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:27:40.063538","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:27:40.068135","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:28:10.554330","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:28:10.559012","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:28:10.710845","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:28:10.716063","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:28:41.211710","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:28:41.216347","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:28:41.389449","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:28:41.395074","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:29:11.552148","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:29:11.556917","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:29:12.006234","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:29:12.010477","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:29:42.351828","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:29:42.357231","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:29:42.509386","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:29:42.513832","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:30:13.227307","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:30:13.231929","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:30:13.396141","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:30:13.404836","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:30:43.772335","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:30:43.776661","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:30:43.932268","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:30:43.937319","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:31:14.148744","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:31:14.157417","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:31:14.602302","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:31:14.606557","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:31:44.909057","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:31:44.914230","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:31:45.079704","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:31:45.084112","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:32:15.652003","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:32:15.656574","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:32:15.821509","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:32:15.825957","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:32:46.019053","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:32:46.023486","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:32:46.405709","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:32:46.409744","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:33:16.774287","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:33:16.778720","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:33:16.960424","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:33:16.966177","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:33:47.899364","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:33:47.930245","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:33:48.118383","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:33:48.123602","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:34:18.538368","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:34:18.542097","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:34:18.696549","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:34:18.700678","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:34:48.844916","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:34:48.849109","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:34:49.221766","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:34:49.225923","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:35:19.607451","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:35:19.617612","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:35:19.779788","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:35:19.784070","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:35:50.283471","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:35:50.291315","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:35:50.467018","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:35:50.472214","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:36:20.978704","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:36:20.983089","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:36:21.123939","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:36:21.127965","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:36:51.277254","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:36:51.281071","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:36:51.616746","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:36:51.621034","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:37:22.465382","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:37:22.477217","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:37:22.645120","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:37:22.649746","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:37:53.272377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:37:53.277055","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:37:53.438695","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:37:53.444063","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:38:23.925621","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:38:23.930000","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:38:24.458117","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:38:24.466333","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:38:54.877807","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:38:54.882597","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:38:55.244809","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:38:55.250169","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:39:25.614946","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:39:25.619084","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:39:25.796731","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:39:25.803277","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:39:56.315562","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:39:56.320334","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:39:56.481172","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:39:56.485222","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:40:26.809407","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:40:26.814505","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:40:27.239139","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:40:27.249711","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:40:57.595745","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:40:57.600232","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:40:57.758292","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:40:57.763122","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:41:28.375948","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:41:28.381549","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:41:28.573524","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:41:28.578263","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:41:59.305452","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:41:59.309304","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:41:59.691016","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:41:59.695344","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:42:30.295085","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:42:30.302173","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:42:30.668974","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:42:30.674163","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:43:00.933700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:43:00.938062","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:43:01.090024","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:43:01.094252","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:43:31.692297","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:43:31.696524","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:43:31.858559","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:43:31.863479","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:44:50.336737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:44:50.340266","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:44:50.484877","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:44:50.488892","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:45:21.279795","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:45:21.288242","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:45:21.573028","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:45:21.578273","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:45:51.929368","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:45:51.935028","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:45:52.314038","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:45:52.318119","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:46:23.459394","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:46:23.465877","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:46:23.624214","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:46:23.628833","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:46:54.103579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:46:54.110188","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:46:54.293999","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:46:54.298758","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:47:24.518042","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:47:24.524176","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:47:24.683160","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:47:24.687486","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:47:54.898193","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:47:54.907983","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:47:55.314304","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:47:55.318765","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:48:25.554550","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:48:25.563323","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:48:25.726005","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:48:25.730273","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:48:56.139250","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:48:56.146224","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:48:56.315296","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:48:56.319537","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:49:26.601474","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:49:26.613335","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:49:26.774421","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:49:26.778068","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:49:57.779675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:49:57.788877","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:49:58.174647","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:49:58.178686","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:50:29.272778","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:50:29.279552","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:50:29.454862","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:50:29.458885","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:50:59.748765","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:50:59.757636","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:50:59.912998","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:50:59.917274","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:51:30.084364","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:51:30.091279","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:51:30.690604","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:51:30.698176","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:52:02.159632","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:52:02.164009","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:52:02.517277","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:52:02.520759","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:52:33.745959","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:52:33.751816","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:52:33.975145","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:52:33.980225","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:53:05.458429","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:53:05.463842","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:53:05.625010","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:53:05.629209","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:53:36.299445","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:53:36.304462","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:53:36.455732","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:53:36.460295","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:54:07.174336","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:54:07.198222","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:54:07.420804","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:54:07.561703","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:54:38.375020","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:54:38.383706","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:54:38.812580","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:54:38.818720","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:55:10.334407","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:55:10.341393","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:55:10.807722","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:55:10.811995","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:55:42.021347","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:55:42.028443","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:55:42.220110","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:55:42.225202","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:56:12.958199","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:56:12.964437","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:56:13.128525","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:56:13.133961","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:56:43.443041","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:56:43.449191","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:56:43.627449","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:56:43.632223","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:57:15.077394","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:57:15.083346","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:57:15.237995","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:57:15.241842","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:57:45.846373","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:57:45.850327","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:57:46.002799","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:57:46.007733","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:58:17.195897","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:58:17.201270","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:58:17.346172","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:58:17.350738","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:58:48.758753","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:58:48.763201","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:58:48.906956","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:58:48.910796","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:59:19.708266","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:59:19.712875","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:59:19.879774","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:59:19.884239","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:59:50.141961","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T05:59:50.146407","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:59:50.541774","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T05:59:50.546340","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:00:21.728888","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:00:21.735600","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:00:22.258736","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:00:22.265336","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:00:53.322806","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:00:53.327544","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:00:53.478306","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:00:53.482105","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:01:23.771636","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:01:23.778782","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:01:24.251199","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:01:24.255712","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:01:54.792434","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:01:54.803403","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:01:55.022732","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:01:55.027882","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:02:26.470655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:02:26.476564","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:02:26.630206","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:02:26.636307","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:02:57.830562","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:02:57.836143","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:02:58.212840","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:02:58.217186","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:03:29.365772","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:03:29.371180","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:03:29.567107","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:03:29.572577","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:04:00.106666","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:04:00.112581","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:04:00.291813","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:04:00.296688","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:04:30.836100","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:04:30.842187","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:04:31.516382","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:04:31.523761","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:05:01.713459","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:05:01.718137","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:05:01.869731","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:05:01.874028","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:05:33.146996","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:05:33.154550","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:05:33.307940","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:05:33.443678","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:06:04.236474","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:06:04.242138","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:06:04.394129","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:06:04.529225","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:06:35.657137","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:06:35.662681","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:06:36.087286","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:06:36.094459","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:07:07.194462","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:07:07.198903","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:07:07.331637","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:07:07.335522","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:07:38.683327","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:07:38.690357","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:07:38.854505","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:07:39.006892","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:08:09.884447","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:08:09.889433","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:08:10.299951","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:08:10.492496","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:08:41.613111","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:08:41.619232","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:08:41.774230","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:08:42.109316","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:09:13.139259","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:09:13.144607","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:09:13.280131","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:09:13.284195","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:09:44.468143","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:09:44.473401","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:09:44.869382","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:09:44.991629","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:10:16.095353","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:10:16.100021","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:10:16.246416","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:10:16.251824","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:10:47.541276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:10:47.545957","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:10:47.688599","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:10:47.693724","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:11:18.914758","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:11:18.920444","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:11:19.403783","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:11:19.544743","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:11:50.596417","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:11:50.600898","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:11:50.749878","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:11:50.754808","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:12:22.182341","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:12:22.192479","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:12:22.367608","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:12:22.689113","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:12:53.691557","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:12:53.696704","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:12:53.831954","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:12:53.836280","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:13:24.979338","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:13:24.985868","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:13:25.155680","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:13:25.499204","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:13:56.661071","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:13:56.666615","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:13:56.807553","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:13:56.937661","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:14:27.505682","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:14:27.511294","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:14:27.665204","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:14:27.790451","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:14:58.736971","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:14:58.741317","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:14:59.037606","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:14:59.041414","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:15:30.084680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:15:30.089931","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:15:30.242437","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:15:30.247608","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:16:00.420657","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:16:00.424919","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:16:00.559802","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:16:00.565281","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:16:31.573028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:16:31.578319","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:16:31.950274","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:16:31.955084","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:17:02.968818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:17:02.975933","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:17:03.177728","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:17:03.182380","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:19:10.895161","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:19:10.899517","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:19:11.035598","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:19:11.039396","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:19:42.138034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:19:42.146095","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:19:42.583710","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:19:42.588152","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:20:13.132360","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:20:13.144086","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:20:14.181764","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:20:14.213962","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:20:45.273879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:20:45.304396","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:20:45.795453","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:20:45.808070","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:21:16.198137","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:21:16.203220","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:21:16.442682","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:21:16.447248","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:21:47.789887","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:21:47.795554","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:21:48.329943","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:21:48.335482","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:22:19.313279","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:22:19.317412","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:22:19.472937","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:22:19.477562","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:22:50.537297","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:22:50.542372","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:22:50.705279","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:22:50.732051","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:23:21.076817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:23:21.082962","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:23:21.544218","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:23:21.548567","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:23:52.087438","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:23:52.092392","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:23:52.247716","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:23:52.252040","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:24:23.144132","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:24:23.148733","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:24:23.304400","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:24:23.308499","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:24:54.576983","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:24:54.581884","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:24:54.980609","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:24:54.985669","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:25:25.646399","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:25:25.663476","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:25:26.124185","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:25:26.132595","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:25:56.489438","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:25:56.495287","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:25:56.701839","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:25:56.707174","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:26:27.344307","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:26:27.354395","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:26:27.605166","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:26:27.613902","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:26:58.293699","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:26:58.300260","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:26:58.744780","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:26:58.749045","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:27:29.093719","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T06:27:29.098486","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:27:29.252500","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T06:27:29.256618","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:43:56.087001","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:43:56.091441","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:43:56.231011","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:43:56.234836","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:44:27.250418","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:44:27.255560","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:44:27.870438","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:44:27.875934","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:44:58.313867","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:44:58.319224","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:44:58.534676","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:44:58.542977","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:45:29.030202","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:45:29.034470","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:45:29.188930","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:45:29.193803","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:46:00.444267","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:46:00.449255","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:46:00.592052","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:46:00.596384","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:46:31.766671","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:46:31.771890","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:46:32.039261","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:46:32.044591","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:47:03.358268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:47:03.364536","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:47:03.507003","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:47:03.511354","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:47:34.168466","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:47:34.174842","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:47:34.341554","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:47:34.478672","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:48:05.476602","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:48:05.483492","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:48:05.800793","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:48:05.805758","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:48:36.983700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:48:36.991645","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:48:37.145922","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:48:37.488864","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:49:08.481072","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:49:08.486366","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:49:08.637602","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:49:08.641566","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:49:39.658168","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:49:39.663275","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:49:40.029546","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:49:40.034967","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:50:11.143784","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:50:11.149433","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:50:11.298625","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:50:11.303036","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:50:42.588582","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:50:42.595076","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:50:42.757682","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:50:42.763231","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:51:13.822678","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:51:13.829669","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:51:14.234438","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:51:14.239230","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:51:45.408017","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:51:45.413181","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:51:45.557813","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:51:45.562872","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:52:17.165456","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:52:17.173494","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:52:17.358905","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:52:17.501153","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:52:47.737559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:52:47.744156","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:52:47.917735","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:52:47.923169","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:53:19.446976","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:53:19.453986","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:53:19.626537","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:53:19.769542","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:53:50.596118","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:53:50.603384","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:53:50.890761","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:53:50.897494","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:54:21.910925","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:54:21.922087","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:54:22.311764","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:54:22.317286","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:54:53.482703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:54:53.487761","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:54:53.633660","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:54:53.637740","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:55:24.918919","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:55:24.924973","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:55:25.088543","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:55:25.095171","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:55:56.246091","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:55:56.250365","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:55:56.398188","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:55:56.403723","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:56:27.775989","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:56:27.780628","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:56:27.926379","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:56:27.930730","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:56:59.158103","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:56:59.164525","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:56:59.303078","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:56:59.307624","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:57:30.553143","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:57:30.559292","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:57:30.925090","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:57:31.048793","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:58:02.156493","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:58:02.163680","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:58:02.671770","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:58:02.677824","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:58:33.761791","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:58:33.770135","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:58:33.960071","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:58:34.105092","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:59:05.113594","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:59:05.118458","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:59:05.488121","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:59:05.629003","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:59:36.813792","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T11:59:36.820761","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:59:37.037114","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T11:59:37.048490","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:00:08.774112","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:00:08.780135","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:00:08.955901","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:00:09.089088","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:00:39.256331","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:00:39.261738","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:00:39.409534","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:00:39.807395","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:01:10.762829","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:01:10.769841","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:01:11.100524","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:01:11.107815","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:01:41.421996","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:01:41.432010","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:01:41.789868","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:01:41.797608","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:02:12.928574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:02:12.936165","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:02:13.503592","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:02:13.645169","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:02:44.638502","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:02:44.647534","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:02:44.809068","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:02:45.193664","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:03:16.120068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:03:16.124714","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:03:16.269971","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:03:16.276143","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:03:47.447092","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:03:47.458399","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:03:48.043888","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:03:48.050266","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:04:19.015252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:04:19.020931","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:04:19.185528","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:04:19.191351","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:04:49.636035","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:04:49.651428","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:04:50.135309","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:04:50.140392","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:05:21.464416","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:05:21.468429","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:05:21.826580","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:05:21.831142","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:05:54.084608","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:05:54.097519","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:05:54.529259","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:05:54.545884","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:06:25.181043","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:06:25.193494","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:06:25.480291","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:06:25.498403","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:06:56.177681","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:06:56.200244","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:06:56.535854","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:06:56.544773","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:07:27.740937","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:07:27.746491","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:07:27.951149","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:07:27.955978","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:07:59.043248","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:07:59.050699","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:07:59.225837","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:07:59.231222","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:08:29.629964","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:08:29.634328","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:08:30.051178","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:08:30.057003","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:09:00.333546","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:09:00.338029","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:09:00.505784","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:09:00.512066","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:09:31.197858","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:09:31.205862","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:09:31.416901","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:09:31.424359","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:10:01.989208","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:10:01.995867","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:10:02.478934","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:10:02.484719","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:10:32.909915","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:10:32.915909","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:10:33.086867","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:10:33.091637","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:11:03.800717","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:11:03.806585","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:11:04.010120","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:11:04.017352","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:11:34.546457","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:11:34.553557","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:11:35.070488","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:11:35.201842","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:12:05.419461","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:12:05.424097","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:12:05.595159","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:12:05.599170","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:12:36.829148","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T12:12:36.839122","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:12:37.494174","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T12:12:37.666920","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:19:40.307607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:19:40.314420","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:19:40.704645","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:19:40.708406","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:20:11.825939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:20:11.834781","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:20:12.013946","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:20:12.021429","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:20:42.405865","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:20:42.413569","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:20:42.570353","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:20:42.575270","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:21:12.897308","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:21:12.903393","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:21:13.082928","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:21:13.089089","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:21:43.682664","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:21:43.689172","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:21:43.856456","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:21:43.860632","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:22:14.367024","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:22:14.374751","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:22:14.553915","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:22:14.558771","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:22:44.717405","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:22:44.723597","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:22:45.145705","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:22:45.150265","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:23:15.341231","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:23:15.349068","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:23:15.520874","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:23:15.525681","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:23:45.908923","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:23:45.916674","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:23:46.086296","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:23:46.092186","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:24:16.287413","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:24:16.301201","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:24:16.961298","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:24:16.967376","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:24:47.244998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:24:47.252086","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:24:47.427564","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:24:47.432200","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:25:18.033601","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:25:18.044631","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:25:18.238324","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:25:18.244977","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:25:48.583009","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:25:48.595551","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:25:49.053631","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:25:49.057832","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:26:19.266619","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:26:19.272213","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:26:19.425000","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:26:19.429114","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:26:49.808156","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:26:49.814856","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:26:49.969021","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:26:49.973726","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:27:20.284782","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:27:20.290262","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:27:20.684699","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:27:20.688777","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:27:51.183117","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:27:51.190140","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:27:51.352390","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:27:51.358189","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:28:22.233109","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:28:22.239039","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:28:22.401230","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:28:22.405515","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:28:52.709326","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:28:52.717401","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:28:53.200850","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:28:53.206305","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:29:23.895757","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:29:23.903104","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:29:24.062978","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:29:24.067350","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:29:54.505913","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:29:54.510782","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:29:54.710627","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:29:54.720589","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:30:25.144665","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:30:25.155512","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:30:25.586439","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:30:25.591243","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:30:55.878124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:30:55.883442","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:30:56.033808","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:30:56.039279","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:31:26.469011","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:31:26.474919","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:31:26.642677","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:31:26.648431","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:31:56.914280","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:31:56.920123","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:31:57.357859","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:31:57.362029","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:32:27.838345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:32:27.845015","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:32:28.033838","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:32:28.038292","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:32:58.552571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:32:58.558057","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:32:58.716847","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:32:58.721106","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:33:29.054664","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:33:29.061496","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:33:29.470035","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:33:29.474575","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:33:59.977788","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:33:59.985066","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:34:00.172187","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:34:00.177163","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:34:30.653476","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:34:30.658478","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:34:30.808367","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:34:30.812421","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:35:01.160163","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:35:01.167891","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:35:01.554004","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:35:01.558692","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:35:31.975927","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:35:31.981718","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:35:32.136838","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:35:32.140944","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:36:02.721830","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:36:02.728567","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:36:02.920746","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:36:02.927498","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:36:33.489744","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:36:33.501813","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:36:34.023955","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:36:34.030344","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:37:04.362956","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:37:04.369097","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:37:04.539875","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:37:04.544198","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:37:35.165271","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:37:35.175695","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:37:35.377585","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:37:35.382392","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:38:05.545261","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:38:05.558514","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:38:06.093937","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:38:06.098993","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:38:37.570637","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:38:37.576602","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:38:37.753516","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:38:37.759081","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:39:08.253207","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:39:08.258237","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:39:08.466183","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:39:08.472362","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:39:38.949468","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:39:38.955124","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:39:39.473412","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:39:39.479828","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:40:10.087463","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:40:10.095807","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:40:10.280713","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:40:10.286750","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:40:40.563648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:40:40.568873","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:40:40.759822","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:40:40.765504","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:41:12.148268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:41:12.164876","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:41:12.698944","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:41:12.704594","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:41:43.281936","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:41:43.291737","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:41:43.461326","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:41:43.466880","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:42:14.078809","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:42:14.086461","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:42:14.321235","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:42:14.327741","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:42:44.519712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:42:44.526362","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:42:45.006032","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:42:45.012950","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:43:15.446857","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:43:15.465742","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:43:15.920226","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:43:15.967648","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:43:33.493425","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:43:33.499040","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:43:34.317545","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:43:34.322142","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:44:05.444822","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:44:05.470796","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:44:05.815767","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:44:05.820988","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:44:36.480517","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-07T13:44:36.498810","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:44:36.682905","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-07T13:44:36.687648","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
