{"timestamp":"2025-09-08T06:39:55.539571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:40:26.370227","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:40:58.029254","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:41:29.319730","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:42:00.578550","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:42:31.922782","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:43:02.603025","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:43:33.044942","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:44:03.188091","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:44:33.546332","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:45:04.440067","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:45:36.088027","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:46:06.561483","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:46:37.150795","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:47:07.526281","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:47:38.799515","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:48:09.211016","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:48:41.323537","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:49:12.153055","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:49:42.517086","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:50:12.998623","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:50:43.981885","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:51:14.739708","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:51:45.368736","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:52:16.120219","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:52:46.715889","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:53:17.761833","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:53:48.313087","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:54:18.463701","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:54:49.040782","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:55:20.326077","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:55:51.308269","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:56:22.219848","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:56:53.012784","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:57:23.884413","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:57:55.195727","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:58:26.297064","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:58:58.298696","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:59:28.940710","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:59:59.552690","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:00:30.589757","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:01:01.124398","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:01:32.142387","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:02:02.466131","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:02:33.017862","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:03:03.523016","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:03:33.940038","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:04:04.374040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:04:34.902234","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:05:05.465531","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:05:35.883590","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:06:06.944521","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:06:37.758667","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:07:08.420554","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:07:38.957303","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:08:09.867884","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:08:40.609706","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:09:10.719579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:09:41.125516","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:10:11.800456","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:10:42.507598","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:11:13.230009","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:11:43.877644","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:12:14.269923","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:12:44.570619","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:13:14.956333","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:13:46.093461","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:14:16.887133","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:14:47.472112","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:15:17.916949","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:15:48.285680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:16:18.770212","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:16:49.210259","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:17:19.676734","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:19:35.034784","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:20:06.467895","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:20:38.004229","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:21:09.137307","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:21:40.641790","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:22:11.839058","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:22:43.250121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:23:14.656640","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:23:45.847556","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:24:17.095964","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:24:48.364692","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:25:19.783344","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:25:51.033360","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:26:22.176079","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:26:53.555784","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:27:24.677396","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:27:55.849954","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:28:26.941972","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:28:58.102780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:29:29.496000","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:30:01.032227","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:30:32.420230","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:31:03.935150","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:31:35.316818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:32:06.592600","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:32:37.602533","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:33:09.007485","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:33:40.337629","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:34:11.808181","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:34:43.294464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:35:14.610052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:35:46.011589","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:36:17.387024","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:36:48.613075","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:37:19.842010","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:37:51.300065","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:38:22.824360","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:38:54.144644","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:39:25.301035","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:39:56.791971","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:40:27.988473","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:40:59.111189","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:41:30.201976","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:42:01.986742","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:42:33.298401","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:43:04.660409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:43:35.990464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:44:08.099079","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:44:38.408109","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:45:09.676233","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:45:41.048828","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:46:12.739513","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:46:43.111759","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:47:13.854841","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:47:45.196313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:48:16.756615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:48:47.823941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:49:19.318659","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:49:50.789615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:50:22.023150","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:50:53.485806","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:51:24.589231","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:51:55.779260","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:52:27.228657","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:52:58.401307","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:53:29.881499","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:54:01.427549","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:54:32.698084","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:55:04.365343","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:55:36.792896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:56:08.446799","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:56:39.856384","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:57:11.320126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:57:42.974894","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:58:14.701906","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:58:44.945312","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:59:16.311588","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:59:48.034747","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:00:19.073356","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:00:50.596345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:01:21.815397","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:01:53.106828","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:02:24.586938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:02:56.386691","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:03:26.831893","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:03:58.215427","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:04:29.875891","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:05:00.994720","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:05:31.667071","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:06:03.128221","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:06:34.470527","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:07:05.807584","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:07:37.541211","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:08:08.928501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:08:40.389926","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:09:11.667488","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:09:43.061507","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:10:14.516977","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:10:45.879954","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:11:17.114675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:11:48.495343","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:12:19.790489","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:12:51.312075","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:13:22.509620","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:13:54.011639","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:14:25.467187","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:14:57.028528","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:15:28.168130","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:15:59.801982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:16:31.321712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:17:02.970560","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:17:34.199861","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:18:05.826843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:18:37.362296","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:19:08.853284","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:19:40.169744","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:20:11.801691","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:20:54.197694","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:21:26.075452","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:21:57.707772","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:22:29.615028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:23:01.354499","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:23:32.929089","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:24:04.278002","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:24:36.149805","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:25:07.903240","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:25:39.430427","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:26:10.752229","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:26:42.231767","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:27:13.955361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:27:45.578794","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:28:16.833326","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:28:48.584198","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:47:52.923425","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:48:24.923355","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:48:55.168561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:49:26.614700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:49:58.155775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:50:29.798985","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:51:01.051895","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:51:32.576026","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:52:03.499304","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:52:34.934494","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:53:06.100364","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:53:37.654010","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:54:09.081664","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:54:41.319827","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:55:11.914433","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:55:42.565049","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:56:14.123315","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:56:45.966966","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:57:17.166227","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:57:48.832293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:58:20.258703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:58:51.887814","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:59:23.418052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:59:54.802299","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:00:26.304938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:00:57.716402","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:01:28.916881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:02:00.361871","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:02:31.793604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:03:03.559025","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:03:34.064272","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:04:05.630221","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:04:37.477923","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:05:09.166015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:05:40.161570","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:06:12.396156","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:06:43.960434","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:07:15.628544","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:07:45.911584","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:08:33.335669","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:09:05.081441","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:09:36.789928","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:10:08.637195","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:10:40.414356","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:11:12.079337","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:11:43.579233","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:12:14.972003","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:12:46.539378","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:13:18.482910","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:13:49.732280","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:14:21.377529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:14:52.936368","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:15:24.472893","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:15:55.663812","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:16:27.100699","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:16:58.592248","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:17:29.909412","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:18:01.574435","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:18:33.397395","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:19:04.528761","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:19:36.250534","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:20:07.642695","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:20:39.061041","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:21:10.776513","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:21:42.416989","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:22:13.773017","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:22:45.229748","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:23:16.428143","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:23:48.325019","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:24:19.912182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:24:51.392561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:25:23.733144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:25:54.099067","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:26:25.520869","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:26:56.730943","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:27:28.297451","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:27:59.731408","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:28:31.289795","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:29:02.434786","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:29:34.139435","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:30:05.691628","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:30:37.410997","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:31:08.756094","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:31:46.068233","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:32:30.359737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:33:02.528108","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:33:33.067807","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:34:04.129770","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:34:35.379157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:35:06.630920","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:35:38.535964","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:36:10.301768","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:36:41.912377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:37:12.961397","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:37:45.059446","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:38:15.733702","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:38:47.575481","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:39:19.076384","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:39:50.099254","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:40:21.735816","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:40:52.888719","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:41:24.599425","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:41:56.121593","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:42:26.723153","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:42:59.096269","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:43:30.277717","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:44:00.999658","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:44:33.006523","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:45:03.389086","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:45:34.743818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:46:05.031570","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:46:35.820758","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:47:07.919135","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:47:38.500920","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:48:09.967606","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:48:41.823199","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:49:12.900369","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:49:44.261063","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:50:16.048903","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:50:47.549301","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:51:18.794702","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:51:50.687433","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:52:22.456903","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:52:53.660365","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:53:25.264189","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:53:57.097931","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:54:27.958990","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:54:59.408126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:55:31.073790","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:56:02.907513","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:56:34.444336","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:57:06.021988","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:57:37.714867","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:58:08.819571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:58:40.231330","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:59:14.755152","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:59:46.364464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:00:17.629123","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:00:48.785846","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:01:20.254224","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:01:51.915068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:02:23.264712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:02:54.752356","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:03:26.249623","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:03:57.933602","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:04:29.981556","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:05:00.805443","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:05:32.776042","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:06:03.999547","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:06:35.480397","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:07:07.038851","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:09:46.668481","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:10:18.213733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:10:49.820937","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:11:21.456281","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:11:52.073262","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:12:23.865391","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:12:55.534578","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:13:26.802089","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:13:58.955795","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:14:29.788464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:15:00.951414","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:15:32.540024","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:16:03.855639","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:16:35.936171","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:17:06.244238","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:17:38.027785","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:18:09.788123","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:18:41.398580","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:19:13.479853","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:19:45.320217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:20:17.129182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:20:48.489675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:21:19.912754","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:21:51.357766","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:22:22.470768","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:22:54.104421","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:23:25.619186","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:23:57.131130","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:24:28.887459","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:25:00.802572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:25:31.544280","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:26:02.153107","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:26:33.964367","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:27:05.586375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:27:37.161667","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:28:08.657813","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:28:40.150234","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:29:11.667336","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:29:42.838076","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:30:14.395534","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:30:46.119015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:31:17.406368","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:31:48.902975","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:32:20.406112","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:32:51.731737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:33:23.127090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:33:54.140766","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:34:25.760514","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:34:56.030072","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:35:27.443756","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:35:58.882189","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:36:30.255534","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:37:01.865161","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:37:33.363164","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:38:04.614226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:38:36.599610","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:39:07.705730","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:39:40.015703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:40:11.618450","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:40:43.161135","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:41:14.038252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:41:44.916590","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:42:15.489469","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:42:46.567304","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:43:16.923616","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:43:47.279061","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:44:18.166060","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:44:49.470833","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:45:20.165249","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:45:51.110285","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:46:22.036831","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:46:52.658749","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:47:24.207382","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:47:54.950140","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:48:25.087995","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:48:55.859102","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:49:26.756445","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:49:57.788506","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:50:28.755556","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:51:00.516256","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:51:31.444795","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:52:02.334311","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:52:33.111118","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:53:04.145370","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:53:35.968438","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:54:06.492596","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:54:37.789136","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:55:08.393527","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:55:38.744331","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:56:09.815697","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:56:40.643015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:57:10.800364","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:57:41.843270","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:58:12.757083","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:58:42.911637","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:59:13.783305","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:59:44.707531","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:00:15.552188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:00:46.706242","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:01:17.734459","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:01:48.494635","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:02:19.469503","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:02:49.930042","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:03:20.754298","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:03:51.741581","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:04:23.011139","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:04:53.799090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:05:24.638820","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:05:54.885576","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:06:25.090054","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:06:55.589438","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:07:26.495727","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:07:57.013613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:08:28.084357","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:08:58.844022","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:09:29.631972","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:09:59.946128","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:10:30.934268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:11:01.988792","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:11:32.887034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:12:03.113905","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:12:33.906241","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:13:04.121817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:13:34.436910","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:14:05.143360","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:14:36.152812","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:15:07.241375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:15:38.205228","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:16:08.554352","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:16:39.355377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:17:09.541666","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:17:40.418858","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:18:10.868369","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:18:42.397573","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:19:13.437648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:19:44.127482","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:20:14.640157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:20:45.678083","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:21:16.995630","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:21:47.570377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:22:18.007877","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:22:48.454852","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:23:19.487008","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:23:49.892141","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:24:20.132772","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:24:50.898430","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:25:21.303575","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:25:51.981415","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:26:22.786973","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:26:53.719328","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:27:24.153766","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:27:54.819615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:28:25.763463","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:28:56.408082","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:29:27.203709","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:29:59.046819","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:30:29.985543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:31:00.513959","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:31:30.820858","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:32:01.918516","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:32:32.629207","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:33:03.452475","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:33:34.296283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:34:05.458361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:34:35.981842","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:35:06.669350","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:35:37.640612","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:36:09.473173","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:36:40.969888","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:37:12.455351","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:37:44.117428","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:38:15.842742","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:38:47.570634","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:39:18.415751","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:39:50.457503","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:40:22.272331","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:40:53.333576","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:41:24.876964","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:41:56.743105","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:42:28.182980","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:42:59.322574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:43:30.842109","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:44:02.408060","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:44:34.034262","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:45:05.864775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:45:37.036506","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:46:07.823717","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:46:39.429819","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:47:11.641499","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:47:42.428970","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:48:14.086065","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:48:44.835000","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:49:16.088999","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:49:47.824985","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:50:19.341574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:50:50.939785","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:51:22.050604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:51:52.968333","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:52:24.756194","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:52:56.444652","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:53:28.047161","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:53:59.814487","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:54:31.300207","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:55:02.447310","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:55:34.137252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:56:05.881874","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:56:37.061491","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:57:08.545658","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:57:40.382660","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:58:11.675737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:58:43.225663","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:59:14.877423","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:59:46.293096","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:00:17.844323","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:00:49.267040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:01:20.772236","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:01:52.432020","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:02:23.888019","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:02:55.250932","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:03:26.211459","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:03:57.104963","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:04:28.069427","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:04:59.617391","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:05:31.374458","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:06:02.064963","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:06:33.639368","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:07:06.605465","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:07:38.006930","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:08:10.359364","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:08:42.571470","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:09:13.856627","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:09:47.085109","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:10:18.204724","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:10:49.715007","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:11:20.731101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:11:52.076080","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:12:22.685407","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:12:54.384770","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:13:25.959609","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:13:57.238123","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:14:28.791129","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:15:00.386395","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:15:31.340493","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:16:03.158093","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:16:34.496529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:17:31.508493","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:18:21.055480","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:18:55.032122","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:19:28.080787","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:19:59.144311","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:20:30.530490","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:21:02.236275","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:21:33.883897","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:22:05.426623","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:22:38.714093","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:23:11.192633","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:23:42.236879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:24:12.894210","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:24:43.989493","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:18:40.706582","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:19:12.408056","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:19:44.273158","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:20:15.794968","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:20:47.684020","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:21:19.118811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:21:50.751487","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:22:22.462236","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:22:53.947140","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:23:25.703437","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:23:57.536829","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:24:29.004656","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:25:00.633120","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:25:32.981960","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:26:03.848377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:26:34.850910","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:27:05.738620","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:27:36.634474","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:28:08.517341","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:28:39.764435","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:29:11.202596","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:29:43.371910","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:30:15.313440","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:30:46.278631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:31:18.155249","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:31:50.030047","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:32:21.008742","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:32:52.742285","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:33:23.882503","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:33:56.180443","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:34:27.286193","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:34:58.613208","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:35:30.245289","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:36:02.021901","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:36:34.505194","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:37:05.401772","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:37:36.666750","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:38:08.825136","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:38:39.622245","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:39:12.177840","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:39:43.793153","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:40:14.880410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:40:45.408167","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:41:17.134329","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:41:48.896235","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:42:20.270284","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:42:51.920289","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:43:23.796058","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:43:55.199567","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:44:26.281842","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:44:57.945580","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:45:29.437329","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:46:00.518100","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:46:32.375744","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:47:03.984934","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:47:35.935891","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:48:07.802549","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:48:39.611217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:49:10.647273","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:49:42.642131","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:50:14.225915","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:50:45.999805","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:51:17.943779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:51:50.254261","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:52:21.578175","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:52:52.450312","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:53:24.089946","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:53:56.031275","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:54:26.643090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:54:58.445250","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:55:29.338523","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:56:00.153903","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:56:31.510993","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:57:03.714726","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:57:35.201097","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:58:07.244616","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:58:38.979641","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:59:09.758622","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:59:41.521064","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:00:13.138844","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:00:45.083672","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:01:16.172978","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:01:47.709194","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:02:19.345940","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:02:51.349449","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:03:22.204684","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:03:54.005529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:04:25.100936","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:04:56.666356","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:05:27.908186","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:05:58.944775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:06:30.800929","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:07:02.371078","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:07:33.055812","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:08:04.853441","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:08:36.519730","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:09:08.055355","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:09:39.842755","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:10:10.569775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:10:42.232764","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:11:13.331771","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:11:44.832968","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:12:16.810944","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:12:48.697449","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:13:20.436917","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:13:51.275117","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:14:23.280180","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:14:55.548298","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:15:26.711984","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:15:58.460571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:16:29.600790","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:17:01.752279","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:17:31.992643","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:18:03.992795","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:18:35.652211","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:19:06.631785","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:19:37.565129","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:20:09.418208","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:20:40.596250","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:21:12.206456","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:21:42.851872","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:22:14.814302","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:22:45.725545","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:23:16.765399","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:23:48.623028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:24:20.270288","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:24:51.630687","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:25:23.370624","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:25:55.903175","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:26:27.462375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:26:59.285849","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:27:30.380314","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:28:02.363847","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:28:33.950109","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:29:04.708208","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:29:35.630254","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:30:07.147614","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:30:38.731284","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:31:11.737951","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:31:42.635301","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:32:14.435993","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:32:45.638224","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
