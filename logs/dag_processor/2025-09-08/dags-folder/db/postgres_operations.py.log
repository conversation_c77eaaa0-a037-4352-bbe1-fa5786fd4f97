{"timestamp":"2025-09-08T06:39:55.991947","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:40:26.474447","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:40:58.102087","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:41:29.368254","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:42:00.643651","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:42:31.977710","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:43:02.656211","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:43:33.170413","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:44:03.261891","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:44:33.616211","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:45:04.496914","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:45:34.968372","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:46:05.479955","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:46:36.090530","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:47:07.483740","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:47:38.855486","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:48:09.268450","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:48:40.162841","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:49:11.092431","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:49:41.446127","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:50:11.928184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:50:42.899186","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:51:13.620454","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:51:44.110174","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:52:15.003911","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:52:45.613261","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:53:16.668728","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:53:47.153483","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:54:17.313412","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:54:47.940807","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:55:19.018462","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:55:49.206865","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:56:20.032049","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:56:50.882407","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:57:21.787336","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:57:52.803669","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:58:23.179254","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:58:54.070166","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:59:24.518867","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:59:55.282811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:00:26.352126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:00:56.887875","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:01:27.743111","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:01:58.241410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:02:28.809450","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:02:59.292649","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:03:29.727481","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:04:00.184874","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:04:30.539287","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:05:01.259072","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:05:31.491707","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:06:01.626763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:06:32.251334","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:07:03.069318","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:07:33.689975","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:08:04.618461","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:08:35.301721","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:09:05.440962","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:09:35.969003","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:10:06.455765","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:10:37.253984","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:11:08.023506","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:11:38.691957","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:12:09.079692","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:12:40.050985","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:13:10.795802","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:13:41.740533","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:14:12.796555","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:14:43.353459","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:15:13.811127","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:15:44.180295","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:16:14.680446","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:16:45.082874","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:17:15.559476","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:19:35.101415","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:20:06.543275","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:20:38.082847","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:21:09.202724","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:21:40.653343","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:22:11.907656","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:22:43.311564","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:23:14.744868","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:23:45.925097","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:24:17.171400","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:24:48.440455","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:25:19.848683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:25:51.105778","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:26:22.241425","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:26:53.622908","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:27:24.743074","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:27:55.915325","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:28:27.005795","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:28:58.163640","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:29:29.560357","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:30:01.104166","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:30:32.482927","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:31:04.010312","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:31:35.379862","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:32:06.660898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:32:37.662642","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:33:09.076181","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:33:40.416287","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:34:11.897012","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:34:43.364662","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:35:14.706843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:35:46.057896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:36:17.423302","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:36:48.682579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:37:19.916154","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:37:51.369125","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:38:22.897384","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:38:54.229410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:39:25.374437","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:39:56.870234","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:40:28.049908","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:40:59.171780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:41:30.391073","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:42:02.069423","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:42:33.369874","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:43:04.736578","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:43:36.001203","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:44:06.952004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:44:38.368630","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:45:09.737261","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:45:41.114917","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:46:12.778251","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:46:43.167365","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:47:13.917831","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:47:45.260958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:48:16.797543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:48:47.894085","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:49:19.386864","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:49:50.865359","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:50:22.090540","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:50:53.557992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:51:24.654419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:51:55.850929","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:52:27.288996","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:52:58.463217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:53:29.891624","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:54:01.493317","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:54:32.761636","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:55:04.440220","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:55:36.863638","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:56:08.556743","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:56:39.952517","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:57:11.388513","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:57:42.987217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:58:13.575337","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:58:44.871598","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:59:16.246419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:59:47.946539","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:00:19.150619","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:00:50.662228","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:01:21.881361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:01:53.171635","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:02:24.602441","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:02:55.243949","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:03:26.790731","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:03:58.281684","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:04:29.979841","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:05:01.260641","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:05:31.739886","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:06:03.206191","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:06:34.544794","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:07:05.881564","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:07:37.609152","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:08:09.007676","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:08:40.480847","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:09:11.753102","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:09:43.127479","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:10:14.585809","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:10:45.943021","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:11:17.197969","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:11:48.560038","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:12:19.874612","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:12:51.383643","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:13:22.608707","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:13:54.083364","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:14:25.537862","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:14:57.099217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:15:28.233540","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:15:59.892143","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:16:31.392231","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:17:03.034937","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:17:34.241362","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:18:05.910072","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:18:37.439826","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:19:08.946865","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:19:40.249872","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:20:12.313364","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:20:54.263201","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:21:26.156034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:21:57.789176","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:22:29.692802","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:23:01.432805","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:23:33.037023","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:24:04.405238","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:24:36.214751","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:25:08.010501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:25:39.504726","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:26:10.826293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:26:42.311877","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:27:14.040442","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:27:45.654605","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:28:16.908573","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:28:48.715346","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:47:53.029052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:48:25.010282","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:48:55.258995","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:49:26.704381","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:49:58.245281","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:50:29.876227","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:51:01.098234","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:51:32.652490","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:52:03.584383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:52:35.009709","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:53:06.184447","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:53:37.740048","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:54:09.120353","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:54:41.606383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:55:12.003584","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:55:42.646556","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:56:14.210029","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:56:46.010514","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:57:17.215119","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:57:48.911360","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:58:20.334839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:58:51.993047","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:59:23.461880","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:59:54.871278","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:00:26.384377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:00:57.792314","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:01:28.960467","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:02:00.437214","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:02:31.889664","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:03:03.650090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:03:34.135497","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:04:05.707436","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:04:37.578925","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:05:09.312252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:05:40.185437","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:06:12.480168","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:06:44.052782","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:07:15.714688","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:07:45.991054","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:08:33.411973","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:09:05.183757","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:09:36.872810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:10:08.737501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:10:40.498432","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:11:12.153833","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:11:43.675724","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:12:15.068343","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:12:46.624079","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:13:18.554517","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:13:49.826203","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:14:21.443659","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:14:53.013461","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:15:24.512881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:15:55.751549","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:16:27.184298","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:16:58.665350","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:17:29.990114","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:18:01.650931","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:18:33.471359","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:19:04.610956","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:19:36.159519","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:20:07.571376","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:20:39.131724","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:21:10.845012","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:21:42.501687","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:22:13.852271","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:22:45.269415","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:23:16.505819","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:23:48.412044","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:24:19.989696","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:24:51.471027","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:25:22.599527","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:25:54.046389","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:26:25.593401","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:26:56.832912","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:27:28.407206","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:27:59.812896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:28:31.389269","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:29:02.474028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:29:34.213795","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:30:05.751184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:30:37.480071","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:31:08.833213","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:31:46.173286","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:32:30.508671","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:33:02.553159","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:33:33.154144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:34:04.162812","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:34:35.476449","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:35:06.683821","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:35:38.656876","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:36:10.704595","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:36:41.957330","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:37:13.011893","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:37:45.188750","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:38:15.817425","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:38:47.721157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:39:19.156429","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:39:50.227439","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:40:21.874975","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:40:52.980548","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:41:24.678937","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:41:56.198152","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:42:26.814192","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:42:59.184151","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:43:30.409285","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:44:01.079293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:44:31.843562","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:45:03.522117","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:45:35.311644","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:46:06.445719","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:46:36.972913","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:47:07.927190","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:47:38.598774","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:48:10.050349","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:48:41.912866","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:49:12.986983","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:49:44.344834","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:50:16.135447","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:50:47.628236","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:51:18.873496","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:51:50.770175","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:52:22.582796","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:52:53.758524","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:53:25.347616","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:53:57.417162","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:54:28.041435","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:54:59.495940","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:55:31.148778","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:56:02.990652","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:56:34.532608","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:57:06.099313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:57:37.793419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:58:08.894085","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:58:40.285351","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:59:14.839338","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:59:46.440474","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:00:17.701530","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:00:48.862357","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:01:20.326296","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:01:52.009734","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:02:23.348679","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:02:54.859881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:03:26.331130","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:03:58.020892","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:04:30.100629","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:05:00.884821","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:05:32.828352","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:06:04.087551","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:06:35.566958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:07:07.120129","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:09:46.748111","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:10:18.296928","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:10:49.895538","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:11:21.543440","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:11:52.149535","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:12:23.969969","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:12:55.638185","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:13:26.888496","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:13:59.091019","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:14:29.875935","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:15:01.013065","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:15:32.618324","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:16:03.996459","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:16:36.064101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:17:06.337114","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:17:38.142349","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:18:09.874957","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:18:41.476466","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:19:13.571220","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:19:45.412718","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:20:17.156324","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:20:48.540849","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:21:20.001883","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:21:51.433201","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:22:22.519924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:22:54.200713","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:23:25.714434","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:23:57.210196","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:24:28.972327","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:25:00.911015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:25:31.695369","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:26:02.243093","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:26:34.072619","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:27:05.688914","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:27:37.236512","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:28:08.730323","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:28:40.224909","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:29:11.775375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:29:42.918642","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:30:14.440689","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:30:46.210449","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:31:17.486908","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:31:48.991217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:32:20.479047","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:32:51.775962","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:33:23.176813","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:33:54.220409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:34:25.840064","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:34:56.101433","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:35:27.519999","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:35:58.960312","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:36:30.345207","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:37:01.948036","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:37:33.442966","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:38:04.715232","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:38:36.692468","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:39:07.794383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:39:40.110760","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:40:11.671563","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:40:43.240260","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:41:14.128346","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:41:45.008603","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:42:15.546113","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:42:46.688357","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:43:17.005000","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:43:48.498718","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:44:19.292048","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:44:49.529579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:45:20.246794","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:45:52.377723","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:46:23.165032","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:46:53.785968","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:47:24.216462","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:47:55.036135","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:48:25.161340","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:48:55.941624","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:49:26.834148","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:49:58.914065","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:50:29.946789","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:51:00.525692","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:51:31.530134","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:52:02.426011","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:52:33.382186","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:53:05.275167","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:53:35.968646","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:54:06.492596","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:54:37.789136","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:55:08.495744","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:55:38.859013","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:56:09.911744","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:56:40.746060","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:57:10.882283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:57:41.916237","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:58:12.846732","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:58:43.007597","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:59:13.869656","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:59:44.814677","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:00:16.699447","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:00:46.847713","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:01:17.823051","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:01:48.578084","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:02:19.565003","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:02:51.109059","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:03:21.881294","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:03:52.928136","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:04:23.025920","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:04:53.799089","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:05:24.638820","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:05:54.936306","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:06:25.101076","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:06:55.589397","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:07:26.505427","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:07:57.025207","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:08:28.101619","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:08:58.853555","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:09:29.636854","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:09:59.954011","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:10:30.934268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:11:01.991775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:11:32.898589","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:12:03.113661","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:12:33.912892","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:13:04.131369","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:13:34.449363","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:14:05.207090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:14:36.168703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:15:07.241375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:15:38.222110","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:16:08.554379","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:16:39.355377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:17:09.501916","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:17:40.531303","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:18:11.994296","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:18:42.406923","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:19:13.436257","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:19:44.127772","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:20:14.644757","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:20:45.686712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:21:16.995113","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:21:47.578089","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:22:18.017783","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:22:48.491490","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:23:19.494744","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:23:49.914685","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:24:20.118639","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:24:50.989973","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:25:21.386535","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:25:52.070702","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:26:22.834239","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:26:53.909963","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:27:24.203510","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:27:55.976820","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:28:26.925307","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:28:57.579788","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:29:28.354786","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:29:59.056339","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:30:30.095769","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:31:00.643066","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:31:30.977221","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:32:02.038979","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:32:32.746933","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:33:03.611899","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:33:34.404174","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:34:05.600612","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:34:36.098448","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:35:06.761316","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:35:37.738302","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:36:09.562967","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:36:41.059434","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:37:12.546796","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:37:44.193659","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:38:15.947174","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:38:47.690117","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:39:18.459958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:39:50.577062","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:40:22.402262","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:40:53.272052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:41:24.956786","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:41:56.837434","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:42:28.272918","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:42:59.427247","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:43:30.933116","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:44:02.499715","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:44:34.123991","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:45:05.942411","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:45:37.124742","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:46:07.906651","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:46:39.631521","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:47:11.675880","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:47:42.473038","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:48:14.167891","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:48:44.933219","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:49:16.179581","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:49:47.947114","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:50:19.441132","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:50:51.050610","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:51:22.452258","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:51:53.054491","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:52:24.840188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:52:56.532985","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:53:28.155513","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:53:59.903956","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:54:31.393229","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:55:02.529445","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:55:34.220526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:56:05.960938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:56:37.121088","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:57:08.671236","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:57:40.462719","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:58:11.768552","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:58:43.333446","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:59:14.978411","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:59:46.382330","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:00:17.940732","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:00:49.378663","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:01:20.858971","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:01:52.510995","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:02:24.063784","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:02:55.350096","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:03:26.297726","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:03:57.179344","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:04:28.114607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:04:59.696291","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:05:31.462634","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:06:02.135478","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:06:33.718965","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:07:06.904574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:07:38.099087","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:08:10.459939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:08:42.669707","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:09:13.946440","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:09:47.144751","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:10:18.265734","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:10:49.788088","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:11:20.832750","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:11:52.314601","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:12:22.848185","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:12:54.469023","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:13:26.035452","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:13:57.330280","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:14:28.874970","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:15:00.459934","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:15:31.455126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:16:03.224102","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:16:34.582588","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:17:31.586481","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:18:21.573361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:18:55.390309","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:19:28.178103","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:19:59.233070","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:20:30.627737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:21:02.324275","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:21:33.965608","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:22:05.532764","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:22:38.775973","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:23:11.201448","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:23:42.267243","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:24:12.958700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:24:44.047212","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:18:40.802347","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:19:12.517680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:19:44.361909","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:20:15.880497","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:20:47.787914","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:21:19.208640","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:21:50.839095","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:22:22.588204","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:22:54.057028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:23:25.841433","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:23:57.642393","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:24:29.111497","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:25:00.713087","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:25:33.064629","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:26:03.969591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:26:34.956466","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:27:05.825063","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:27:36.675892","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:28:08.570861","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:28:39.846960","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:29:11.284762","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:29:43.455258","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:30:15.332801","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:30:46.323225","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:31:18.242740","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:31:50.106737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:32:21.079608","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:32:52.822814","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:33:23.905036","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:33:56.205816","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:34:27.337122","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:34:58.700913","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:35:30.379356","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:36:02.111113","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:36:34.585853","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:37:05.463725","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:37:36.787446","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:38:08.934631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:38:39.708267","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:39:12.871580","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:39:43.897231","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:40:14.983590","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:40:45.500483","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:41:17.239864","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:41:49.003098","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:42:20.365177","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:42:52.020633","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:43:23.886519","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:43:55.288198","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:44:26.374665","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:44:58.036846","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:45:29.532875","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:46:00.602744","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:46:32.431248","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:47:04.080331","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:47:36.041652","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:48:07.836114","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:48:39.677152","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:49:10.739959","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:49:42.731977","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:50:14.319527","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:50:46.081994","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:51:18.069737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:51:50.406336","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:52:21.704252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:52:52.539627","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:53:24.184566","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:53:56.134109","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:54:26.736220","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:54:58.533039","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:55:29.424653","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:56:00.234084","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:56:31.613162","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:57:03.808321","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:57:35.291737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:58:07.336361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:58:39.075882","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:59:09.892145","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:59:41.609319","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:00:13.222576","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:00:45.183004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:01:16.264666","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:01:47.805872","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:02:19.430571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:02:51.450427","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:03:22.310330","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:03:54.096539","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:04:25.200061","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:04:56.772224","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:05:29.000817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:06:00.127405","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:06:30.894437","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:07:02.711897","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:07:33.148902","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:08:04.940313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:08:36.635222","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:09:08.205311","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:09:39.926566","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:10:10.658185","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:10:42.321746","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:11:13.423479","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:11:44.916896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:12:16.919698","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:12:48.818840","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:13:20.587702","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:13:51.364775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:14:23.335988","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:14:55.630007","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:15:26.801537","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:15:58.586841","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:16:29.636998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:17:01.994696","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:17:32.074924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:18:04.085195","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:18:35.750485","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:19:06.723821","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:19:37.649644","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:20:09.542525","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:20:40.647674","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:21:12.300869","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:21:42.949847","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:22:14.907870","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:22:45.826900","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:23:16.888330","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:23:48.722474","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:24:20.366367","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:24:51.725799","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:25:23.465529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:25:56.037227","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:26:27.559472","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:26:59.375994","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:27:30.532467","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:28:02.881697","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:28:34.060175","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:29:04.827137","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:29:35.717176","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:30:07.249597","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:30:38.907819","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:31:11.874924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:31:42.908283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:32:14.535842","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:32:45.738163","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
