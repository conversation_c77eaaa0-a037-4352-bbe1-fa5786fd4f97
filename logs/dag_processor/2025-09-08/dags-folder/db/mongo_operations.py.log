{"timestamp":"2025-09-08T06:39:52.252921","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:40:23.519436","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:40:53.881583","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:41:24.660572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:41:54.887441","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:42:25.122834","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:42:55.681123","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:43:26.307362","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:43:56.933005","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:44:27.312665","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:44:57.971138","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:45:28.535236","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:45:58.947268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:46:29.666842","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:46:59.819732","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:47:30.221793","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:48:00.650603","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:48:31.610278","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:49:02.442471","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:49:32.939901","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:50:03.363455","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:50:34.248380","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:51:04.385599","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:51:34.693621","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:52:05.804597","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:52:36.368546","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:53:07.195728","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:53:37.883433","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:54:08.960471","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:54:39.452410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:55:10.712732","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:55:41.935873","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:56:12.831501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:56:43.618192","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:57:14.616801","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:57:44.882383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:58:15.657966","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:58:46.621431","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:59:17.324065","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:59:48.052780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:00:19.065045","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:00:49.720598","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:01:20.152171","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:01:51.028592","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:02:21.635556","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:02:52.094589","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:03:22.561123","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:03:52.961363","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:04:23.204938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:04:54.060983","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:05:24.652713","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:05:55.348376","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:06:26.036188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:06:56.688255","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:07:27.261558","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:07:59.420962","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:08:30.127503","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:09:00.249378","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:09:30.818209","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:10:01.064359","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:10:32.037118","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:11:02.867807","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:11:33.470122","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:12:03.899561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:12:34.623894","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:13:05.621237","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:13:36.404732","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:14:06.635972","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:14:37.193971","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:15:07.697387","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:15:38.019099","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:16:08.535371","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:16:38.887677","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:17:09.355241","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:19:34.967734","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:20:06.392268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:20:37.915303","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:21:09.067409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:21:40.460481","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:22:11.768396","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:22:43.164249","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:23:14.574199","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:23:45.764061","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:24:16.995660","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:24:48.283554","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:25:19.714760","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:25:50.947028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:26:22.104196","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:26:53.479264","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:27:24.608138","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:27:55.761132","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:28:26.836522","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:28:58.007220","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:29:29.409877","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:30:00.957965","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:30:32.194530","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:31:03.855939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:31:35.214398","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:32:06.513638","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:32:37.528217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:33:08.927468","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:33:40.252811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:34:11.697660","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:34:43.207148","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:35:14.510568","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:35:45.914172","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:36:17.277963","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:36:48.540816","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:37:19.746358","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:37:51.210478","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:38:22.742125","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:38:54.021540","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:39:25.231825","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:39:56.711895","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:40:27.920741","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:40:59.042247","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:41:30.123830","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:42:01.888105","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:42:33.219106","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:43:04.584769","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:43:35.683246","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:44:06.742421","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:44:38.290161","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:45:09.600188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:45:40.950935","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:46:12.368807","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:46:43.029351","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:47:13.769484","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:47:45.108893","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:48:16.648110","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:48:47.740636","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:49:19.228762","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:49:50.689503","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:50:21.943802","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:50:53.408374","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:51:24.502256","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:51:55.701588","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:52:27.147761","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:52:58.328199","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:53:29.562476","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:54:01.354293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:54:32.624782","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:55:04.282771","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:55:36.692938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:56:08.352456","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:56:39.736174","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:57:11.225468","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:57:42.605573","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:58:13.476088","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:58:44.777774","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:59:16.163905","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:59:47.841204","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:00:19.000024","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:00:50.504513","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:01:21.744589","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:01:53.007155","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:02:24.193361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:02:55.169016","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:03:26.708376","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:03:58.136149","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:04:29.759493","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:05:00.723508","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:05:31.592914","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:06:03.047814","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:06:34.392398","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:07:05.722186","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:07:37.460193","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:08:08.848869","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:08:40.282777","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:09:11.524810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:09:42.964545","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:10:14.433778","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:10:45.793672","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:11:17.018645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:11:48.407774","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:12:19.705005","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:12:51.218793","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:13:22.431705","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:13:53.926730","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:14:25.383101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:14:56.914811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:15:28.060579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:15:59.689148","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:16:31.224365","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:17:02.876219","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:17:34.089947","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:18:05.689625","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:18:37.254562","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:19:08.727844","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:19:40.077877","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:20:11.685391","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:20:54.081664","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:21:25.934777","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:21:57.610086","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:22:29.518286","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:23:01.251025","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:23:32.823509","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:24:04.141386","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:24:36.052721","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:25:07.762312","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:25:39.330623","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:26:10.649102","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:26:42.118439","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:27:13.850082","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:27:45.469822","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:28:16.746922","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:28:48.442305","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:47:52.818954","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:48:24.660279","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:48:55.081154","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:49:26.511213","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:49:58.033324","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:50:29.689019","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:51:00.931094","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:51:32.472522","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:52:03.367390","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:52:34.844491","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:53:06.009098","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:53:37.546528","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:54:08.953708","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:54:40.974501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:55:11.800139","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:55:42.472754","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:56:14.028806","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:56:45.838511","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:57:17.041787","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:57:48.717499","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:58:20.168101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:58:51.780775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:59:23.286086","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:59:54.700920","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:00:26.208534","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:00:57.623010","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:01:28.802602","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:02:00.259704","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:02:31.668127","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:03:03.373936","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:03:33.965511","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:04:05.525838","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:04:37.362085","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:05:08.845841","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:05:40.087785","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:06:12.271610","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:06:43.840608","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:07:15.382182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:07:45.812068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:08:33.230571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:09:04.968871","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:09:36.690577","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:10:08.456038","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:10:40.302552","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:11:11.968563","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:11:43.454166","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:12:14.859214","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:12:46.419414","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:13:18.381754","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:13:49.607792","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:14:21.267173","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:14:52.803434","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:15:24.352263","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:15:55.578404","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:16:27.001010","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:16:58.503287","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:17:29.799751","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:18:01.470157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:18:33.296255","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:19:04.446065","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:19:36.067388","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:20:07.463599","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:20:38.965701","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:21:10.651060","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:21:42.323336","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:22:13.687248","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:22:45.110691","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:23:16.328814","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:23:48.229195","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:24:19.815076","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:24:51.299480","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:25:22.499297","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:25:53.956586","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:26:25.427500","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:26:56.644950","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:27:28.187630","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:27:59.635297","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:28:31.193576","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:29:02.322114","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:29:34.031849","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:30:05.551044","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:30:37.247695","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:31:08.669437","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:31:45.951483","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:32:30.176069","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:33:02.208235","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:33:32.938754","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:34:04.024845","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:34:35.245742","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:35:06.400111","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:35:38.388063","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:36:09.998342","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:36:41.859916","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:37:12.913789","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:37:44.873647","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:38:15.623408","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:38:47.473427","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:39:18.897156","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:39:49.973940","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:40:21.631144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:40:52.778066","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:41:24.499361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:41:56.020005","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:42:26.637096","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:42:58.986507","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:43:30.145990","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:44:00.870635","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:44:31.735281","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:45:03.109970","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:45:34.200927","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:46:04.864890","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:46:35.709294","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:47:06.587868","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:47:38.378895","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:48:09.870846","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:48:41.709288","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:49:12.791860","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:49:44.150692","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:50:15.947822","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:50:47.453794","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:51:18.695677","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:51:50.576626","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:52:22.323859","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:52:53.545488","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:53:25.158537","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:53:56.972843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:54:27.856431","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:54:59.301574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:55:30.968003","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:56:02.804249","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:56:34.345518","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:57:05.918569","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:57:37.606141","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:58:08.720233","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:58:40.094889","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:59:14.608982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:59:46.270168","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:00:17.536890","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:00:48.694596","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:01:20.148911","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:01:51.780772","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:02:23.159675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:02:54.654559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:03:26.166881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:03:57.825424","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:04:29.737449","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:05:00.633785","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:05:31.986419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:06:03.902661","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:06:35.372383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:07:06.941172","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:09:46.557170","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:10:18.102645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:10:49.723542","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:11:21.353313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:11:51.966929","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:12:23.756012","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:12:55.384616","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:13:26.685341","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:13:58.474719","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:14:29.698983","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:15:00.822539","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:15:32.442639","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:16:03.729191","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:16:35.309309","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:17:06.148509","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:17:37.903594","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:18:09.678571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:18:41.296257","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:19:13.351580","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:19:45.197413","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:20:16.616239","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:20:48.402516","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:21:19.832354","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:21:51.253935","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:22:22.350078","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:22:54.001373","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:23:25.513990","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:23:57.024176","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:24:28.782428","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:25:00.432661","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:25:31.218713","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:26:02.041554","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:26:33.805548","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:27:05.466800","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:27:37.003578","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:28:08.524969","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:28:40.047338","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:29:11.234125","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:29:42.739139","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:30:14.272344","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:30:46.007421","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:31:17.303283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:31:48.776540","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:32:20.305689","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:32:51.607819","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:33:23.001047","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:33:54.037609","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:34:25.635255","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:34:55.917499","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:35:27.347772","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:35:58.785685","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:36:30.145189","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:37:01.759750","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:37:33.256972","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:38:04.518491","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:38:36.494667","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:39:07.598014","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:39:39.896267","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:40:11.460168","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:40:43.061855","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:41:13.919984","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:41:44.324972","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:42:15.349991","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:42:46.433684","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:43:16.809894","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:43:47.170904","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:44:18.070105","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:44:49.326194","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:45:20.056049","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:45:50.965296","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:46:21.927402","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:46:52.553962","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:47:23.011361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:47:53.808502","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:48:24.954377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:48:55.749601","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:49:26.663775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:49:57.650470","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:50:28.633484","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:50:59.291250","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:51:30.293517","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:52:01.192839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:52:31.649308","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:53:03.003771","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:53:33.261922","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:54:04.174640","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:54:35.738484","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:55:06.186952","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:55:36.401134","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:56:06.623153","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:56:37.429630","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:57:07.590272","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:57:39.641262","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:58:10.472617","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:58:40.749717","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:59:11.644039","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:59:42.565551","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:00:13.354048","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:00:43.482584","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:01:14.534763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:01:45.294784","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:02:16.305579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:02:46.711425","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:03:17.528584","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:03:48.532684","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:04:18.696898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:04:50.413973","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:05:21.296737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:05:51.589824","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:06:21.750739","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:06:52.198373","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:07:23.211264","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:07:53.661445","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:08:24.755268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:08:55.568054","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:09:26.373189","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:09:56.629471","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:10:27.619453","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:10:58.617436","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:11:29.652328","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:11:59.802344","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:12:30.598624","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:13:00.851646","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:13:31.154911","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:14:02.280998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:14:32.753501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:15:03.929605","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:15:34.842853","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:16:05.138015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:16:35.945186","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:17:07.025162","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:17:37.229071","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:18:07.710189","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:18:38.080124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:19:10.107379","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:19:41.335591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:20:12.319834","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:20:43.397200","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:21:14.377006","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:21:45.318712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:22:15.717115","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:22:45.974129","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:23:16.496800","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:23:47.439941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:24:17.959710","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:24:49.355280","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:25:19.936237","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:25:50.629427","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:26:21.046577","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:26:51.778648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:27:22.824967","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:27:53.445735","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:28:24.037591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:28:54.970916","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:29:25.855340","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:29:56.301430","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:30:27.519147","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:30:57.930040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:31:28.202113","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:31:59.711299","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:32:30.251954","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:33:02.116144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:33:33.141323","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:34:04.100487","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:34:35.781498","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:35:06.530314","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:35:37.525307","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:36:09.364199","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:36:40.812890","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:37:12.347284","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:37:43.991676","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:38:15.722219","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:38:47.149212","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:39:18.319159","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:39:50.333050","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:40:21.835794","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:40:53.159965","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:41:24.771874","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:41:56.630336","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:42:28.076026","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:42:59.191428","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:43:30.731280","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:44:02.297611","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:44:33.930230","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:45:05.751969","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:45:36.900669","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:46:07.716039","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:46:39.182267","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:47:11.556400","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:47:42.333219","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:48:13.971390","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:48:44.719538","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:49:15.978220","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:49:47.678907","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:50:19.215538","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:50:50.804805","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:51:21.720542","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:51:52.846799","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:52:24.583960","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:52:56.313677","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:53:27.933783","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:53:59.694065","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:54:31.192158","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:55:02.343439","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:55:34.031789","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:56:05.475471","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:56:36.961067","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:57:08.420540","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:57:40.253068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:58:11.579631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:58:43.102743","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:59:14.756313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:59:46.156746","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:00:17.731761","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:00:49.038920","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:01:20.663245","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:01:52.315453","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:02:23.462786","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:02:55.089241","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:03:26.092351","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:03:56.651705","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:04:27.981181","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:04:59.511537","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:05:30.808873","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:06:01.959409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:06:33.522923","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:07:06.387841","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:07:37.829053","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:08:10.237626","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:08:42.442601","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:09:13.758052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:09:46.862573","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:10:18.056934","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:10:49.528822","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:11:20.473873","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:11:51.892933","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:12:22.540938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:12:54.226839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:13:25.840229","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:13:57.131216","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:14:28.676770","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:15:00.269478","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:15:31.217943","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:16:03.016818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:16:34.370204","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:17:31.307650","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:18:20.497947","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:18:54.525336","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:19:27.874657","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:19:59.014162","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:20:30.418494","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:21:02.114927","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:21:33.523408","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:22:05.314111","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:22:38.529529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:23:09.962903","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:23:42.128961","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:24:12.765034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:24:43.892520","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:18:40.578678","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:19:12.266138","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:19:44.162837","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:20:15.675543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:20:47.566814","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:21:19.004862","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:21:50.633211","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:22:22.342823","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:22:53.796552","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:23:25.566643","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:23:57.378672","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:24:28.859969","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:25:00.501319","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:25:32.858718","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:26:03.687234","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:26:34.715925","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:27:05.625112","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:27:36.487876","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:28:08.174087","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:28:39.656005","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:29:11.065342","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:29:43.230019","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:30:14.866600","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:30:46.176422","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:31:18.044492","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:31:49.438858","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:32:20.880219","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:32:52.625931","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:33:23.387075","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:33:56.016236","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:34:27.175517","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:34:58.499306","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:35:30.091987","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:36:01.895615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:36:34.271248","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:37:05.291057","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:37:36.557403","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:38:08.649183","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:38:39.486793","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:39:11.203609","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:39:42.645104","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:40:13.733886","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:40:45.289529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:41:17.015886","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:41:48.774673","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:42:20.140826","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:42:51.793377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:43:23.670299","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:43:55.079274","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:44:26.145915","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:44:57.826628","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:45:29.313582","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:46:00.393171","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:46:32.187029","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:47:03.827940","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:47:35.814549","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:48:07.319227","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:48:39.398668","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:49:10.522785","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:49:42.229362","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:50:14.125881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:50:45.875872","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:51:17.437164","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:51:50.053333","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:52:21.390224","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:52:52.327501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:53:23.960814","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:53:55.894618","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:54:26.510124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:54:58.332010","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:55:29.215718","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:55:59.974045","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:56:31.342103","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:57:03.587229","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:57:35.082921","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:58:07.105580","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:58:38.857095","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:59:09.630298","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:59:41.398701","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:00:12.964184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:00:44.947626","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:01:16.039084","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:01:47.579065","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:02:19.204106","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:02:51.211989","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:03:22.076416","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:03:53.851570","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:04:24.973758","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:04:56.553925","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:05:27.693596","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:05:58.799247","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:06:30.668901","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:07:02.242580","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:07:32.863065","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:08:04.721278","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:08:36.355364","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:09:07.908007","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:09:39.685974","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:10:10.431813","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:10:42.116305","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:11:13.187239","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:11:44.709905","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:12:16.666990","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:12:48.561737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:13:20.240080","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:13:51.162619","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:14:22.852818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:14:54.971499","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:15:26.591962","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:15:58.015000","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:16:29.431015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:17:01.119159","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:17:31.766300","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:18:03.876179","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:18:35.503422","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:19:06.497561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:19:37.438722","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:20:08.994557","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:20:40.498052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:21:12.090104","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:21:42.717248","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:22:14.676661","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:22:45.604969","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:23:16.645087","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:23:48.494476","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:24:20.146342","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:24:51.510895","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:25:23.245747","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:25:55.716830","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:26:27.338518","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:26:59.167806","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:27:30.173873","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:28:02.193066","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:28:33.833335","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:29:04.563466","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:29:35.491689","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:30:07.015493","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:30:38.607782","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:31:11.511789","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:31:42.464970","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:32:14.305006","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:32:45.516791","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/mongo_operations.py","logger":"airflow.models.dagbag.DagBag"}
