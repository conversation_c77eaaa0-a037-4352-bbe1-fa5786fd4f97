{"timestamp":"2025-09-08T06:39:55.480500","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:40:26.341190","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:40:57.988303","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:41:29.293595","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:42:00.550755","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:42:31.903230","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:43:02.418760","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:43:32.679460","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:44:03.175804","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:44:33.538460","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:45:04.218046","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:45:34.774502","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:46:05.235276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:46:35.902302","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:47:07.089029","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:47:38.434398","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:48:08.985248","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:48:39.916094","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:49:10.885664","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:49:41.234250","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:50:11.705936","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:50:42.516772","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:51:13.620455","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:51:44.108504","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:52:15.003911","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:52:45.610529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:53:16.668728","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:53:47.136635","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:54:17.313412","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:54:49.013680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:55:20.318158","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:55:51.295354","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:56:22.193555","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:56:52.999447","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:57:23.880040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:57:55.207411","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:58:26.287139","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:58:57.166764","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:59:27.622407","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:59:58.414747","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:00:29.441875","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:00:59.996142","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:01:30.817949","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:02:01.342351","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:02:31.928810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:03:02.399157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:03:32.822375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:04:03.261188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:04:33.643546","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:05:04.320094","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:05:34.677342","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:06:05.786549","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:06:36.611291","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:07:07.157781","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:07:37.805739","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:08:08.746578","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:08:39.458597","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:09:09.583902","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:09:41.122041","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:10:11.780817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:10:42.496860","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:11:13.223268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:11:43.874354","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:12:14.265201","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:12:44.502161","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:13:16.099820","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:13:46.255077","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:14:18.007817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:14:48.588194","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:15:19.031134","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:15:49.389038","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:16:19.885779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:16:50.460162","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:17:20.809919","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:19:35.031166","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:20:06.462992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:20:37.998621","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:21:09.127876","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:21:40.565135","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:22:11.831552","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:22:43.235668","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:23:14.642807","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:23:45.838210","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:24:17.077885","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:24:48.351176","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:25:19.771676","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:25:51.021660","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:26:22.167084","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:26:53.543228","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:27:24.668026","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:27:55.837759","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:28:26.905294","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:28:58.068882","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:29:29.485372","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:30:01.028011","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:30:32.361028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:31:03.925061","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:31:35.298670","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:32:06.581800","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:32:37.589663","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:33:08.995373","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:33:40.324012","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:34:11.783087","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:34:43.284627","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:35:14.582331","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:35:45.972480","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:36:17.349309","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:36:48.610243","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:37:19.833186","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:37:51.285548","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:38:22.810254","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:38:54.128001","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:39:25.297858","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:39:56.786147","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:40:27.978845","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:40:59.101257","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:41:30.195858","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:42:01.977000","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:42:33.286591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:43:04.657912","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:43:35.757605","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:44:06.830029","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:44:38.358901","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:45:09.664975","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:45:41.011532","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:46:12.544045","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:46:43.065855","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:47:13.840804","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:47:45.179108","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:48:16.719012","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:48:47.813702","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:49:19.296998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:49:50.767822","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:50:22.013171","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:50:53.474826","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:51:24.576153","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:51:55.767115","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:52:27.210240","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:52:58.387319","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:53:29.638828","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:54:01.415987","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:54:32.688205","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:55:04.352401","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:55:36.778391","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:56:08.430779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:56:39.856384","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:57:11.305672","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:57:42.691344","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:58:13.534558","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:58:44.856763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:59:16.235076","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:59:47.928578","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:00:19.069528","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:00:50.580646","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:01:21.811132","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:01:53.069993","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:02:24.273358","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:02:55.232310","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:03:26.777968","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:03:58.202610","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:04:29.862294","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:05:00.889573","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:05:31.663221","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:06:03.124148","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:06:34.459528","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:07:05.794351","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:07:37.530337","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:08:08.929068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:08:40.378023","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:09:11.650819","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:09:43.040841","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:10:14.505938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:10:45.864147","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:11:17.101049","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:11:48.480919","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:12:19.779487","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:12:51.299700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:13:22.506902","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:13:53.997554","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:14:25.453058","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:14:56.983485","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:15:28.130639","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:15:59.770804","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:16:31.305982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:17:02.953430","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:17:34.159848","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:18:05.808349","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:18:37.344261","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:19:08.818907","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:19:40.152822","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:20:11.783744","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:20:54.168776","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:21:26.052384","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:21:57.689023","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:22:29.600797","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:23:01.332799","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:23:32.915216","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:24:04.251166","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:24:36.125849","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:25:07.880755","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:25:39.414262","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:26:10.737957","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:26:42.212156","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:27:13.939640","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:27:45.556149","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:28:16.819797","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:28:48.560436","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:47:52.905826","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:48:24.914433","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:48:55.164977","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:49:26.597093","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:49:58.133162","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:50:29.777850","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:51:01.005974","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:51:32.556193","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:52:03.482949","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:52:34.921042","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:53:06.088064","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:53:37.637251","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:54:09.029922","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:54:41.313105","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:55:11.891751","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:55:42.551203","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:56:14.116091","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:56:45.916957","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:57:17.118746","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:57:48.807454","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:58:20.245685","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:58:51.870500","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:59:23.367895","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:59:54.779952","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:00:26.297795","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:00:57.708534","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:01:28.873460","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:02:00.345074","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:02:31.777345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:03:03.488099","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:03:34.044276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:04:05.623751","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:04:37.457392","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:05:09.042725","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:05:40.102361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:06:12.374922","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:06:43.946023","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:07:15.613884","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:07:45.893170","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:08:33.317820","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:09:05.064365","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:09:36.775423","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:10:08.616612","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:10:40.393085","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:11:12.055807","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:11:43.569505","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:12:14.949090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:12:46.523038","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:13:18.465533","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:13:49.714231","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:14:21.355534","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:14:52.917462","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:15:24.428592","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:15:55.655314","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:16:27.089581","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:16:58.580628","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:17:29.890992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:18:01.557097","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:18:33.381926","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:19:04.523430","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:19:36.143763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:20:07.557888","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:20:39.045802","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:21:10.742671","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:21:42.401975","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:22:13.758110","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:22:45.184515","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:23:16.411368","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:23:48.320695","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:24:19.895990","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:24:51.377487","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:25:22.583592","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:25:54.033196","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:26:25.507691","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:26:56.723927","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:27:28.289155","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:27:59.716401","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:28:31.273202","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:29:02.390521","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:29:34.128147","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:30:05.633345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:30:37.391536","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:31:08.749012","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:31:46.051850","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:32:30.326781","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:33:02.377432","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:33:33.041271","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:34:04.060971","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:34:35.356583","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:35:06.567450","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:35:38.517225","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:36:10.132325","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:36:41.788467","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:37:12.860766","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:37:45.031636","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:38:15.710330","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:38:47.557923","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:39:19.039099","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:39:50.073173","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:40:21.719144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:40:52.868041","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:41:24.580838","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:41:56.107794","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:42:26.720012","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:42:59.083362","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:43:30.265391","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:44:00.970092","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:44:31.828161","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:45:03.355735","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:45:34.651568","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:46:04.981952","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:46:35.803613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:47:06.731528","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:47:38.475103","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:48:09.951843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:48:41.806436","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:49:12.882463","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:49:44.249033","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:50:16.042391","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:50:47.536107","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:51:18.777711","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:51:50.668922","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:52:22.425886","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:52:53.640401","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:53:25.246944","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:53:57.074924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:54:27.941796","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:54:59.393129","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:55:31.053747","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:56:02.891122","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:56:34.432332","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:57:06.005027","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:57:37.684993","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:58:08.804986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:58:40.178124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:59:14.716257","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:59:46.349027","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:00:17.616382","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:00:48.775955","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:01:20.233082","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:01:51.894739","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:02:23.250002","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:02:54.744737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:03:26.244498","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:03:57.916768","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:04:29.947247","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:05:00.779090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:05:32.171000","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:06:03.985343","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:06:35.466515","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:07:07.029061","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:09:46.649331","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:10:18.197707","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:10:49.807034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:11:21.439295","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:11:52.054863","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:12:23.845098","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:12:55.503217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:13:26.778481","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:13:58.947224","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:14:29.773846","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:15:00.896881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:15:32.527953","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:16:03.808318","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:16:35.771439","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:17:06.227495","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:17:38.018286","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:18:09.769433","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:18:41.384200","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:19:13.457839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:19:45.293879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:20:16.750063","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:20:48.437472","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:21:19.909003","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:21:51.342980","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:22:22.425442","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:22:54.095098","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:23:25.601082","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:23:57.112330","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:24:28.872035","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:25:00.769090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:25:31.483232","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:26:02.134253","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:26:33.945207","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:27:05.560872","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:27:37.144417","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:28:08.638852","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:28:40.136238","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:29:11.330505","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:29:42.825952","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:30:14.349202","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:30:46.098817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:31:17.388383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:31:48.888466","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:32:20.391283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:32:51.685067","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:33:23.075964","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:33:54.121268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:34:25.743233","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:34:56.013072","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:35:27.429581","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:35:58.868404","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:36:30.237525","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:37:01.849333","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:37:33.339963","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:38:04.596039","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:38:36.583048","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:39:07.688449","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:39:40.019218","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:40:11.552805","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:40:43.146362","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:41:14.010729","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:41:44.895781","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:42:15.434999","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:42:46.535368","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:43:16.881281","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:43:47.240249","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:44:18.134820","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:44:49.403545","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:45:20.126715","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:45:51.040759","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:46:21.997050","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:46:52.620601","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:47:23.077489","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:47:54.930596","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:48:25.037622","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:48:55.823226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:49:26.724699","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:49:57.751994","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:50:28.719656","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:50:59.371737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:51:31.433968","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:52:02.314134","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:52:33.100978","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:53:04.133629","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:53:34.378015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:54:05.315564","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:54:35.946931","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:55:08.383993","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:55:38.744331","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:56:09.802845","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:56:40.623453","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:57:10.771207","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:57:41.805566","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:58:12.728772","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:58:42.891659","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:59:13.774922","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:59:44.707531","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:00:15.539409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:00:46.684859","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:01:17.727824","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:01:48.486678","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:02:19.469503","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:02:49.918534","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:03:20.744112","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:03:51.731267","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:04:21.879924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:04:52.608268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:05:23.487117","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:05:53.750162","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:06:23.927186","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:06:54.399841","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:07:25.353996","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:07:55.842613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:08:26.922918","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:08:57.721658","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:09:28.494645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:09:58.786044","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:10:29.773144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:11:00.815474","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:11:31.769703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:12:01.933736","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:12:32.777467","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:13:03.007963","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:13:33.292112","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:14:04.669964","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:14:34.938414","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:15:05.067539","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:15:36.042147","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:16:07.299849","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:16:38.185595","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:17:09.391915","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:17:40.416694","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:18:10.859459","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:18:41.254083","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:19:12.274392","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:19:42.646272","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:20:13.465162","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:20:44.510367","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:21:15.524418","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:21:46.429215","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:22:16.816132","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:22:47.298007","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:23:18.335530","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:23:48.745056","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:24:18.932662","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:24:50.885505","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:25:21.294899","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:25:51.969503","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:26:22.727637","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:26:53.481681","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:27:24.094174","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:27:54.743079","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:28:25.672382","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:28:56.348444","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:29:27.145818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:29:57.915908","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:30:29.971991","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:31:00.503245","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:31:30.809820","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:32:01.910126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:32:32.612670","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:33:03.430955","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:33:34.278253","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:34:05.455159","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:34:35.981842","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:35:06.644274","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:35:37.624810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:36:09.454891","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:36:40.949547","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:37:12.439506","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:37:44.088690","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:38:15.817837","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:38:47.264586","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:39:18.356583","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:39:50.442421","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:40:21.987142","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:40:53.251160","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:41:24.858862","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:41:56.721761","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:42:28.165218","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:42:59.297278","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:43:30.826311","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:44:02.388359","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:44:34.016737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:45:05.843182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:45:37.004004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:46:07.807276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:46:39.296876","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:47:11.455007","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:47:42.371087","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:48:14.070092","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:48:44.816570","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:49:16.078005","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:49:47.792709","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:50:19.323841","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:50:50.907225","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:51:22.035400","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:51:52.943407","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:52:24.732188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:52:56.421116","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:53:28.036641","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:53:59.798244","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:54:31.282119","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:55:02.429874","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:55:34.119847","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:56:05.606828","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:56:37.008583","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:57:08.475263","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:57:40.364571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:58:11.667701","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:58:43.204100","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:59:14.857714","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:59:46.249123","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:00:17.823538","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:00:49.155173","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:01:20.754824","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:01:52.414526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:02:23.756143","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:02:55.224444","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:03:26.193416","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:03:56.777574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:04:28.018172","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:04:59.601264","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:05:30.925813","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:06:02.018231","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:06:33.617622","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:07:06.578529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:07:37.961820","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:08:10.335035","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:08:42.542723","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:09:13.845107","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:09:47.001308","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:10:18.134072","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:10:49.635879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:11:20.685221","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:11:52.094589","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:12:22.652952","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:12:54.348944","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:13:25.933973","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:13:57.222279","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:14:28.770929","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:15:00.364474","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:15:31.323063","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:16:03.116827","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:16:34.477340","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:17:31.444337","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:18:20.970021","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:18:54.807327","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:19:28.028877","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:19:59.119989","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:20:30.513236","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:21:02.214448","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:21:33.635572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:22:05.409194","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:22:38.602956","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:23:10.402677","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:23:42.149889","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:24:12.831248","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:24:43.927179","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:18:40.683075","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:19:12.384584","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:19:44.253195","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:20:15.772526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:20:47.663571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:21:19.106982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:21:50.731684","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:22:22.449193","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:22:53.921988","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:23:25.678801","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:23:57.510655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:24:28.991733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:25:00.605281","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:25:32.962571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:26:03.814997","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:26:34.837750","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:27:05.716679","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:27:36.579281","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:28:08.429805","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:28:39.746695","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:29:11.178882","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:29:43.339794","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:30:14.989633","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:30:46.209335","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:31:18.128392","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:31:49.798050","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:32:20.926356","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:32:52.724491","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:33:23.503986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:33:56.071188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:34:27.220936","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:34:58.595846","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:35:30.222222","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:36:01.999281","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:36:34.381958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:37:05.330274","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:37:36.635054","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:38:08.808361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:38:39.603784","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:39:11.622323","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:39:43.792846","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:40:14.868804","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:40:45.389842","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:41:17.116101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:41:48.876170","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:42:20.231971","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:42:51.901290","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:43:23.770640","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:43:55.176540","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:44:26.258588","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:44:57.927006","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:45:29.411618","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:46:00.498733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:46:32.314300","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:47:03.954998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:47:35.922897","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:48:07.443765","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:48:39.539569","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:49:10.623886","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:49:42.382854","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:50:14.189701","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:50:45.975349","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:51:17.594232","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:51:50.190128","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:52:21.556563","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:52:52.424976","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:53:24.069610","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:53:56.002083","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:54:26.613785","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:54:58.430109","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:55:29.321060","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:56:00.096614","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:56:31.488934","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:57:03.689295","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:57:35.185189","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:58:07.216176","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:58:38.958636","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:59:09.732145","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:59:41.502003","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:00:13.090259","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:00:45.060151","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:01:16.147274","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:01:47.687846","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:02:19.309695","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:02:51.328541","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:03:22.184793","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:03:53.973056","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:04:25.078410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:04:56.657703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:05:27.835089","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:05:58.908989","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:06:30.774073","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:07:02.353314","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:07:33.010885","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:08:04.829035","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:08:36.489604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:09:08.024780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:09:39.811340","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:10:10.547679","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:10:42.216470","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:11:13.308895","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:11:44.814316","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:12:16.784492","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:12:48.676025","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:13:20.390455","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:13:51.257470","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:14:22.977604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:14:55.068097","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:15:26.692184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:15:58.165505","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:16:29.527491","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:17:01.756911","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:17:31.949856","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:18:03.976113","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:18:35.632969","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:19:06.612453","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:19:37.545344","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:20:09.131573","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:20:40.537146","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:21:12.191121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:21:42.822015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:22:14.791309","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:22:45.706212","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:23:16.746324","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:23:48.600698","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:24:20.246987","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:24:51.611691","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:25:23.348952","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:25:55.877011","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:26:27.440658","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:26:59.267328","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:27:30.357202","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:28:02.335466","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:28:33.930927","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:29:04.683631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:29:35.601715","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:30:07.127320","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:30:38.711952","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:31:11.711045","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:31:42.602196","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:32:14.414033","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:32:45.619721","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
