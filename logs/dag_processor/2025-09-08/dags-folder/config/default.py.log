{"timestamp":"2025-09-08T06:39:53.579635","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:40:24.623840","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:40:55.115089","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:41:25.731526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:41:56.056496","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:42:26.237289","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:42:56.806391","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:43:27.575695","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:43:58.050592","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:44:28.426172","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:44:59.061540","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:45:29.652086","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:46:00.054426","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:46:30.774547","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:47:00.946553","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:47:31.304460","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:48:01.808005","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:48:32.800930","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:49:03.616850","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:49:34.054530","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:50:04.536241","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:50:35.347607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:51:05.481821","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:51:35.949529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:52:06.876838","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:52:37.456925","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:53:08.498738","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:53:39.003143","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:54:09.101079","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:54:40.748126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:55:10.788840","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:55:41.935357","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:56:12.831501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:56:43.618193","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:57:14.616788","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:57:44.882437","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:58:15.657966","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:58:46.631412","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:59:17.322592","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:59:48.052782","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:00:19.065045","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:00:49.727432","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:01:20.152171","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:01:51.028592","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:02:21.635556","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:02:52.097603","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:03:22.561123","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:03:52.961363","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:04:23.215099","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:04:54.060983","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:05:24.652714","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:05:55.348354","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:06:26.036188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:06:56.689254","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:07:27.041553","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:07:57.303279","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:08:28.033523","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:08:59.176648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:09:29.730997","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:09:59.978189","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:10:30.960762","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:11:01.820639","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:11:32.376607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:12:02.827852","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:12:33.524742","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:13:04.565931","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:13:35.334638","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:14:05.542580","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:14:36.116768","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:15:06.613163","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:15:36.938206","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:16:07.482279","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:16:37.843139","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:17:08.273856","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:19:34.994563","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:20:06.425565","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:20:37.956233","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:21:09.090810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:21:40.527018","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:22:11.792761","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:22:43.194572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:23:14.593310","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:23:45.791094","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:24:17.041689","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:24:48.315366","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:25:19.735125","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:25:50.974931","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:26:22.131050","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:26:53.508036","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:27:24.630193","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:27:55.785017","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:28:26.865059","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:28:58.034314","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:29:29.439547","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:30:00.988708","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:30:32.258087","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:31:03.885674","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:31:35.251711","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:32:06.548258","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:32:37.552057","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:33:08.958156","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:33:40.288767","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:34:11.738865","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:34:43.245539","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:35:14.539661","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:35:45.935924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:36:17.311871","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:36:48.572473","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:37:19.786002","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:37:51.250061","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:38:22.776007","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:38:54.054642","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:39:25.256518","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:39:56.745236","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:40:27.942140","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:40:59.063033","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:41:30.153402","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:42:01.922023","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:42:33.246752","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:43:04.619377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:43:35.714356","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:44:06.767749","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:44:38.322501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:45:09.628823","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:45:40.974607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:46:12.368809","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:46:43.004572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:47:13.795397","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:47:45.137936","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:48:16.678564","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:48:47.774379","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:49:19.262022","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:49:50.726808","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:50:21.975058","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:50:53.433624","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:51:24.541020","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:51:55.727994","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:52:27.170732","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:52:58.348898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:53:29.589753","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:54:01.375829","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:54:32.648101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:55:04.307643","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:55:36.728895","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:56:08.386519","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:56:39.766490","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:57:11.261169","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:57:42.638878","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:58:13.497671","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:58:44.812937","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:59:16.197749","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:59:47.875401","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:00:19.024994","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:00:50.542622","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:01:21.770157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:01:53.032207","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:02:24.220858","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:02:55.191138","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:03:26.738559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:03:58.157035","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:04:29.800780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:05:00.755871","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:05:31.620316","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:06:03.084514","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:06:34.423850","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:07:05.759299","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:07:37.487998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:08:08.879369","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:08:40.324850","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:09:11.569898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:09:42.998810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:10:14.469703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:10:45.819129","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:11:17.058974","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:11:48.437946","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:12:19.732898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:12:51.249049","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:13:22.464005","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:13:53.952376","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:14:25.407734","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:14:56.940602","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:15:28.089617","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:15:59.722974","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:16:31.251649","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:17:02.913893","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:17:34.121689","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:18:05.754818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:18:37.294387","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:19:08.768027","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:19:40.114476","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:20:11.734330","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:20:54.113388","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:21:26.004138","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:21:57.636261","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:22:29.560054","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:23:01.276763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:23:32.852496","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:24:04.187310","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:24:36.076815","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:25:07.804264","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:25:39.365509","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:26:10.691615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:26:42.167612","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:27:13.894420","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:27:45.500706","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:28:16.778926","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:28:48.508334","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:47:52.847696","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:48:24.688637","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:48:55.115157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:49:26.544845","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:49:58.073422","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:50:29.728134","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:51:00.959050","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:51:32.512519","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:52:03.431272","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:52:34.876330","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:53:06.038508","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:53:37.589708","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:54:08.985075","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:54:40.996802","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:55:11.829355","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:55:42.510824","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:56:14.061831","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:56:45.869425","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:57:17.072961","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:57:48.758745","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:58:20.195333","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:58:51.822040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:59:23.319103","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T08:59:54.730408","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:00:26.250323","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:00:57.660060","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:01:28.829103","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:02:00.300793","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:02:31.711248","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:03:03.433069","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:03:34.001405","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:04:05.571301","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:04:37.398836","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:05:08.878193","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:05:40.045050","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:06:12.317357","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:06:43.885810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:07:15.391024","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:07:45.844772","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:08:33.273465","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:09:05.018424","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:09:36.733434","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:10:08.555153","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:10:40.349817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:11:12.005680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:11:43.500187","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:12:14.903370","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:12:46.466880","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:13:18.418669","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:13:49.668207","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:14:21.320778","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:14:52.867641","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:15:24.383980","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:15:55.606278","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:16:27.035002","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:16:58.532563","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:17:29.840749","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:18:01.505798","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:18:33.334895","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:19:04.476731","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:19:36.099349","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:20:07.504231","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:20:39.004174","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:21:10.685561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:21:42.360113","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:22:13.710295","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:22:45.138465","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:23:16.371920","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:23:48.272293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:24:19.845691","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:24:51.330714","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:25:22.533559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:25:53.984238","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:26:25.460226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:26:56.669708","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:27:28.232511","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:27:59.668860","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:28:31.219298","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:29:02.345928","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:29:34.086399","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:30:05.578567","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:30:37.337391","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:31:08.698627","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:31:45.994373","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:32:30.244866","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:33:02.459709","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:33:32.973306","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:34:03.995417","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:34:35.291264","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:35:06.494454","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:35:38.442823","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:36:10.043046","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:36:41.751819","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:37:12.824293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:37:44.949325","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:38:15.655103","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:38:47.501328","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:39:18.968792","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:39:50.018992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:40:21.668643","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:40:52.815571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:41:24.526728","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:41:56.054240","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:42:26.668288","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:42:59.026066","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:43:30.192634","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:44:00.916325","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:44:31.771249","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:45:03.262215","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:45:34.252742","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:46:04.924561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:46:35.747820","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:47:06.679757","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:47:38.421495","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:48:09.902004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:48:41.745799","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:49:12.831141","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:49:44.189870","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:50:15.986778","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:50:47.481809","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:51:18.730965","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:51:50.617234","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:52:22.372385","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:52:53.589275","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:53:25.191600","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:53:57.023820","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:54:27.890337","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:54:59.339501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:55:31.011332","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:56:02.830306","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:56:34.378852","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:57:05.956875","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:57:37.621201","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:58:08.754525","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:58:40.129682","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:59:14.632393","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T09:59:46.296732","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:00:17.566436","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:00:48.726749","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:01:20.190083","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:01:51.826789","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:02:23.196475","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:02:54.691154","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:03:26.194461","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:03:57.864339","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:04:29.841551","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:05:00.712085","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:05:32.030898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:06:03.935320","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:06:35.409848","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:07:06.978050","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:09:46.601159","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:10:18.138151","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:10:49.761909","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:11:21.393404","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:11:52.013666","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:12:23.795677","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:12:55.438281","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:13:26.719514","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:13:58.751692","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:14:29.722293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:15:00.849374","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:15:32.484315","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:16:03.760739","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:16:35.344661","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:17:06.186888","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:17:37.946366","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:18:09.722224","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:18:41.332567","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:19:13.394306","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:19:45.239696","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:20:16.676055","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:20:48.376346","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:21:19.859797","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:21:51.285228","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:22:22.377900","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:22:54.038028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:23:25.557324","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:23:57.059665","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:24:28.818399","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:25:00.482747","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:25:31.243839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:26:02.085650","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:26:33.873452","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:27:05.505600","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:27:37.075992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:28:08.569270","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:28:40.091283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:29:11.265314","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:29:42.772239","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:30:14.297681","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:30:46.044696","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:31:17.337672","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:31:48.824964","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:32:20.345131","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:32:51.635782","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:33:23.028543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:33:54.075095","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:34:25.691331","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:34:55.964687","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:35:27.384648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:35:58.818175","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:36:30.181992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:37:01.800715","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:37:33.290730","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:38:04.548936","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:38:36.538182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:39:07.640078","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:39:39.933192","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:40:11.500153","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:40:43.101325","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:41:13.957385","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:41:44.818538","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:42:15.349991","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:42:46.463822","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:43:16.809950","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:43:47.170911","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:44:18.070105","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:44:49.326195","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:45:20.056051","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:45:50.965296","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:46:21.927402","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:46:52.553962","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:47:23.011361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:47:53.808503","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:48:24.954377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:48:55.749601","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:49:26.663775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:49:57.650521","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:50:28.633485","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:50:59.291250","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:51:30.293517","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:52:01.192839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:52:31.649308","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:53:01.907313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:53:32.185784","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:54:03.019392","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:54:34.198318","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:55:05.041801","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:55:35.211980","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:56:05.523317","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:56:36.243291","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:57:07.512100","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:57:38.520049","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:58:09.288998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:58:39.676081","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:59:10.574585","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T10:59:41.468577","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:00:12.247277","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:00:43.399269","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:01:14.534762","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:01:45.294784","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:02:16.305579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:02:46.711425","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:03:17.528584","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:03:48.532684","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:04:18.696898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:04:49.316363","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:05:20.178075","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:05:50.477245","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:06:20.666812","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:06:51.076256","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:07:22.103454","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:07:52.456807","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:08:23.647116","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:08:54.466649","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:09:25.274711","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:09:55.546288","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:10:26.477429","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:10:57.493659","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:11:28.569451","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:11:58.707926","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:12:29.497805","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:12:59.772765","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:13:30.049497","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:14:00.940755","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:14:31.640585","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:15:02.711744","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:15:33.732670","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:16:04.042758","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:16:34.596735","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:17:04.779780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:17:35.715369","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:18:06.423319","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:18:37.793545","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:19:08.302603","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:19:39.680526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:20:10.970150","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:20:42.045552","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:21:12.753391","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:21:44.068599","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:22:14.402686","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:22:45.484532","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:23:17.215065","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:23:47.634421","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:24:18.694721","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:24:49.810118","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:25:20.197526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:25:50.903924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:26:22.722412","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:26:53.485734","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:27:24.087393","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:27:54.726068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:28:25.666261","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:28:56.340420","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:29:27.140651","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:29:57.910707","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:30:28.870970","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:30:59.304695","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:31:29.652906","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:31:59.763644","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:32:31.462384","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:33:02.140471","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:33:33.180676","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:34:04.154567","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:34:35.884641","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:35:06.584686","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:35:37.563162","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:36:09.407210","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:36:40.860455","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:37:12.385533","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:37:44.038766","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:38:15.755390","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:38:47.179725","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:39:18.291801","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:39:50.376498","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:40:21.879002","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:40:53.192764","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:41:24.809143","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:41:56.669042","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:42:28.109843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:42:59.239508","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:43:30.762405","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:44:02.332654","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:44:33.963597","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:45:05.791747","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:45:36.945814","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:46:07.749701","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:46:39.218568","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:47:11.390810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:47:42.306415","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:48:14.005079","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:48:44.758134","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:49:16.017328","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:49:47.723799","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:50:19.261700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:50:50.851459","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:51:21.772251","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:51:52.904646","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:52:24.615273","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:52:56.348453","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:53:27.964066","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:53:59.730612","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:54:31.228333","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:55:02.379655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:55:34.058945","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:56:05.521958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:56:36.930454","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:57:08.397817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:57:40.306399","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:58:11.609543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:58:43.141230","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:59:14.800811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T11:59:46.196704","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:00:17.766980","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:00:49.081114","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:01:20.701090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:01:52.351775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:02:23.547489","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:02:55.170370","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:03:26.134180","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:03:56.690574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:04:27.956103","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:04:59.550689","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:05:30.853448","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:06:01.918964","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:06:33.567701","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:07:06.459073","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:07:37.874473","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:08:10.279758","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:08:42.477928","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:09:13.786949","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:09:46.862573","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:10:18.017183","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:10:49.612983","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:11:20.594181","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:11:51.930845","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:12:22.576436","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:12:54.279613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:13:25.884617","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:13:57.162337","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:14:28.708545","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:15:00.308901","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:15:31.255930","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:16:03.061719","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:16:34.407731","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:17:31.307650","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:18:20.586600","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:18:54.260031","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:19:27.915950","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:19:59.057664","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:20:30.451722","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:21:02.155642","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:21:33.566494","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:22:05.305778","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:22:38.529529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:23:10.246976","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:23:40.973762","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:24:12.729345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T12:24:43.854470","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:18:40.623072","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:19:12.318962","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:19:44.190957","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:20:15.711649","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:20:47.605285","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:21:19.045406","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:21:50.672922","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:22:22.380196","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:22:53.855117","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:23:25.603614","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:23:57.426148","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:24:28.902053","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:25:00.544469","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:25:32.898703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:26:03.730274","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:26:34.770538","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:27:05.663423","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:27:36.523141","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:28:08.282175","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:28:39.688490","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:29:11.106623","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:29:43.274694","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:30:14.917100","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:30:46.143214","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:31:18.062583","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:31:49.480926","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:32:20.847310","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:32:52.662915","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:33:23.422687","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:33:55.923253","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:34:27.135961","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:34:58.543234","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:35:30.136182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:36:01.942914","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:36:34.271226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:37:05.246556","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:37:36.557403","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:38:08.700549","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:38:39.539158","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:39:11.308705","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:39:42.592324","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:40:13.701242","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:40:45.325421","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:41:17.056466","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:41:48.811482","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:42:20.174010","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:42:51.832685","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:43:23.715453","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:43:55.120031","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:44:26.187108","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:44:57.865303","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:45:29.352456","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:46:00.432607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:46:32.292197","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:47:03.894146","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:47:35.856928","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:48:07.353179","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:48:39.443707","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:49:10.565407","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:49:42.297196","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:50:14.112810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:50:45.912500","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:51:17.503883","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:51:50.090933","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:52:21.470456","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:52:52.363525","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:53:24.019215","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:53:55.944278","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:54:26.558230","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:54:58.373090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:55:29.263694","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:56:00.025211","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:56:31.403433","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:57:03.624354","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:57:35.119320","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:58:07.138265","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:58:38.900918","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:59:09.670926","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T15:59:41.436241","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:00:13.001077","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:00:44.993438","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:01:16.083146","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:01:47.630645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:02:19.243265","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:02:51.258818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:03:22.118860","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:03:53.908910","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:04:25.006498","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:04:56.589824","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:05:27.733341","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:05:58.849683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:06:30.709912","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:07:02.288368","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:07:32.930534","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:08:04.768723","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:08:36.400294","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:09:07.954739","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:09:39.738777","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:10:10.471932","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:10:42.156695","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:11:13.236679","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:11:44.754758","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:12:16.708870","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:12:48.604501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:13:20.286041","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:13:51.193187","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:14:22.894737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:14:54.822281","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:15:26.632654","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:15:58.067378","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:16:29.355608","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:17:01.284021","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:17:31.840762","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:18:03.917878","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:18:35.548903","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:19:06.555203","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:19:37.483518","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:20:09.044841","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:20:40.460368","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:21:12.122835","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:21:42.755702","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:22:14.725507","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:22:45.641134","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:23:16.681600","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:23:48.537145","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:24:20.190166","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:24:51.550821","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:25:23.284948","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:25:55.813856","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:26:27.376868","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:26:59.204678","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:27:30.263823","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:28:02.256486","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:28:33.872706","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:29:04.625293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:29:35.541360","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:30:07.058077","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:30:38.641883","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:31:11.644979","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:31:42.510004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:32:14.336184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T16:32:45.553777","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
