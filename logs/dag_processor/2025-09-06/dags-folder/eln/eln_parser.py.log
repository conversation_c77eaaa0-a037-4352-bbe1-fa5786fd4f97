{"timestamp":"2025-09-06T09:12:56.690889","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:12:56.696604","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:12:57.211622","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:12:57.217383","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:13:27.446740","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:13:27.452440","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:13:27.619274","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:13:27.623958","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:13:58.110126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:13:58.115704","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:13:58.281884","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:13:58.286298","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:14:28.543375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:14:28.554805","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:14:29.017499","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:14:29.021654","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:14:59.329829","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:14:59.340594","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:14:59.537193","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:14:59.542802","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:15:30.932068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:15:30.943382","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:15:31.438604","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:15:31.444336","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:16:01.895275","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:16:01.901709","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:16:02.096107","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:16:02.102468","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:16:33.121190","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:16:33.126700","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:16:33.313768","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:16:33.318488","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:17:04.216302","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:17:04.223898","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:17:04.429509","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:17:04.435667","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:17:34.946860","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:17:34.953861","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:17:35.445752","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:17:35.450795","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:18:05.912613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:18:05.918548","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:18:06.145776","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:18:06.153419","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:18:36.384614","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:18:36.391783","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:18:37.119454","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:18:37.125946","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:19:08.019620","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:19:08.027174","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:19:08.219255","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:19:08.223944","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:19:38.962179","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:19:38.969756","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:19:39.204498","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:19:39.210910","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:20:09.600196","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:20:09.606214","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:20:10.089717","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:20:10.095275","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:20:40.547422","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:20:40.552972","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:20:40.744629","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:20:40.750210","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:21:11.436615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:21:11.442270","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:21:11.619463","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:21:11.624922","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:21:42.207363","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:21:42.221771","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:21:42.780156","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:21:42.785398","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:22:13.313155","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:22:13.318697","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:22:13.531457","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:22:13.537681","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:22:44.241530","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:22:44.247059","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:22:44.427238","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:22:44.432169","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:23:14.690103","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:23:14.697297","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:23:15.215457","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:23:15.223376","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:23:45.545646","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:23:45.552427","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:23:45.745770","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:23:45.752204","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:24:16.337675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:24:16.344280","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:24:16.529585","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:24:16.534601","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:24:46.853663","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:24:46.858958","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:24:47.020855","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:24:47.025690","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:25:18.027895","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:25:18.034245","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:25:18.221324","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:25:18.226533","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:25:49.163780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:25:49.173637","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:25:49.540968","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:25:49.546214","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:26:19.849848","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:26:19.856154","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:26:20.047511","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:26:20.053473","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:26:51.121608","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:26:51.127349","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:26:51.321028","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:26:51.325883","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:27:21.910024","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:27:21.914174","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:27:22.119033","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:27:22.129116","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:27:52.461286","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:27:52.472071","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:27:52.652815","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:27:52.657396","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:28:23.746527","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:28:23.753987","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:28:23.937502","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:28:23.941917","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:28:54.244424","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:28:54.266853","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:28:54.432320","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:28:54.438240","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:29:24.798928","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:29:24.804319","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:29:24.980780","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:29:24.985027","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:29:55.258581","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:29:55.263582","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:29:55.461231","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:29:55.466653","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:30:26.048448","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:30:26.053799","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:30:26.590701","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:30:26.595035","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:30:57.034177","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:30:57.041711","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:30:57.222247","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:30:57.227505","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:31:28.125325","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:31:28.132433","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:31:28.351285","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:31:28.359302","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:31:58.952484","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:31:58.963422","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:31:59.477747","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:31:59.483112","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:32:30.186057","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:32:30.196663","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:32:30.427437","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:32:30.432609","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:33:01.008948","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:33:01.014709","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:33:01.189384","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:33:01.193891","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:33:31.494746","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:33:31.499888","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:33:31.943196","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:33:31.948110","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:34:02.373394","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:34:02.383270","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:34:02.601693","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:34:02.749125","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:34:33.809576","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:34:33.814707","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:34:33.991536","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:34:33.996274","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:35:04.279408","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:35:04.285607","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:35:04.851424","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:35:04.856164","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:35:35.298268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:35:35.323272","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:35:35.841621","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:35:35.848828","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:36:06.394501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:36:06.400989","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:36:07.110015","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:36:07.116307","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:36:37.713319","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:36:37.718729","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:36:37.919206","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:36:37.923922","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:37:08.745828","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:37:08.751875","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:37:08.946239","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:37:08.951714","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:37:39.315273","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:37:39.322945","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:37:39.918864","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:37:39.923708","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:38:10.417265","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:38:10.427435","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:38:10.604451","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:38:10.609486","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:38:41.364820","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:38:41.371347","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:38:41.583717","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:38:41.594126","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:39:12.009433","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:39:12.015249","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:39:12.515699","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:39:12.520467","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:39:42.890780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:39:42.895895","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:39:43.071611","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:39:43.076162","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:40:13.525474","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:40:13.534798","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:40:13.746075","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:40:13.751455","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:40:44.189543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:40:44.195117","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:40:44.361486","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:40:44.366351","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:41:15.555081","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:41:15.562780","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:41:15.761191","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:41:15.765800","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:41:46.438526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:41:46.444352","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:41:46.932107","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:41:46.936736","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:42:17.520270","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:42:17.526461","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:42:17.711034","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:42:17.715996","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:42:48.628293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:42:48.635354","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:42:48.825482","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:42:48.830333","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:43:19.273935","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:43:19.279811","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:43:19.760914","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:43:19.765704","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:43:50.098834","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:43:50.103464","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:43:50.268316","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:43:50.273024","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:44:20.828817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:44:20.835006","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:44:21.002071","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:44:21.009792","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:44:51.266017","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:44:51.271247","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:44:51.756359","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:44:51.762685","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:45:22.206592","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:45:22.217097","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:45:22.425580","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:45:22.433360","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:45:53.085449","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:45:53.093028","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:45:53.269546","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:45:53.274876","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:46:23.673552","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:46:23.680599","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:46:23.894047","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:46:23.902012","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:46:54.119565","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:46:54.125293","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:46:54.296388","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:46:54.301288","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:47:25.903689","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:47:25.908392","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:47:26.369479","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:47:26.373957","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:47:56.972941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:47:56.978193","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:47:57.198712","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:47:57.204981","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:48:28.252241","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:48:28.256995","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:48:28.433564","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:48:28.438475","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:48:58.848980","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:48:58.854376","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:48:59.331190","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:48:59.335846","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:49:30.406512","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:49:30.412348","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:49:30.597334","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:49:30.602416","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:50:01.373237","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:50:01.379407","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:50:01.559554","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:50:01.564145","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:50:32.512703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:50:32.518801","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:50:32.692137","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:50:32.697516","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:51:03.848741","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:51:03.857075","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:51:04.054307","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:51:04.058740","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:51:34.472286","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:51:34.477399","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:51:34.680672","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:51:34.688329","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:52:05.308312","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:52:05.315080","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:52:05.487561","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:52:05.494958","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:52:36.468725","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:52:36.474480","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:52:36.638761","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:52:36.643842","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:53:07.430663","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:53:07.437010","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:53:07.942030","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:53:07.947541","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:53:38.592627","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:53:38.604484","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:53:38.809351","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:53:38.815476","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:54:09.794035","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:54:09.798596","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:54:09.985962","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:54:09.990883","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:54:40.460124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:54:40.465927","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:54:40.966741","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:54:40.975242","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:55:11.759812","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:55:11.764494","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:55:12.007388","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:55:12.013011","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:55:42.684341","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:55:42.688913","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:55:42.857681","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:55:42.862307","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:56:13.286566","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:56:13.291000","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:56:13.761694","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:56:13.766047","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:56:44.217587","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:56:44.223927","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:56:44.418579","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:56:44.423833","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:57:15.058258","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:57:15.062901","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:57:15.394779","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:57:15.398992","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:57:45.815677","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:57:45.820897","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:57:45.976427","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:57:45.983013","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:58:16.647293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:58:16.651968","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:58:16.809775","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:58:16.815110","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:58:47.184954","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:58:47.189431","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:58:47.712401","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:58:47.718538","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:59:18.272336","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:59:18.276761","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:59:18.432327","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:59:18.437027","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:59:49.127573","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:59:49.133689","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:59:49.313259","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T09:59:49.319091","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:00:19.717660","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:00:19.722549","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:00:20.295131","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:00:20.300345","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:00:50.794925","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:00:50.802867","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:00:51.015902","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:00:51.021844","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:01:21.787952","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:01:21.792442","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:01:22.162352","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:01:22.167726","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:01:52.614632","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:01:52.620749","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:01:52.782901","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:01:52.787730","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:02:23.817171","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:02:23.822190","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:02:23.998266","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:02:24.005394","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:02:55.247889","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:02:55.252081","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:02:55.624911","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:02:55.629702","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:03:25.961541","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:03:25.966422","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:03:26.126184","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:03:26.130957","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:03:56.820931","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:03:56.826808","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:03:57.005438","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:03:57.011066","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:04:27.601022","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:04:27.606455","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:04:28.097106","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:04:28.101530","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:04:58.598618","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:04:58.603842","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:04:58.798919","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:04:58.804108","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:05:29.886806","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:05:29.894617","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:05:30.071237","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:05:30.076471","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:06:00.511511","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:06:00.519972","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:06:00.995273","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:06:00.999742","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:06:31.390225","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:06:31.395102","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:06:31.567691","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:06:31.572238","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:07:03.356105","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:07:03.370190","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:07:04.076364","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:07:04.084278","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:07:34.587956","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:07:34.592875","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:07:34.756254","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:07:34.761168","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:08:06.009786","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:08:06.016111","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:08:06.200327","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:08:06.206048","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:08:37.916101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:08:37.931634","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:08:38.139412","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:08:38.144545","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:09:08.790737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:09:08.801765","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:09:09.266826","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:09:09.271662","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:09:39.651420","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:09:39.657371","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:09:39.835883","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:09:39.841365","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:10:10.424560","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:10:10.430701","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:10:10.914892","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:10:10.919629","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:10:41.458088","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:10:41.462968","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:10:41.633867","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:10:41.639246","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:11:12.650484","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:11:12.656619","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:11:12.839329","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:11:12.846249","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:11:43.291276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:11:43.298778","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:11:43.788430","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:11:43.793803","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:12:14.292157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:12:14.297788","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:12:14.473914","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:12:14.479038","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:12:45.167289","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:12:45.171316","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:12:45.327364","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:12:45.332690","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:13:16.118617","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:13:16.125526","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:13:16.317039","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:13:16.324175","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:13:47.389058","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:13:47.395081","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:13:47.564365","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:13:47.569543","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:14:18.801144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:14:18.814112","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:14:19.364701","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:14:19.369394","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:14:49.982377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:14:49.987030","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:14:50.154486","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:14:50.159363","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:15:20.833205","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:15:20.839898","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:15:21.011392","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:15:21.016189","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:15:51.398958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:15:51.403878","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:15:51.560895","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:15:51.565481","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:16:22.663136","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:16:22.668815","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:16:22.841781","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:16:22.846584","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:16:53.407752","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:16:53.411935","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:16:53.758529","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:16:53.763634","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:17:24.206278","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:17:24.210321","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:17:24.382746","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:17:24.387791","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:17:55.190534","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:17:55.195200","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:17:55.352518","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:17:55.357414","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:18:25.842493","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:18:25.847234","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:18:26.342209","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:18:26.347586","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:18:56.799728","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:18:56.804182","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:18:56.972849","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:18:56.977396","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:19:27.772935","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:19:27.780664","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:19:27.976851","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:19:27.981705","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:19:58.362386","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:19:58.368328","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:19:58.814852","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:19:58.819910","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:20:29.290434","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:20:29.297086","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:20:29.485204","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:20:29.494590","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:21:00.370717","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:21:00.375003","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:21:00.533290","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:21:00.537830","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:21:31.073661","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:21:31.081426","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:21:31.282396","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:21:31.288313","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:22:02.590373","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:22:02.834119","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:22:03.800343","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:22:03.817514","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:22:34.738582","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:22:34.744567","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:22:35.201915","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:22:35.206947","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:23:05.600763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:23:05.611815","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:23:05.780065","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:23:05.787055","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:23:36.553126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:23:36.568619","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:23:36.767774","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:23:36.774160","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:24:07.364300","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:24:07.372385","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:24:07.865014","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:24:07.870968","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:24:38.265139","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:24:38.270171","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:24:38.455901","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:24:38.461602","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:25:09.855703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:25:09.861987","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:25:10.245437","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:25:10.250336","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:25:40.525406","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:25:40.531030","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:25:40.690941","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:25:40.696196","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:26:11.440403","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:26:11.445541","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:26:11.617506","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:26:11.624368","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:26:42.098185","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:26:42.106943","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:26:42.620936","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:26:42.626063","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:27:12.991798","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:27:12.997674","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:27:13.173886","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:27:13.178720","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:27:43.853550","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:27:43.857764","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:27:44.007550","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:27:44.012290","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:28:14.407486","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:28:14.416226","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:28:14.905427","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:28:14.910455","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:28:45.309735","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:28:45.313747","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:28:45.494085","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:28:45.499373","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:29:16.435968","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:29:16.441485","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:29:16.922514","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:29:16.927299","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:29:47.398355","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:29:47.404191","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:29:47.580808","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:29:47.586088","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:30:18.347896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:30:18.358054","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:30:18.519925","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:30:18.527669","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:30:48.990277","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:30:48.996042","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:30:49.522003","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:30:49.527016","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:31:19.997204","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:31:20.004024","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:31:20.166519","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:31:20.171503","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:31:50.736034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:31:50.740556","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:31:50.896536","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:31:50.901049","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:32:21.661926","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:32:21.673043","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:32:22.205533","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:32:22.210694","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:32:52.735673","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:32:52.740537","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:32:52.933816","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:32:52.939713","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:33:23.568107","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:33:23.577715","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:33:23.743398","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:33:23.748431","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:33:54.311712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:33:54.318874","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:33:54.491751","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:33:54.496870","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:34:24.678153","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:34:24.682356","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:34:24.860077","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:34:24.864880","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:34:56.597425","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:34:56.601651","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:34:57.097751","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:34:57.102685","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:35:27.546489","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:35:27.551034","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:35:27.721062","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:35:27.726047","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:35:58.506159","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:35:58.510556","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:35:58.675815","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:35:58.687197","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:36:29.131612","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:36:29.136555","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:36:29.637390","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:36:29.644596","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:36:59.992214","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:36:59.997453","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:37:00.153839","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:37:00.158808","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:37:31.380429","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:37:31.386174","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:37:31.861709","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:37:31.866840","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:38:02.867950","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:38:02.882056","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:38:03.088168","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:38:03.094691","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:38:34.642700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:38:34.659445","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:38:34.940654","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:38:34.948618","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:39:06.149238","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:39:06.156215","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:39:06.682372","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:39:06.824706","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:39:37.194889","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:39:37.201075","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:39:37.389030","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:39:37.395615","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:40:08.301096","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:40:08.308183","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:40:08.570295","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:40:08.578192","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:40:39.067014","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:40:39.073995","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:40:39.260833","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:40:39.265693","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:41:10.126353","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:41:10.130906","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:41:10.319160","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:41:10.337382","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:41:41.217991","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:41:41.225424","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:41:41.911790","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:41:41.934844","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:42:12.559557","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:42:12.570406","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:42:12.757926","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:42:12.764439","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:42:44.025527","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:42:44.031531","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:42:44.342540","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:42:44.348710","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:43:14.641113","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:43:14.647159","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:43:14.844610","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:43:15.352195","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:43:45.689069","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:43:45.694159","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:43:45.882673","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:43:45.887759","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:44:16.402679","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:44:16.411190","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:44:17.093357","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:44:17.275132","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:44:47.984373","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:44:47.991167","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:44:48.297809","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:44:48.308987","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:52:34.192452","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:52:34.197758","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:52:34.466861","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:52:34.475861","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:53:05.007885","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:53:05.012276","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:53:12.248987","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:53:12.252578","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:53:13.023154","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:53:13.026691","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:53:43.969699","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:53:43.975283","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:53:44.383669","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:53:44.386901","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:54:15.238272","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:54:15.244264","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:54:15.380263","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:54:15.383425","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:54:46.531250","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:54:46.545317","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:54:46.755348","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:54:46.759116","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:55:16.848029","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:55:16.853023","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:55:17.012071","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:55:17.015972","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:55:48.046495","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:55:48.052517","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:55:48.254065","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:55:48.257760","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:56:19.567610","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:56:19.577814","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:56:19.970694","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:56:19.974625","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:56:50.937345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:56:50.944060","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:56:51.274165","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:56:51.277343","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:57:22.127363","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:57:22.132985","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:57:22.780349","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:57:22.783894","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:57:53.666073","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:57:53.671603","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:57:53.869324","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:57:53.873357","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:58:23.986985","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:58:23.991193","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:58:24.135422","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:58:24.139683","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:58:55.247218","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:58:55.253262","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:58:55.419504","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:58:55.423140","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:59:25.652964","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:59:25.659050","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:59:25.812745","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:59:25.816071","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:59:56.641030","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:59:56.645933","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:59:56.944541","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T10:59:56.949392","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:00:27.823074","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:00:27.829726","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:00:28.161810","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:00:28.165377","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:00:58.997371","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:00:59.003069","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:00:59.150504","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:00:59.153747","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:01:30.216502","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:01:30.231756","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:01:30.435219","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:01:30.439784","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:02:01.519024","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:02:01.524373","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:02:01.672514","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:02:01.676900","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:02:32.596805","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:02:32.601916","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:02:32.920337","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:02:32.924958","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:03:03.925376","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:03:03.930821","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:03:04.201734","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:03:04.206191","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:03:35.053768","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:03:35.059425","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:03:35.209065","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:03:35.212387","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:04:05.355135","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:04:05.362404","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:04:05.508900","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:04:05.512163","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:04:36.499604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:04:36.504764","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:04:36.654172","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:04:36.658093","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:05:07.637324","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:05:07.642222","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:05:07.779346","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:05:07.782718","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:05:38.784185","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:05:38.788922","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:05:38.939042","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:05:38.942321","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:06:09.421646","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:06:09.434632","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:06:10.559931","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:06:10.587237","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:06:40.854761","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:06:40.859657","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:06:41.223317","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:06:41.227219","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:07:12.126661","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:07:12.132384","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:07:12.293304","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:07:12.297074","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:07:42.431245","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:07:42.436781","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:07:42.584277","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:07:42.587351","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:08:12.831819","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:08:12.839306","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:08:13.018099","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:08:13.022021","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:08:43.180494","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:08:43.185375","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:08:43.329855","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:08:43.333707","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:09:15.395186","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:09:15.401497","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:09:15.783453","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:09:15.787677","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:09:45.992958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:09:45.997889","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:09:46.296970","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:09:46.300776","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:10:17.288043","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:10:17.293103","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:10:17.430172","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:10:17.433875","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:10:47.746271","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:10:47.751220","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:10:47.888088","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:10:47.891970","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:11:18.091761","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:11:18.096904","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:11:18.243423","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:11:18.248899","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:11:49.086466","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:11:49.090261","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:11:49.334441","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T11:11:49.338259","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:40:37.229088","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:40:37.235596","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:40:37.496361","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:40:37.499831","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:41:08.393411","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:41:08.400110","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:41:08.713642","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:41:08.717333","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:41:39.556281","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:41:39.561182","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:41:39.725544","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:41:39.729132","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:42:09.833154","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:42:09.840654","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:42:10.034508","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:42:10.038266","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:42:41.032675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:42:41.037760","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:42:41.209023","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:42:41.213379","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:43:12.244912","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:43:12.251562","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:43:12.416446","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:43:12.419941","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:43:43.483419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:43:43.493375","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:43:43.979083","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:43:43.983607","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:44:15.047028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:44:15.054141","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:44:15.363298","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:44:15.366637","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:44:46.229741","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:44:46.237105","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:44:46.570273","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:44:46.573631","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:45:17.499996","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:45:17.507309","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:45:17.668170","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:45:17.671718","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:45:47.795138","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:45:47.799188","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:45:47.938424","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:45:47.941656","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:46:19.026380","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:46:19.030425","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:46:19.170854","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:46:19.174627","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:46:50.569547","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:46:50.573800","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:46:50.966888","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:46:50.972138","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:47:21.248314","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:47:21.253884","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:47:21.592570","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:47:21.596137","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:47:52.560559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:47:52.570778","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:47:52.936955","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:47:53.057857","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:48:23.862553","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:48:23.867286","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:48:24.013491","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:48:24.016880","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:48:55.110645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:48:55.115951","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:48:55.258030","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:48:55.261544","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:49:26.402031","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:49:26.407920","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:49:26.563620","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:49:26.693385","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:49:57.605749","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:49:57.619201","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:49:58.253186","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:49:58.258996","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:50:28.546694","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:50:28.554298","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:50:28.708107","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:50:29.080351","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:51:00.030613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:51:00.037924","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:51:00.584672","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:51:00.589875","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:51:31.519283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:51:31.526301","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:51:31.694815","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:51:31.830874","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:52:02.813795","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:52:02.822091","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:52:03.171349","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:52:03.292327","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:52:34.205334","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:52:34.211151","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:52:34.528004","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:52:34.645809","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:53:05.552760","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:53:05.560078","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:53:05.740961","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:53:06.043942","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:53:36.920053","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:53:36.925663","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:53:37.076883","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:53:37.081655","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:54:08.244228","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:54:08.250438","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:54:08.404039","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:54:08.773208","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:54:39.539746","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:54:39.545984","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:54:39.690154","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:54:39.813591","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:55:10.717237","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:55:10.725092","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:55:11.156031","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:55:11.278494","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:55:42.132900","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:55:42.138442","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:55:42.277293","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:55:42.280847","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:56:13.480910","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:56:13.486863","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:56:13.894761","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:56:13.900257","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:56:44.754519","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:56:44.768143","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:56:44.934951","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:56:44.940628","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:57:15.518553","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:57:15.526392","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:57:15.688536","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:57:15.813870","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:57:46.125215","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:57:46.130511","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:57:46.372144","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:57:46.494354","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:58:17.451848","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:58:17.457090","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:58:17.763235","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:58:17.771613","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:58:48.825774","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:58:48.830329","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:58:49.095090","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:58:49.098987","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:59:19.381879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:59:19.387008","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:59:19.531068","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:59:19.798977","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:59:50.761009","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:59:50.765743","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:59:50.901604","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T12:59:50.906872","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:00:22.288922","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:00:22.294869","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:00:22.432100","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:00:22.557964","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:00:53.584903","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:00:53.588761","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:00:53.874327","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:00:53.878005","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:01:24.936417","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:01:24.942779","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:01:25.235427","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:01:25.353005","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:01:56.192457","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:01:56.200741","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:01:56.373370","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:01:56.715248","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:02:27.839621","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:02:27.845909","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:02:28.040127","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:02:28.361818","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:02:59.392801","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:02:59.399264","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:02:59.559590","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:02:59.694469","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:03:30.712726","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:03:30.717637","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:03:31.071316","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:03:31.211693","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:04:02.150103","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:04:02.161317","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:04:02.561654","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:04:02.705028","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:04:33.562352","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:04:33.568366","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:04:33.737662","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:04:34.101243","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:05:05.081023","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:05:05.089607","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:05:05.250975","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:05:05.281269","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:05:36.724861","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:05:36.738307","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:05:36.921796","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:05:36.927659","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:06:07.099839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:06:07.105192","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:06:07.440939","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:06:07.450250","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:06:38.440686","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:06:38.447125","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:06:38.907290","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:06:38.914012","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:07:09.111926","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:07:09.119403","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:07:09.349093","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:07:09.353555","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:07:39.694147","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:07:39.699102","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:07:39.845849","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:07:39.850004","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:08:10.457182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:08:10.463543","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:08:10.637927","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:08:10.643472","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:08:40.736732","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:08:40.751605","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:08:41.029222","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:08:41.032741","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:09:12.306599","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:09:12.311969","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:09:12.608670","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:09:12.612423","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:09:43.718579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:09:43.726595","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:09:44.150176","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:09:44.156409","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:10:15.145069","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:10:15.151086","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:10:15.300709","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:10:15.304867","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:10:46.696800","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:10:46.702102","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:10:46.850839","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:10:46.854859","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:11:17.096882","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:11:17.101765","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:11:17.454294","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:11:17.458462","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:11:48.438950","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:11:48.443890","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:11:48.715453","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:11:48.719557","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:12:19.807240","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:12:19.812338","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:12:20.105020","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:12:20.109164","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:12:51.123488","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:12:51.130636","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:12:51.282947","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:12:51.291469","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:13:22.455609","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:13:22.461228","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:13:22.601743","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:13:22.605748","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:13:53.877526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:13:53.883904","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:13:54.022144","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:13:54.027113","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:14:25.135301","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:14:25.141062","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:14:25.434296","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:14:25.437929","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:14:56.462830","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:14:56.467501","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:14:56.785131","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:14:56.788873","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:15:27.835436","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:15:27.840964","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:15:27.998620","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:15:28.003047","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:15:59.214748","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:15:59.220940","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:15:59.395073","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:15:59.776342","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:16:30.847235","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:16:30.856654","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:16:31.037347","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:16:31.043113","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:17:02.208188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:17:02.214236","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:17:02.581469","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:17:02.705706","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:17:33.681214","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:17:33.686437","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:17:33.844857","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:17:33.848255","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:18:05.213345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:18:05.222620","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:18:05.376714","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:18:05.673034","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:18:36.605318","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:18:36.615470","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:18:37.033010","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:18:37.039986","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:19:07.950394","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:19:07.957150","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:19:08.366768","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:19:08.488651","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:19:39.519130","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:19:39.527102","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:19:39.874115","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:19:40.002087","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:20:10.967569","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:20:10.973980","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:20:11.140776","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:20:11.430146","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:20:42.398006","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:20:42.403810","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:20:42.550489","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:20:42.676508","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:21:12.996599","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:21:13.001906","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:21:13.310227","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:21:13.434961","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:21:44.507720","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:21:44.513576","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:21:44.964498","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:21:44.970102","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:22:15.990566","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:22:15.996931","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:22:16.282647","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:22:16.405234","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:22:47.285791","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:22:47.290993","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:22:47.781084","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:22:47.786428","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:23:18.723252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:23:18.729582","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:23:19.005417","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:23:19.012133","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:23:49.822949","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:23:49.827598","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:23:49.957506","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:23:49.962368","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:24:20.287763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:24:20.293857","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:24:20.614905","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:24:20.751026","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:24:51.755881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:24:51.764454","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:24:52.091888","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:24:52.222358","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:25:27.900901","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:25:28.019430","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:25:28.709444","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:25:28.733637","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:25:59.627925","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:25:59.646959","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:25:59.903986","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:25:59.910941","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:26:30.504987","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:26:30.532756","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:26:31.469155","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:26:31.527539","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:27:02.323179","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:27:02.339607","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:27:02.867242","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:27:02.880313","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:27:34.330295","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:27:34.336325","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:27:34.681362","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:27:34.685703","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:28:05.806206","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:28:05.811252","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:28:06.151288","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:28:06.158793","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:28:37.214409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:28:37.219903","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:28:37.381557","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:28:37.385842","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:29:08.722463","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:29:08.732235","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:29:08.928518","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:29:08.932867","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:29:40.075559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:29:40.080070","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:29:40.541233","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:29:40.545151","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:30:11.695264","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:30:11.700288","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:30:12.002810","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:30:12.006541","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:30:43.114145","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:30:43.119100","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:30:43.276038","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:30:43.280274","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:31:13.793071","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:31:13.800599","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:31:13.981835","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:31:13.985828","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:31:45.209179","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:31:45.214194","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:31:45.371526","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:31:45.375123","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:32:15.954246","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:32:15.959921","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:32:16.328695","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:32:16.471282","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:32:47.508960","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:32:47.513003","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:32:47.803064","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:32:47.806909","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:33:18.797090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:33:18.802777","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:33:18.948611","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:33:18.952631","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:51:25.753867","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:51:25.758509","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:51:25.908364","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:51:25.912345","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:51:57.167996","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:51:57.172750","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:51:57.314327","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:51:57.320266","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:52:28.389001","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:52:28.394321","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:52:28.777758","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:52:28.781705","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:52:59.796905","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:52:59.804177","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:53:00.148180","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:53:00.151716","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:53:31.054100","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:53:31.060381","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:53:31.219110","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:53:31.223182","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:54:01.555596","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:54:01.562521","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:54:01.780070","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:54:01.788977","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:54:31.974536","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:54:31.981208","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:54:32.178896","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:54:32.183751","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:55:03.840642","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:55:03.847877","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:55:04.162697","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:55:04.169613","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:55:35.339950","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:55:35.350667","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:55:35.741617","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:55:35.746102","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:56:06.790063","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:56:06.798675","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:56:07.228557","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:56:07.232983","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:56:38.290824","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:56:38.302707","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:56:38.535356","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:56:38.540416","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:57:08.723563","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:57:08.729241","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:57:08.906980","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:57:08.911315","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:57:39.124652","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:57:39.130530","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:57:39.302568","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:57:39.307032","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:58:10.348420","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:58:10.355553","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:58:10.705641","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:58:10.709744","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:58:41.781807","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:58:41.789953","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:58:42.217532","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:58:42.221693","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:59:13.212931","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:59:13.218463","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:59:13.376872","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:59:13.380972","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:59:43.610214","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:59:43.615873","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:59:43.777334","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T13:59:43.781910","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:00:13.980037","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:00:13.987717","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:00:14.152049","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:00:14.155876","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:00:44.582405","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:00:44.585972","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:00:44.874900","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:00:44.879264","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:01:15.074689","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:01:15.081585","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:01:15.548661","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:01:15.554475","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:01:46.543143","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:01:46.552199","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:01:46.948162","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:01:46.952545","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:02:17.070499","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:02:17.078318","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:02:17.262770","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:02:17.266737","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:02:47.609366","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:02:47.615453","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:02:47.788766","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:02:47.792791","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:03:17.961106","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:03:17.968474","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:03:18.140348","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:03:18.144594","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:03:48.602927","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:03:48.608079","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:03:48.929150","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:03:48.934282","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:04:19.085887","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:04:19.093228","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:04:19.417999","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:04:19.422644","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:04:51.528543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:04:51.532487","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:04:51.858108","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:04:51.862490","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:05:21.998950","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:05:22.003683","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:05:22.183731","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:05:22.207225","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:05:53.454818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:05:53.459566","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:05:53.607835","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:05:53.611813","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:06:24.431315","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:06:24.436449","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:06:24.723369","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:06:24.730435","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:06:55.817604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:06:55.822136","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:06:56.126827","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:06:56.131205","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:07:27.268431","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:07:27.273940","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:07:27.431343","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:07:27.716134","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:07:58.634838","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:07:58.646466","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:07:58.850095","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:07:58.856146","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:08:30.065435","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:08:30.069273","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:08:30.215345","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:08:30.218833","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:09:01.216929","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:09:01.223295","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:09:01.629801","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:09:01.771806","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:09:32.757157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:09:32.767490","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:09:33.093803","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:09:33.099450","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:10:04.114765","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:10:04.121584","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:10:04.352107","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:10:04.357371","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:10:35.871813","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:10:35.877311","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:10:36.045548","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:10:36.050171","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:11:07.358871","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:11:07.368380","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:11:07.554144","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:11:07.695481","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:11:38.701619","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:11:38.706172","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:11:39.249202","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:11:39.255859","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:12:10.180464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:12:10.186058","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:12:10.351022","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:12:10.682406","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:12:41.567579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:12:41.573024","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:12:41.780583","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:12:42.124321","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:13:13.110532","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:13:13.116225","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:13:13.270773","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:13:13.403199","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:13:44.279564","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:13:44.285830","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:13:44.641860","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:13:44.770158","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:14:15.764017","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:14:15.768998","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:14:16.268280","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:14:16.273760","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:14:47.224800","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:14:47.231632","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:14:47.412936","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:14:47.745126","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:15:18.670865","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:15:18.677219","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:15:18.857816","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:15:18.996626","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:15:49.249015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:15:49.254541","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:15:49.610441","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:15:49.735145","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:16:20.889950","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:16:20.895186","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:16:21.210474","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:16:21.345952","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:16:52.277914","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:16:52.282619","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:16:52.441262","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:16:52.718935","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:17:23.715216","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:17:23.723260","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:17:23.907808","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:17:24.058275","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:17:54.534307","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:17:54.541593","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:17:54.966861","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:17:55.112091","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:18:26.450992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:18:26.455967","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:18:26.751349","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:18:26.872651","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:18:57.843033","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:18:57.848491","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:18:58.253999","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:18:58.389659","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:19:29.515145","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:19:29.520849","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:19:29.674483","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:19:29.971939","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:20:00.915821","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:20:00.921635","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:20:01.087081","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:20:01.244445","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:20:31.853289","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:20:31.860161","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:20:32.168771","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:20:32.174712","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:21:02.577036","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:21:02.584516","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:21:03.053599","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:21:03.059329","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:21:33.976630","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:21:33.982463","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:21:34.520484","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:21:34.528669","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:22:05.630721","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:22:05.636138","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:22:05.812902","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:22:06.212820","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:22:37.221426","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:22:37.228934","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:22:37.415877","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:22:37.565796","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:23:08.479578","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:23:08.485663","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:23:08.833833","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:23:08.845388","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:23:39.941118","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:23:39.947393","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:23:40.313865","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:23:40.325196","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:24:11.309820","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:24:11.318845","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:24:11.502649","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:24:11.510450","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:24:41.697952","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:24:41.705175","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:24:41.933042","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:24:41.943659","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:25:12.190656","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:25:12.198999","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:25:12.394604","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:25:12.400090","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:25:43.353231","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:25:43.358574","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:25:43.745577","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:25:43.749423","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:26:14.780683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:26:14.789453","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:26:14.980315","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:26:14.985052","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:26:45.171624","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:26:45.176539","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:26:45.338945","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:26:45.343421","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:27:15.472049","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:27:15.477242","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:27:15.647653","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:27:15.652396","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:27:46.015572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:27:46.022450","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:27:46.408304","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:27:46.412160","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:28:16.684140","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:28:16.688027","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:28:17.001806","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:28:17.005940","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:28:48.172972","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:28:48.179848","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:28:48.373592","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:28:48.379592","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:29:18.978675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:29:18.983162","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:29:19.138156","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:29:19.142235","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:29:49.503939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:29:49.509048","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:29:49.680683","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:29:49.685366","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:30:19.998253","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:30:20.003620","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:30:20.325520","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:30:20.329565","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:30:51.570933","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:30:51.576862","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:30:51.939303","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:30:51.958763","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:31:23.058118","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:31:23.062452","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:31:23.212355","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:31:23.216356","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:31:53.613023","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:31:53.618165","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:31:53.766506","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:31:53.770743","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:32:23.971300","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:32:23.977273","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:32:24.139517","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:32:24.143993","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:32:54.424849","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:32:54.428975","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:32:54.750294","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:32:54.754502","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:33:25.074757","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:33:25.078637","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:33:25.465403","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:33:25.469905","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:33:55.579386","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:33:55.583362","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:33:55.737650","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:33:55.741870","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:34:26.012274","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:34:26.016713","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:34:26.178250","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:34:26.182408","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:34:56.407105","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:34:56.411386","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:34:56.574018","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:34:56.579517","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:35:27.064407","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:35:27.071070","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:35:27.472758","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:35:27.477539","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:35:57.860703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:35:57.866140","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:35:58.197875","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:35:58.202710","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:36:28.368086","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:36:28.373322","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:36:28.560175","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:36:28.565965","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:36:59.291910","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:36:59.296605","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:36:59.459277","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:36:59.463752","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:37:29.995958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:37:30.001391","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:37:30.166134","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:37:30.170370","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:38:00.559880","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:38:00.564643","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:38:01.098634","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:38:01.106809","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:38:31.390246","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:38:31.395612","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:38:31.734374","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:38:31.739461","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:39:02.208746","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:39:02.217065","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:39:02.439499","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:39:02.445942","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:39:33.225377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:39:33.230733","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:39:33.410857","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:39:33.416293","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:40:04.257971","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:40:04.264180","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:40:04.447772","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:40:04.456554","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:40:35.227575","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:40:35.233502","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:40:35.585284","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:40:35.589816","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:41:06.094269","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:41:06.099277","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:41:06.440601","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:41:06.444563","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:41:36.628536","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:41:36.636097","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:41:36.825163","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:41:36.830089","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:42:07.530770","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:42:07.537056","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:42:07.709117","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:42:07.713805","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:42:38.099648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:42:38.104944","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:42:38.262016","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:42:38.266040","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:43:08.941548","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:43:08.946643","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:43:09.388317","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:43:09.395568","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:43:39.695004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:43:39.700249","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:43:40.094167","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:43:40.098833","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:44:10.228508","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:44:10.232879","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:44:10.395579","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:44:10.399765","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:44:41.026216","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:44:41.030511","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:44:41.192263","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:44:41.196583","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:45:11.529958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:45:11.542849","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:45:11.812988","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:45:11.817842","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:45:41.977897","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:45:41.983677","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:45:42.428059","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:45:42.435389","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:46:12.849127","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:46:12.868266","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:46:13.360956","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:46:13.368339","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:46:43.496571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:46:43.500470","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:46:43.649636","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:46:43.654913","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:47:15.109468","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:47:15.113867","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:47:15.267064","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:47:15.271198","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:47:45.566145","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:47:45.579137","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:47:45.731618","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:47:45.735420","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:48:16.786395","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:48:16.791651","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:48:17.131345","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:48:17.135344","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:48:48.178883","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:48:48.183643","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:48:48.324394","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:48:48.328720","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:49:18.623122","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:49:18.627946","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:49:18.776224","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:49:18.780096","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:49:48.990108","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:49:48.995391","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:49:49.136376","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:49:49.140407","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:50:19.322030","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:50:19.326530","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:50:19.475349","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:50:19.479036","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:50:50.554977","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:50:50.559724","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:50:50.880375","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:50:50.884093","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:51:21.025161","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:51:21.030262","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:51:21.203866","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:51:21.208498","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:51:51.565965","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:51:51.571145","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:51:51.728215","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:51:51.732596","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:52:22.366672","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:52:22.371636","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:52:22.536604","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:52:22.541610","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:52:52.842976","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:52:52.848418","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:52:53.327003","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:52:53.331475","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:53:23.693817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:53:23.697804","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:53:24.005445","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:53:24.010051","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:53:54.207907","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:53:54.213583","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:53:54.379014","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T14:53:54.384466","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:09:02.759822","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:09:02.765231","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:09:02.920306","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:09:02.924401","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:09:33.963032","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:09:33.967878","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:09:34.120713","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:09:34.125016","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:10:05.128382","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:10:05.136236","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:10:05.520727","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:10:05.525284","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:10:36.500460","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:10:36.504896","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:10:36.656262","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:10:36.661669","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:11:06.874041","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:11:06.879496","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:11:07.050869","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:11:07.055542","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:11:37.186824","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:11:37.192266","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:11:37.344995","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:11:37.349020","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:12:09.044998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:12:09.050207","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:12:09.210907","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:12:09.215336","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:12:40.283137","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:12:40.288140","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:12:40.618908","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:12:40.623576","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:13:11.773448","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:13:11.779147","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:13:11.930152","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:13:11.934588","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:13:43.083142","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:13:43.086836","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:13:43.238775","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:13:43.242776","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:14:14.492026","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:14:14.496864","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:14:14.679815","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:14:14.684438","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:14:45.785092","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:14:45.789368","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:14:46.108340","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:14:46.112383","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:15:17.166501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:15:17.172664","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:15:17.338622","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:15:17.344363","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:15:48.643780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:15:48.648888","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:15:48.794356","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:15:48.798507","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:16:20.122477","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:16:20.126978","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:16:20.284638","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:16:20.289051","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:16:51.387288","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:16:51.391931","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:16:51.718340","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:16:51.722068","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:17:22.851545","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:17:22.855553","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:17:23.015742","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:17:23.019982","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:17:54.385994","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:17:54.391711","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:17:54.565589","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:17:54.887732","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:18:25.938354","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:18:25.943333","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:18:26.109344","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:18:26.114195","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:18:57.416516","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:18:57.423368","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:18:57.866217","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:18:58.004345","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:19:29.045878","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:19:29.050526","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:19:29.195033","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:19:29.198632","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:20:00.479259","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:20:00.485986","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:20:00.665929","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:20:00.990457","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:20:32.031184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:20:32.036954","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:20:32.196175","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:20:32.329117","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:21:03.321550","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:21:03.327779","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:21:03.761003","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:21:03.892644","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:21:34.822750","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:21:34.827933","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:21:34.973943","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:21:34.978299","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:22:06.246271","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:22:06.251999","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:22:06.415875","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:22:06.818527","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:22:37.867569","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:22:37.872377","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:22:38.019352","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:22:38.027118","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:23:09.155200","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:23:09.160313","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:23:09.548121","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:23:09.681325","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:23:40.623190","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:23:40.627957","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:23:40.797977","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:23:41.179159","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:24:12.130631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:24:12.143985","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:24:12.314533","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:24:12.709762","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:24:43.638873","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:24:43.643321","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:24:43.799640","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:24:43.944272","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:25:14.914030","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:25:14.919608","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:25:15.487362","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:25:15.494796","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:25:46.559085","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:25:46.568199","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:25:46.969145","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:25:46.976421","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:26:18.079984","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:26:18.085200","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:26:18.255578","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:26:18.594171","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:26:49.633041","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:26:49.638642","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:26:49.822687","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:26:49.976817","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:27:20.988253","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:27:20.993884","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:27:21.357895","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:27:21.363644","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:27:52.457819","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:27:52.467543","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:27:52.648614","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:27:52.653946","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:28:23.154816","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:28:23.164125","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:28:23.375150","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:28:23.383709","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:28:53.697691","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:28:53.705846","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:28:53.903784","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:28:53.912183","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:29:24.079763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:29:24.084874","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:29:24.472783","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:29:24.477545","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:29:54.933802","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:29:54.941185","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:29:55.334848","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:29:55.341890","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:30:26.431922","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:30:26.444473","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:30:26.639236","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:30:26.644832","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:30:56.873996","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:30:56.880350","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:30:57.072063","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:30:57.078504","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:31:27.530418","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:31:27.537828","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:31:27.714873","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:31:27.719968","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:31:58.797798","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:31:58.804932","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:31:59.160479","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:31:59.165505","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:32:30.452195","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:32:30.459061","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:32:30.637735","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:32:30.642750","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:33:01.075088","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:33:01.080253","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:33:01.248009","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:33:01.252543","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:33:31.499585","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:33:31.504625","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:33:31.668367","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:33:31.672748","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:34:02.020535","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:34:02.030303","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:34:02.211958","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:34:02.216476","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:34:32.357803","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:34:32.363008","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:34:32.726649","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:34:32.730886","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:35:03.880353","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:35:03.888037","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:35:04.079121","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:35:04.083534","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:35:34.413297","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:35:34.418839","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:35:34.583915","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:35:34.588242","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:36:04.741783","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:36:04.747689","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:36:04.915230","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:36:04.919870","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:36:35.855652","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:36:35.862270","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:36:36.198754","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:36:36.202737","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:37:07.223147","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:37:07.229110","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:37:07.409992","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:37:07.416444","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:37:37.812074","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:37:37.816685","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:37:37.963105","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:37:37.967461","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:38:08.221971","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:38:08.227175","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:38:08.391624","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:38:08.396176","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:38:38.539609","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:38:38.545000","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:38:38.969660","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:38:38.974771","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:39:09.195596","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:39:09.202087","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:39:09.378299","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:39:09.382938","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:39:40.047408","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:39:40.052672","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:39:40.213035","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:39:40.218003","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:40:10.667515","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:40:10.676357","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:40:10.878719","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:40:10.884040","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:40:41.110976","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:40:41.116310","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:40:41.459227","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:40:41.463053","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:41:11.620965","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:41:11.626280","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:41:11.793142","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:41:11.797534","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:41:42.242159","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:41:42.247771","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:41:42.412722","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:41:42.417040","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:42:12.687799","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:42:12.695576","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:42:12.873119","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:42:12.877527","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:42:43.115360","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:42:43.121008","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:42:43.492969","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:42:43.497009","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:43:13.693386","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:43:13.700334","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:43:13.859489","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:43:13.863880","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:43:44.506558","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:43:44.520647","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:43:44.732896","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:43:44.739417","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:44:15.034563","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:44:15.040115","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:44:15.209472","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:44:15.214264","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:44:45.385674","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:44:45.391108","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:44:45.779409","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:44:45.783579","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:45:15.969586","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:45:15.975174","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:45:16.159364","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:45:16.164068","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:45:46.805445","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:45:46.811440","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:45:46.979432","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:45:46.983264","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:46:17.291821","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:46:17.297549","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:46:17.484241","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:46:17.488802","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:46:47.674867","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:46:47.680428","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:46:48.042453","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:46:48.046614","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:47:18.190466","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:47:18.196487","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:47:18.393185","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:47:18.400286","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:47:48.800574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:47:48.805278","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:47:48.973136","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:47:48.977558","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:48:19.958712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:48:19.963846","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:48:20.128464","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:48:20.132532","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:48:51.231874","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:48:51.236032","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:48:51.588888","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:48:51.592654","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:49:21.715017","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:49:21.720354","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:49:21.879539","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:49:21.884206","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:49:52.265111","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:49:52.272752","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:49:52.485461","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:49:52.490124","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:50:22.891556","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:50:22.897115","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:50:23.064949","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:50:23.069206","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:50:54.148515","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:50:54.153441","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:50:54.472834","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:50:54.476994","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:51:24.658395","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:51:24.663533","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:51:24.815971","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:51:24.820041","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:51:55.186744","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:51:55.191977","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:51:55.333103","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:51:55.337012","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:52:25.781768","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:52:25.787738","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:52:25.963393","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:52:25.967956","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:52:56.100869","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:52:56.105595","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:52:56.461910","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:52:56.466049","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:53:26.619979","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:53:26.624579","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:53:26.781009","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:53:26.785292","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:53:57.307060","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:53:57.313201","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:53:57.468354","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:53:57.473162","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:54:27.888450","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:54:27.893622","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:54:28.047773","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:54:28.051581","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:54:59.315499","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:54:59.338837","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:54:59.839302","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T15:54:59.843866","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:10:17.369570","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:10:17.375186","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:10:17.528646","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:10:17.532498","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:10:47.803316","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:10:47.809669","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:10:47.977350","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:10:47.981228","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:11:18.388843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:11:18.396738","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:11:18.571691","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:11:18.576553","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:11:49.522188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:11:49.527033","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:11:49.860090","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:11:49.864257","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:12:20.917474","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:12:20.922937","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:12:21.291124","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:12:21.295250","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:12:52.296511","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:12:52.303847","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:12:52.477746","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:12:52.483233","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:13:22.718797","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:13:22.726003","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:13:22.914941","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:13:22.919741","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:13:53.489403","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:13:53.498016","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:13:53.685677","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:13:53.690362","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:14:23.840288","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:14:23.847389","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:14:24.207167","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:14:24.211483","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:14:55.400006","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:14:55.413074","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:14:55.579439","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:14:55.584859","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:15:26.326314","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:15:26.334345","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:15:26.511913","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:15:26.516249","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:15:56.760976","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:15:56.767674","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:15:56.952274","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:15:56.956862","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:16:27.649748","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:16:27.659141","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:16:27.847896","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:16:27.853329","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:16:58.131005","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:16:58.144012","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:16:58.540207","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:16:58.544862","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:17:29.621101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:17:29.629435","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:17:29.821496","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:17:29.830300","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:18:00.225746","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:18:00.230483","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:18:00.401716","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:18:00.406321","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:18:30.698004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:18:30.705167","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:18:30.872749","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:18:30.877519","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:19:01.193830","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:19:01.200853","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:19:01.596992","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:19:01.601473","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:19:32.008262","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:19:32.017253","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:19:32.470814","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:19:32.475269","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:20:03.500252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:20:03.505481","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:20:03.681168","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:20:03.685901","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:20:33.923338","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:20:33.927952","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:20:34.109072","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:20:34.115086","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:21:05.185866","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:21:05.191181","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:21:05.578177","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:21:05.582269","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:21:36.573649","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:21:36.588057","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:21:36.807218","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:21:36.811612","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:22:07.137568","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:22:07.143703","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:22:07.327225","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:22:07.331748","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:22:37.516312","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:22:37.521439","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:22:37.686805","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:22:37.692681","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:23:08.746647","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:23:08.752106","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:23:09.121051","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:23:09.125255","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:23:40.137337","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:23:40.142851","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:23:40.304875","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:23:40.309077","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:24:10.432782","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:24:10.437887","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:24:10.585508","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:24:10.589997","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:24:40.721829","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:24:40.726215","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:24:41.080049","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:24:41.084362","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:25:12.548396","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:25:12.553615","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:25:12.946768","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:25:12.951837","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:25:43.259262","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:25:43.265197","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:25:43.459365","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:25:43.464663","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:26:13.772217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:26:13.777971","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:26:13.952828","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:26:13.958199","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:26:44.365849","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:26:44.370048","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:26:44.519520","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:26:44.523432","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:27:14.745457","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:27:14.750208","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:27:15.163539","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:27:15.167600","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:27:45.312854","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:27:45.318539","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:27:45.464828","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:27:45.469218","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:28:15.784888","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:28:15.789551","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:28:15.952326","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:28:15.956723","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:28:46.820748","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:28:46.825390","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:28:46.972406","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:28:46.979425","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:29:18.256877","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:29:18.261783","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:29:18.604979","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:29:18.609300","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:29:49.735058","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:29:49.740125","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:29:49.909630","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:29:49.914281","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:30:21.368648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:30:21.373311","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:30:21.537791","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:30:21.542231","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:30:52.883760","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:30:52.889463","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:30:53.059335","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:30:53.066997","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:31:24.234445","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:31:24.239482","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:31:24.762073","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:31:24.766729","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:31:55.932032","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:31:55.936601","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:31:56.101244","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:31:56.105658","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:32:27.516084","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:32:27.522247","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:32:27.682282","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:32:27.686695","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:32:58.514433","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:32:58.521343","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:32:59.004507","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:32:59.147516","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:33:30.396481","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:33:30.401682","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:33:30.743013","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:33:30.747139","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:34:01.810899","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:34:01.816110","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:34:01.987080","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:34:02.324069","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:34:33.299055","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:34:33.311983","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:34:33.637887","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:34:33.644365","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:35:04.573851","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:35:04.580971","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:35:05.036349","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:35:05.161095","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:35:36.162334","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:35:36.168674","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:35:36.397941","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:35:36.827972","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:36:07.871561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:36:07.876376","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:36:08.048722","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:36:08.181396","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:36:39.094846","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:36:39.099721","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:36:39.479465","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:36:39.628234","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:37:10.599583","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:37:10.604453","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:37:10.768869","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:37:11.128019","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:37:41.432613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:37:41.446612","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:37:41.608702","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:37:41.836398","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:38:12.389063","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:38:12.403442","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:38:13.316328","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:38:13.505816","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:38:47.383860","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:38:47.406588","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:38:49.234863","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:38:49.573697","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:39:20.693776","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:39:20.717453","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:39:22.268842","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:39:22.286133","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:39:52.939078","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:39:52.950150","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:39:53.410717","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:39:53.481187","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:40:24.882678","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:40:24.931851","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:40:26.283409","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:40:26.305018","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:40:57.720304","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:40:57.735316","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:41:00.052462","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:41:00.087207","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:41:32.474661","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:41:32.670167","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:41:33.277070","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:41:33.682292","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:42:06.161742","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:42:06.186803","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:42:08.102975","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:42:08.115920","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:42:38.765678","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:42:38.793592","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:42:39.447342","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:42:39.453106","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:43:11.254793","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:43:11.281971","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:43:11.534818","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:43:11.576916","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:43:42.523088","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:43:42.529267","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:43:42.677762","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:43:42.685678","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:44:13.984645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:44:13.989411","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:44:14.395360","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:44:14.399480","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:44:45.472975","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:44:45.477728","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:44:45.612185","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:44:45.617621","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:45:16.828935","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:45:16.834896","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:45:16.982877","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:45:16.986866","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:45:47.856182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:45:47.861232","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:45:48.131750","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:45:48.136779","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:46:19.329963","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:46:19.335507","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:46:19.468352","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:46:19.473457","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:46:50.729013","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:46:50.733845","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:46:50.875696","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:46:50.882536","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:47:22.172269","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:47:22.178312","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:47:22.325485","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:47:22.329602","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:47:53.348976","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:47:53.353458","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:47:53.676184","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:47:53.679913","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:48:24.744105","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:48:24.749271","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:48:24.912040","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:48:24.917152","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:48:55.172311","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln/eln_parser.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:48:55.176209","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:48:55.316610","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-06T16:48:55.320718","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
