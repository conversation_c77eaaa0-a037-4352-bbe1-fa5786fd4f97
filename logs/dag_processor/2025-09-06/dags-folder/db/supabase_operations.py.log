{"timestamp":"2025-09-06T09:12:57.416008","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:13:27.769533","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:13:58.456328","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:14:29.216651","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:14:59.699548","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:15:31.663807","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:16:04.013613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:16:35.018819","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:17:05.926231","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:17:36.864048","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:18:07.391371","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:18:38.663791","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:19:09.436584","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:19:40.607153","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:20:11.360689","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:20:42.525873","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:21:13.011635","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:21:44.224851","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:22:14.823736","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:22:45.675052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:23:16.533703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:23:46.983905","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:24:17.956652","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:24:48.220006","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:25:19.483544","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:25:49.663889","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:26:20.178149","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:26:51.443645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:27:22.288725","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:27:53.062968","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:28:24.059924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:28:54.548581","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:29:25.096438","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:29:55.624722","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:30:26.760942","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:30:57.348805","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:31:28.516834","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:31:59.625660","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:32:30.551029","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:33:01.298780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:33:32.101866","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:34:02.941138","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:34:34.106891","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:35:04.988662","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:35:35.995257","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:36:07.308071","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:36:38.035041","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:37:09.082692","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:37:40.074132","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:38:10.733189","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:38:41.735666","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:39:12.658188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:39:43.192460","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:40:13.890730","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:40:44.479038","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:41:15.927829","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:41:47.102624","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:42:17.836201","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:42:48.951561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:43:21.031488","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:43:51.460453","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:44:22.140621","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:44:52.952861","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:45:23.646941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:45:54.447570","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:46:25.127477","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:46:56.579126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:47:27.551464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:47:58.357939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:48:28.514715","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:48:59.426016","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:49:30.726497","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:50:01.635858","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:50:33.145003","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:51:04.133412","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:51:34.771245","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:52:05.566288","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:52:37.829009","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:53:08.040805","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:53:38.890294","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:54:10.076490","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:54:41.095975","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:55:12.120455","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:55:42.948018","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:56:13.866722","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:56:45.630436","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:57:16.788200","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:57:47.133733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:58:17.968637","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:58:48.907944","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:59:19.585819","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:59:50.479464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:00:21.439937","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:00:52.149342","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:01:22.468912","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:01:54.130383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:02:25.243558","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:02:55.807813","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:03:26.305330","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:03:57.096348","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:04:28.214307","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:04:58.918821","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:05:30.192996","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:06:01.144753","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:06:31.654609","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:07:04.480490","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:07:35.169549","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:08:06.888289","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:08:38.568733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:09:09.437228","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:09:39.964375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:10:11.021753","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:10:41.763774","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:11:12.985536","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:11:43.898747","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:12:14.563395","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:12:45.406982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:13:17.115245","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:13:47.651010","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:14:19.558487","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:14:50.701471","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:15:21.150894","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:15:51.647117","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:16:22.926137","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:16:53.831383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:17:24.465805","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:17:55.443892","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:18:26.470843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:18:57.054739","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:19:28.066901","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:19:58.911562","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:20:29.609999","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:21:00.615068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:21:31.383459","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:22:04.035244","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:22:35.360195","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:23:05.969702","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:23:36.872235","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:24:07.964275","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:24:38.544675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:25:10.392930","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:25:40.769521","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:26:11.710604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:26:42.702830","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:27:13.267892","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:27:44.085591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:28:15.000377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:28:45.577366","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:29:17.319287","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:29:48.098550","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:30:18.694437","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:30:49.619688","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:31:20.240959","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:31:50.969117","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:32:22.466976","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:32:53.026337","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:33:23.892131","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:33:54.592112","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:34:26.053578","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:34:57.417081","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:35:28.336401","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:35:59.000935","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:36:29.967583","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:37:00.746336","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:37:32.490415","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:38:03.814004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:38:35.827083","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:39:06.956892","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:39:37.967897","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:40:08.898732","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:40:39.889250","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:41:10.652430","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:41:42.397804","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:42:13.833712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:42:44.467887","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:43:15.499694","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:43:46.144658","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:44:17.490042","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:44:49.055811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:52:34.668776","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:53:13.283609","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:53:44.477033","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:54:15.451203","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:54:46.835798","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:55:18.143077","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:55:49.363030","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:56:20.083577","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:56:51.369121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:57:22.875392","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:57:53.955039","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:58:25.251820","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:58:56.557894","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:59:27.960054","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:59:59.118978","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:00:30.289642","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:01:01.284693","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:01:32.604428","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:02:03.812030","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:02:34.176210","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:03:05.344689","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:03:36.345254","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:04:07.641945","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:04:38.792109","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:05:09.900545","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:05:41.071491","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:06:11.832618","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:06:42.365579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:07:13.440010","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:07:44.817277","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:08:16.211973","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:08:47.495569","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:09:17.905387","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:09:48.408877","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:10:19.553805","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:10:51.029147","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:11:22.603821","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:11:53.494470","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:40:37.578816","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:41:08.804551","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:41:39.821870","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:42:11.170606","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:42:42.355727","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:43:13.525669","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:43:44.328338","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:44:15.456283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:44:46.663100","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:45:17.765192","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:45:49.063906","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:46:20.285517","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:46:51.179564","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:47:21.788675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:47:53.134617","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:48:24.365778","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:48:55.610680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:49:26.771100","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:49:58.353475","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:50:29.170003","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:51:00.675340","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:51:31.933988","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:52:03.389599","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:52:34.726100","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:53:06.122389","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:53:37.447587","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:54:08.855774","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:54:39.891454","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:55:11.359636","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:55:42.617977","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:56:13.969281","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:56:45.373540","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:57:15.897457","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:57:46.570115","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:58:17.999686","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:58:49.282639","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:59:19.873437","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:59:51.268572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:00:22.632574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:00:54.066126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:01:25.428965","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:01:56.798196","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:02:28.443465","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:02:59.784030","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:03:31.327227","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:04:02.785049","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:04:34.193210","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:05:05.784800","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:05:37.012094","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:06:07.536843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:06:39.013609","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:07:09.453202","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:07:39.893833","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:08:10.729842","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:08:41.280171","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:09:12.808163","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:09:44.222272","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:10:15.744162","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:10:47.061370","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:11:17.650531","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:11:48.905442","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:12:20.298415","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:12:51.624888","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:13:23.013959","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:13:54.224051","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:14:25.633224","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:14:56.998106","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:15:28.362391","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:15:59.860865","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:16:31.296583","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:17:02.785143","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:17:34.238064","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:18:05.754779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:18:37.124761","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:19:08.588831","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:19:40.085655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:20:11.510707","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:20:42.759351","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:21:13.706717","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:21:45.059739","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:22:16.481108","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:22:47.865988","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:23:19.093983","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:23:50.154729","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:24:20.851982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:24:52.310893","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:25:28.999361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:26:00.067736","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:26:31.956258","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:27:03.018325","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:27:33.184424","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:28:04.680396","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:28:35.075600","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:29:05.514770","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:29:35.873785","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:30:06.504858","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:30:36.884682","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:31:07.551151","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:31:37.995596","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:32:08.540214","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:32:39.293312","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:33:09.549262","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:51:26.010091","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:51:57.411224","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:52:29.084178","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:53:00.262750","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:53:31.316495","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:54:01.914202","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:54:32.386174","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:55:04.287522","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:55:35.852712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:56:07.353858","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:56:38.681172","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:57:10.070531","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:57:40.462669","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:58:10.813169","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:58:42.364125","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:59:13.481853","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:59:44.914744","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:00:15.311942","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:00:46.059402","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:01:16.693687","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:01:47.071001","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:02:17.363786","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:02:47.877607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:03:19.273633","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:03:50.068793","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:04:20.743876","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:04:51.957803","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:05:22.637890","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:05:53.812676","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:06:24.975141","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:06:56.357161","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:07:27.802655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:07:59.229396","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:08:30.417010","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:09:01.891290","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:09:33.319470","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:10:04.992616","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:10:36.456442","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:11:07.793169","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:11:39.351014","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:12:10.771843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:12:42.232957","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:13:13.490959","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:13:44.874939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:14:16.365555","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:14:47.843728","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:15:19.100868","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:15:50.027150","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:16:21.455152","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:16:52.800039","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:17:24.159481","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:17:55.404320","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:18:26.957698","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:18:58.488157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:19:30.071220","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:20:01.380264","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:20:32.388229","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:21:03.147045","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:21:34.664871","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:22:06.300531","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:22:37.673184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:23:08.938428","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:23:40.438856","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:24:11.608019","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:24:42.068106","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:25:12.495984","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:25:43.865091","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:26:15.102210","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:26:46.471397","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:27:16.847143","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:27:47.876020","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:28:18.175657","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:28:48.432634","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:29:20.280972","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:29:50.779026","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:30:21.591252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:30:51.982436","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:31:23.066009","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:31:53.619793","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:32:23.975674","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:32:54.433053","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:33:24.969570","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:33:55.428041","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:34:26.018985","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:34:56.410945","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:35:27.077479","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:35:57.864248","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:36:28.376022","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:36:59.295325","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:37:30.003953","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:38:00.560333","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:38:31.399250","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:39:02.215078","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:39:33.222713","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:40:04.267451","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:40:35.230358","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:41:06.101343","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:41:36.628536","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:42:07.534192","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:42:38.107524","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:43:08.946609","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:43:39.703441","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:44:10.234872","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:44:41.024661","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:45:11.530061","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:45:42.000773","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:46:12.865474","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:46:43.452040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:47:14.008766","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:47:44.429127","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:48:14.661216","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:48:45.024178","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:49:15.463603","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:49:45.823903","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:50:16.152538","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:50:46.400327","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:51:16.771496","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:51:47.391679","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:52:18.210136","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:52:48.653862","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:53:19.354045","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:53:50.017535","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:09:03.042700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:09:34.248685","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:10:05.652072","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:10:37.094549","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:11:08.213503","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:11:39.559519","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:12:10.345751","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:12:40.902874","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:13:12.292088","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:13:43.607668","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:14:14.915736","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:14:46.325882","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:15:17.813275","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:15:49.132015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:16:20.531943","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:16:51.928785","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:17:23.400326","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:17:54.984874","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:18:26.359101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:18:58.118200","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:19:29.563128","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:20:01.092725","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:20:32.417570","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:21:03.983251","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:21:35.347553","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:22:06.961153","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:22:38.242173","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:23:09.779730","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:23:41.293757","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:24:12.818294","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:24:44.043569","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:25:15.609188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:25:47.209713","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:26:18.692889","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:26:50.083099","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:27:21.461245","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:27:52.753990","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:28:23.443510","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:28:53.967754","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:29:24.543236","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:29:55.412308","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:30:26.716543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:30:57.147274","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:31:27.825811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:31:59.531625","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:32:31.325376","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:33:02.432736","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:33:32.842244","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:34:03.589947","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:34:34.013326","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:35:04.225277","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:35:34.682547","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:36:05.010004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:36:36.293006","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:37:07.547172","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:37:38.049170","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:38:08.483660","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:38:39.093145","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:39:09.493573","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:39:40.311062","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:40:10.984252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:40:41.567756","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:41:11.887881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:41:42.506011","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:42:12.969390","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:42:43.588667","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:43:13.951632","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:43:44.835386","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:44:15.306064","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:44:45.891581","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:45:16.267422","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:45:47.071998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:46:18.631045","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:46:49.176503","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:47:19.532907","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:47:50.108204","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:48:20.219617","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:48:51.688487","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:49:21.996158","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:49:52.614240","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:50:23.163037","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:50:54.568555","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:51:24.902524","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:51:55.522991","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:52:26.063896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:52:56.648718","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:53:26.878691","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:53:57.574896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:54:28.135953","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:54:59.956055","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:10:17.642178","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:10:48.091953","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:11:18.664893","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:11:49.983688","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:12:21.420461","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:12:52.630322","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:13:23.022058","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:13:53.842776","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:14:24.353187","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:14:56.243538","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:15:26.647976","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:15:57.094518","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:16:27.958353","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:16:58.675584","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:17:30.361655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:18:01.594758","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:18:32.063683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:19:02.977898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:19:33.682241","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:20:04.879305","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:20:35.297644","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:21:05.728744","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:21:36.931029","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:22:07.437717","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:22:37.779724","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:23:09.228601","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:23:40.445739","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:24:11.758316","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:24:42.573702","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:25:13.071795","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:25:43.528779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:26:14.025211","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:26:44.573165","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:27:15.227775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:27:45.516679","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:28:16.009693","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:28:47.254383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:29:18.839047","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:29:50.381414","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:30:21.963555","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:30:53.323020","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:31:25.006565","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:31:56.529207","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:32:27.911708","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:32:59.496079","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:33:30.960839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:34:02.428368","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:34:33.739857","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:35:05.268085","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:35:36.944697","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:36:08.280393","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:36:39.757899","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:37:11.221728","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:37:41.944376","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:38:14.111873","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:38:49.878867","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:39:23.158685","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:39:53.915049","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:40:27.057065","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:41:00.451419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:41:34.395058","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:42:08.472329","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:42:39.746038","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:43:12.589090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:43:42.945117","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:44:14.624026","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:44:45.960290","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:45:17.212031","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:45:48.383555","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:46:19.820565","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:46:51.273667","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:47:22.417364","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:47:53.776136","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:48:25.009127","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:48:56.454871","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/supabase_operations.py","logger":"airflow.models.dagbag.DagBag"}
