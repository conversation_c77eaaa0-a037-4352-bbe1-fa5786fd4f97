{"timestamp":"2025-09-06T09:12:57.618685","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:13:28.290954","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:13:58.663208","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:14:29.418189","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:14:59.933345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:15:31.929973","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:16:04.156702","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:16:35.069526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:17:06.050902","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:17:36.972916","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:18:08.756739","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:18:39.090767","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:19:09.959034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:19:40.680550","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:20:11.616180","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:20:42.646499","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:21:13.123590","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:21:44.347569","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:22:15.379831","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:22:45.862530","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:23:16.715321","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:23:47.424816","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:24:18.035291","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:24:48.944998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:25:19.498349","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:25:49.728942","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:26:20.276972","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:26:51.510426","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:27:22.357254","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:27:54.458170","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:28:25.223785","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:28:55.705099","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:29:26.593209","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:29:57.928964","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:30:29.058016","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:30:59.627101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:31:29.790985","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:32:00.782187","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:32:31.726211","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:33:02.472848","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:33:33.285733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:34:04.141475","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:34:35.243549","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:35:06.167350","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:35:37.245445","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:36:07.430931","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:36:38.101718","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:37:09.130106","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:37:40.146433","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:38:10.776057","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:38:41.816799","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:39:12.728425","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:39:43.240571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:40:15.063336","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:40:45.901373","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:41:17.201213","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:41:48.346996","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:42:19.015720","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:42:50.125429","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:43:21.046041","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:43:51.535000","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:44:22.208354","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:44:53.033806","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:45:23.720588","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:45:54.507730","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:46:26.257630","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:46:57.712210","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:47:28.726628","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:47:59.509791","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:48:29.683487","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:49:00.555283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:49:30.796461","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:50:02.904048","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:50:33.479700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:51:05.309756","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:51:35.946968","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:52:07.291312","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:52:38.995383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:53:09.354317","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:53:40.099562","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:54:11.244116","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:54:42.312144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:55:13.292616","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:55:44.090636","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:56:15.045898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:56:45.646304","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:57:16.801842","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:57:48.318282","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:58:19.133019","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:58:50.078878","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:59:20.762405","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:59:51.651476","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:00:22.621150","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:00:53.293606","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:01:23.636197","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:01:54.738243","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:02:25.474503","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:02:57.121229","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:03:28.062327","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:03:58.645717","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:04:29.661810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:05:00.800758","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:05:31.645903","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:06:02.556173","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:06:33.083334","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:07:04.613588","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:07:35.581676","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:08:06.971129","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:08:38.665752","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:09:09.626837","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:09:40.252927","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:10:11.402087","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:10:41.902154","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:11:13.175675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:11:44.054155","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:12:15.156281","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:12:45.666790","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:13:17.289850","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:13:47.927231","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:14:19.797937","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:14:50.784560","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:15:21.340409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:15:52.381502","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:16:22.986170","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:16:53.962315","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:17:24.512172","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:17:55.479888","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:18:26.529763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:18:57.098101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:19:28.119166","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:19:58.962092","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:20:29.702030","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:21:00.667235","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:21:32.565620","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:22:04.196303","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:22:35.566521","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:23:06.530233","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:23:37.049528","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:24:08.120003","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:24:38.820421","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:25:10.696998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:25:41.486123","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:26:11.841994","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:26:42.861926","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:27:13.398888","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:27:44.334229","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:28:15.167085","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:28:45.903827","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:29:17.441441","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:29:48.206735","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:30:19.037749","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:30:49.749586","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:31:20.383203","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:31:51.230296","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:32:22.690429","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:32:53.773129","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:33:24.090619","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:33:55.232187","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:34:26.322386","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:34:57.459907","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:35:28.383377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:35:59.045609","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:36:30.008321","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:37:00.437129","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:37:32.496048","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:38:03.814361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:38:35.849756","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:39:07.006821","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:39:38.027759","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:40:08.943992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:40:39.957864","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:41:10.710984","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:41:42.415230","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:42:13.873532","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:42:44.512154","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:43:15.556366","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:43:46.189458","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:44:17.538547","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:44:49.116452","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:52:34.960749","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:53:13.369270","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:53:44.645882","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:54:15.794321","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:54:47.237536","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:55:18.473030","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:55:49.553217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:56:21.479248","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:56:51.640977","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:57:23.107541","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:57:54.305505","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:58:25.628050","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:58:57.021251","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:59:28.127646","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:59:59.293450","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:00:30.463898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:01:01.598601","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:01:32.937551","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:02:03.991974","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:02:34.345200","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:03:05.520754","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:03:36.773659","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:04:07.934780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:04:39.089050","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:05:10.205216","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:05:41.243557","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:06:12.052314","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:06:42.579851","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:07:13.879175","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:07:45.230586","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:08:16.526779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:08:47.680054","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:09:18.102793","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:09:48.614309","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:10:20.050579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:10:51.413957","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:11:22.947967","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:11:54.786704","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:40:37.743106","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:41:08.968659","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:41:40.172616","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:42:11.512420","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:42:42.663688","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:43:13.695228","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:43:44.373638","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:44:15.623142","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:44:46.823021","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:45:18.155278","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:45:49.437885","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:46:20.459356","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:46:52.363943","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:47:22.961828","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:47:53.195205","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:48:24.394378","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:48:55.648098","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:49:26.794081","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:49:58.384695","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:50:29.191992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:51:00.696627","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:51:32.005419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:52:03.474199","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:52:34.748737","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:53:06.149617","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:53:37.480336","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:54:08.876133","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:54:39.914854","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:55:11.391851","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:55:42.648369","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:56:14.006785","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:56:45.380070","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:57:15.923383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:57:46.725842","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:58:18.110652","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:58:49.305678","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:59:19.896518","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:59:51.298877","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:00:22.654521","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:00:54.089897","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:01:25.455527","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:01:56.827209","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:02:28.482050","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:02:59.810894","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:03:31.351365","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:04:02.801405","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:04:34.236277","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:05:05.843988","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:05:37.205755","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:06:07.732745","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:06:39.179593","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:07:09.863523","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:07:39.971658","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:08:10.898778","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:08:41.342269","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:09:12.834369","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:09:44.244358","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:10:15.773701","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:10:47.093297","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:11:17.698115","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:11:48.926759","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:12:20.326731","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:12:51.648861","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:13:23.038356","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:13:54.256418","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:14:25.672800","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:14:57.020984","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:15:28.392039","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:15:59.896376","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:16:31.331260","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:17:02.815288","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:17:34.277899","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:18:05.784753","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:18:37.156671","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:19:08.615962","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:19:40.112011","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:20:11.533533","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:20:42.785280","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:21:13.732606","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:21:45.083106","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:22:16.506530","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:22:47.892775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:23:19.128561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:23:50.175489","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:24:20.945308","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:24:52.346395","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:25:29.671644","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:26:00.515397","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:26:32.467294","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:27:03.374030","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:27:34.901373","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:28:06.362809","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:28:37.763116","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:29:09.151199","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:29:40.761026","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:30:12.201112","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:30:43.742625","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:31:14.352414","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:31:45.579520","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:32:16.571242","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:32:48.003377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:33:19.340663","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:51:26.182225","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:51:57.662121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:52:29.134520","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:53:00.452074","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:53:31.730631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:54:01.939050","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:54:32.422746","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:55:04.478274","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:55:36.054739","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:56:07.544355","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:56:39.049965","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:57:10.478196","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:57:40.574658","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:58:10.899742","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:58:42.565716","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:59:13.896005","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:59:45.289099","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:00:15.538466","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:00:46.239462","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:01:16.856263","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:01:47.265385","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:02:18.917631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:02:49.355987","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:03:19.466340","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:03:50.229498","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:04:21.906434","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:04:52.177133","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:05:22.697722","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:05:53.840443","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:06:24.998928","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:06:56.381004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:07:27.826481","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:07:59.263109","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:08:30.442663","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:09:01.941609","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:09:33.348166","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:10:05.043409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:10:36.482455","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:11:07.830327","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:11:39.381738","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:12:10.798637","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:12:42.257052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:13:13.518989","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:13:44.910575","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:14:16.396645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:14:47.885987","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:15:19.134076","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:15:50.050868","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:16:21.488265","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:16:52.824104","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:17:24.187199","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:17:55.428006","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:18:26.986617","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:18:58.518710","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:19:30.098003","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:20:01.407556","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:20:32.541587","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:21:03.176214","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:21:34.698934","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:22:06.337937","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:22:37.711470","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:23:09.116419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:23:40.636215","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:24:11.967753","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:24:42.464616","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:25:12.728001","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:25:44.044412","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:26:15.473108","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:26:46.848429","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:27:17.115806","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:27:49.048761","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:28:19.446248","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:28:49.771655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:29:20.288406","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:29:50.790103","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:30:21.594849","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:30:52.052964","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:31:23.262844","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:31:53.867307","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:32:24.247233","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:32:54.808982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:33:24.976277","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:33:55.430804","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:34:26.231015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:34:56.627571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:35:27.715293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:35:59.347124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:36:29.696608","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:37:00.598373","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:37:31.306931","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:38:02.372170","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:38:32.904901","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:39:03.597537","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:39:34.565451","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:40:05.845940","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:40:36.889468","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:41:07.573428","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:41:37.970683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:42:08.839900","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:42:39.365664","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:43:09.537542","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:43:40.148522","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:44:10.447026","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:44:42.326607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:45:12.922326","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:45:43.724273","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:46:14.518271","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:46:44.777588","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:47:15.119831","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:47:45.576493","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:48:16.794210","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:48:48.184217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:49:18.631720","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:49:48.995036","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:50:19.330298","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:50:50.562344","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:51:20.967498","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:51:51.568468","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:52:22.372987","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:52:52.851468","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:53:23.575376","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:53:54.207907","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:09:03.366504","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:09:34.472342","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:10:05.852612","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:10:37.265628","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:11:08.569490","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:11:39.753906","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:12:10.561105","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:12:40.957036","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:13:12.326047","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:13:43.638642","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:14:14.942339","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:14:46.356319","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:15:17.842470","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:15:49.156227","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:16:20.563591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:16:51.958600","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:17:23.428668","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:17:55.010324","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:18:26.394029","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:18:58.142052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:19:29.594089","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:20:01.117652","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:20:32.444588","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:21:04.008032","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:21:35.375135","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:22:06.997377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:22:38.267181","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:23:09.811444","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:23:41.323832","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:24:12.851895","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:24:44.076929","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:25:15.639871","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:25:47.243020","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:26:18.729357","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:26:50.110206","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:27:21.638905","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:27:53.281207","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:28:23.483514","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:28:54.011934","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:29:24.643910","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:29:55.452010","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:30:26.756345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:30:57.252658","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:31:27.880865","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:31:59.734948","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:32:31.415833","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:33:02.853089","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:33:33.353872","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:34:03.691701","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:34:34.104746","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:35:05.830764","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:35:36.184401","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:36:07.162420","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:36:37.435492","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:37:08.699192","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:37:39.167952","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:38:09.625846","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:38:40.238907","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:39:10.841918","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:39:41.483738","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:40:12.137234","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:40:42.714882","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:41:13.034279","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:41:43.647047","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:42:14.124183","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:42:44.690814","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:43:15.066243","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:43:45.976163","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:44:16.447374","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:44:47.042217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:45:17.428836","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:45:48.184900","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:46:18.637745","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:46:49.233428","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:47:19.582376","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:47:50.163511","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:48:20.267366","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:48:51.706304","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:49:22.019109","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:49:52.689218","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:50:23.180812","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:50:54.585082","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:51:24.948683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:51:55.419851","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:52:26.084025","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:52:56.674893","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:53:26.896418","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:53:57.595660","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:54:28.183242","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:54:59.975941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:10:18.043704","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:10:48.137053","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:11:18.713136","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:11:50.198611","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:12:21.622100","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:12:52.979144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:13:23.120379","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:13:54.042372","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:14:25.617380","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:14:56.433011","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:15:26.995946","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:15:57.297416","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:16:29.283641","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:16:59.927531","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:17:30.541664","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:18:02.013494","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:18:32.190980","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:19:03.095480","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:19:33.846406","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:20:05.284117","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:20:35.387226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:21:05.911213","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:21:37.314748","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:22:07.448967","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:22:37.832356","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:23:09.275769","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:23:40.855615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:24:11.979351","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:24:43.805634","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:25:14.459990","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:25:45.101459","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:26:15.666149","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:26:45.873259","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:27:16.601691","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:27:47.087989","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:28:17.387082","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:28:48.453583","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:29:18.872103","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:29:50.418295","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:30:21.996073","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:30:53.355595","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:31:25.050805","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:31:56.563921","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:32:27.951746","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:32:59.522090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:33:30.988619","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:34:02.458447","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:34:33.769879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:35:05.306779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:35:36.974157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:36:08.313501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:36:39.807465","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:37:11.252885","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:37:42.258846","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:38:14.161427","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:38:49.951949","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:39:23.442168","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:39:53.964673","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:40:27.213978","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:41:00.494499","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:41:34.852033","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:42:08.532505","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:42:39.865634","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:43:12.661060","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:43:43.058598","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:44:14.650064","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:44:45.984808","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:45:17.249508","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:45:48.415955","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:46:19.846201","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:46:51.312749","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:47:22.590760","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:47:53.943493","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:48:25.421573","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:48:56.646637","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/s3_operations.py","logger":"airflow.models.dagbag.DagBag"}
