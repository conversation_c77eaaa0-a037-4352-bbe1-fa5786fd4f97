{"timestamp":"2025-09-06T09:12:57.685127","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:13:28.354189","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:13:58.745508","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:14:29.518664","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:15:00.052002","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:15:32.017799","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:16:04.182580","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:16:35.375574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:17:06.078878","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:17:36.994267","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:18:07.587939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:18:39.009764","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:19:10.030284","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:19:40.727817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:20:11.682874","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:20:42.668998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:21:13.169579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:21:44.311986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:22:15.498659","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:22:45.942564","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:23:16.788048","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:23:47.486941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:24:18.075768","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:24:49.072475","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:25:19.622189","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:25:49.781923","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:26:20.705425","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:26:51.564004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:27:23.507266","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:27:54.483935","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:28:25.239398","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:28:55.719492","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:29:26.601775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:29:56.799088","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:30:26.877904","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:30:58.516513","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:31:28.646201","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:31:59.700346","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:32:30.622187","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:33:02.482269","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:33:33.292446","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:34:04.182564","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:34:35.262338","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:35:06.186936","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:35:37.251671","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:36:07.516034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:36:38.152972","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:37:09.235948","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:37:40.197103","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:38:10.863462","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:38:41.893387","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:39:12.774692","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:39:44.395361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:40:15.090545","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:40:45.916272","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:41:16.019357","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:41:47.220811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:42:19.033638","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:42:50.136228","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:43:21.163511","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:43:51.598035","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:44:22.255191","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:44:54.187441","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:45:24.891957","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:45:55.660195","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:46:26.258791","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:46:56.601840","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:47:27.559052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:47:58.379429","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:48:28.562739","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:48:59.468111","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:49:30.694924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:50:01.682367","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:50:33.155799","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:51:04.206880","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:51:34.819003","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:52:05.655710","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:52:36.716498","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:53:08.168819","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:53:38.983398","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:54:10.116230","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:54:41.181204","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:55:12.170206","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:55:42.991963","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:56:13.909004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:56:44.501352","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:57:15.671147","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:57:47.143784","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:58:17.978666","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:58:48.919474","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:59:19.599343","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:59:50.489381","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:00:21.462134","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:00:52.168484","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:01:22.490383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:01:52.864891","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:02:24.102507","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:02:55.713597","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:03:26.197705","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:03:57.164382","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:04:28.252383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:04:58.971232","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:05:30.423003","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:06:01.340249","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:06:31.941687","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:07:04.558516","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:07:35.661885","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:08:07.261611","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:08:38.711774","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:09:09.699764","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:09:41.756051","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:10:12.575301","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:10:43.539267","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:11:14.360245","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:11:45.304944","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:12:16.313527","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:12:46.820994","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:13:17.428073","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:13:49.351815","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:14:19.898418","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:14:50.831399","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:15:21.406399","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:15:52.516108","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:16:23.145934","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:16:54.084325","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:17:24.623759","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:17:55.587504","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:18:26.625766","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:18:57.204436","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:19:28.223401","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:19:59.082436","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:20:29.768599","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:21:01.847229","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:21:33.269503","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:22:04.423213","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:22:35.655212","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:23:06.586147","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:23:37.355328","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:24:08.346303","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:24:38.923636","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:25:10.586700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:25:40.892632","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:26:12.048195","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:26:43.042377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:27:14.001228","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:27:44.420807","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:28:15.357922","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:28:46.014416","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:29:17.365396","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:29:48.134459","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:30:18.929371","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:30:49.954618","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:31:20.859707","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:31:51.317652","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:32:22.759019","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:32:53.882473","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:33:24.159712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:33:55.375036","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:34:26.431698","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:34:57.549902","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:35:28.490102","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:35:59.125243","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:36:30.090983","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:37:00.893538","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:37:32.617575","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:38:03.932759","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:38:35.974398","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:39:07.087981","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:39:38.101090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:40:09.042931","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:40:40.041107","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:41:10.795147","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:41:42.526199","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:42:13.990996","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:42:44.592565","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:43:15.625984","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:43:46.284968","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:44:17.633651","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:44:49.195779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:52:35.086877","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:53:13.379964","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:53:44.694200","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:54:15.865218","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:54:47.308348","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:55:18.528032","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:55:49.607493","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:56:20.363219","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:56:51.589089","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:57:23.050215","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:57:54.379517","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:58:25.668166","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:58:57.071088","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:59:28.173720","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:59:59.338188","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:00:30.511595","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:01:01.648287","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:01:32.989606","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:02:04.042471","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:02:34.388039","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:03:05.561600","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:03:36.844196","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:04:08.007158","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:04:39.131034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:05:10.252762","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:05:41.286896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:06:12.110723","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:06:42.620949","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:07:13.936375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:07:45.279896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:08:16.580600","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:08:47.743680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:09:18.171828","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:09:48.659283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:10:20.118187","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:10:51.479457","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:11:23.008383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:11:53.668864","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:40:37.782908","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:41:09.015787","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:41:40.245612","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:42:11.552996","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:42:42.714021","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:43:13.745453","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:43:44.405138","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:44:15.677051","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:44:46.870495","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:45:18.232817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:45:49.497814","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:46:20.509778","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:46:51.208958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:47:21.837304","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:47:53.157184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:48:24.450629","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:48:55.744752","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:49:26.853108","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:49:58.693455","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:50:29.247335","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:51:00.752155","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:51:32.037209","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:52:03.558345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:52:34.810604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:53:06.201880","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:53:37.529236","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:54:08.936326","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:54:39.967884","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:55:11.452582","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:55:42.700496","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:56:14.046842","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:56:45.656079","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:57:15.983723","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:57:46.644572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:58:18.035237","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:58:49.356405","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:59:19.949090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:59:51.354692","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:00:22.712519","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:00:54.144148","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:01:25.506292","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:01:56.898908","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:02:28.535195","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:02:59.869542","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:03:31.409112","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:04:02.865255","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:04:34.373797","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:05:05.879502","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:05:37.255767","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:06:07.789084","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:06:39.228053","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:07:09.942129","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:07:41.412872","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:08:12.026255","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:08:42.478731","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:09:12.890945","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:09:44.299112","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:10:15.841413","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:10:47.148981","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:11:17.744096","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:11:48.981220","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:12:20.375911","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:12:51.703462","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:13:23.099815","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:13:54.306404","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:14:25.726369","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:14:57.083175","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:15:28.442000","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:15:59.950148","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:16:31.384508","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:17:02.870252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:17:34.364608","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:18:05.835869","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:18:37.212172","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:19:08.691814","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:19:40.167776","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:20:11.592512","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:20:43.097986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:21:13.789518","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:21:45.143449","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:22:16.562445","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:22:47.945982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:23:19.426056","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:23:50.232008","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:24:21.021301","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:24:52.407550","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:25:29.787647","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:26:00.638915","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:26:33.491047","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:27:04.524139","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:27:34.937798","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:28:06.387812","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:28:37.799584","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:29:09.180175","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:29:40.795678","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:30:12.230938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:30:43.782357","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:31:14.387426","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:31:45.619933","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:32:16.599810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:32:48.030522","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:33:19.364512","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:51:26.237314","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:51:57.599607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:52:29.168714","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:53:00.503630","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:53:31.849726","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:54:02.035801","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:54:32.489579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:55:04.534216","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:55:36.109635","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:56:07.591932","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:56:39.140875","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:57:10.536600","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:57:40.752862","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:58:10.981221","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:58:42.640305","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:59:13.981984","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:59:45.340955","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:00:15.617893","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:00:46.297077","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:01:16.906428","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:01:48.401123","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:02:18.927290","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:02:49.361599","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:03:19.523252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:03:50.277318","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:04:20.824259","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:04:52.246534","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:05:22.733861","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:05:54.117718","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:06:25.062415","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:06:56.450645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:07:27.888478","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:07:59.319888","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:08:30.502097","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:09:02.013282","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:09:33.411495","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:10:05.109653","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:10:36.540937","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:11:07.894547","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:11:39.443363","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:12:10.865467","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:12:42.326870","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:13:13.578427","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:13:44.985739","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:14:16.455897","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:14:47.941322","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:15:19.210911","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:15:50.110743","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:16:21.564440","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:16:52.888305","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:17:24.513241","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:17:55.489466","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:18:27.045766","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:18:58.587323","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:19:30.161769","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:20:01.709148","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:20:32.640067","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:21:03.234447","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:21:34.779648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:22:06.391751","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:22:37.785701","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:23:09.168384","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:23:40.683913","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:24:12.022215","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:24:42.533551","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:25:12.791414","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:25:44.101191","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:26:15.586674","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:26:46.910052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:27:17.197222","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:27:47.985774","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:28:19.452351","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:28:50.156355","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:29:20.376936","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:29:50.874229","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:30:21.683371","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:30:52.095848","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:31:23.302353","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:31:53.817437","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:32:24.189556","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:32:54.902377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:33:25.062266","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:33:55.522618","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:34:26.267858","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:34:56.658173","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:35:27.532973","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:35:58.254516","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:36:29.704480","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:37:00.764258","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:37:31.314812","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:38:02.380450","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:38:32.909757","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:39:03.611003","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:39:34.565863","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:40:05.845940","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:40:36.897239","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:41:07.573235","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:41:37.994993","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:42:08.850108","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:42:39.378639","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:43:09.464057","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:43:40.195186","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:44:11.747624","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:44:42.338647","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:45:12.936335","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:45:43.730256","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:46:14.518273","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:46:44.787124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:47:15.315867","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:47:46.836071","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:48:17.192610","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:48:48.502575","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:49:18.827526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:49:49.185681","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:50:19.524834","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:50:50.936749","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:51:21.070658","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:51:51.780664","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:52:22.589296","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:52:53.402172","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:53:23.582389","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:53:54.442325","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:09:03.419840","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:09:34.531872","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:10:05.907351","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:10:37.315077","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:11:08.627533","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:11:40.008127","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:12:10.636135","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:12:41.023980","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:13:12.385337","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:13:43.699500","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:14:15.006217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:14:46.419528","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:15:17.907958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:15:49.213938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:16:20.667383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:16:52.018444","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:17:23.504753","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:17:55.078002","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:18:26.474826","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:18:58.220584","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:19:29.664194","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:20:01.183238","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:20:32.512559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:21:04.076372","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:21:35.437679","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:22:07.065324","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:22:38.346528","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:23:09.876635","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:23:41.392345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:24:12.922462","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:24:44.157800","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:25:15.714299","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:25:47.306696","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:26:18.793205","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:26:50.175903","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:27:21.694696","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:27:53.384873","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:28:23.540780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:28:54.097068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:29:24.733249","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:29:55.526798","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:30:26.822407","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:30:57.260772","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:31:27.928277","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:31:59.787864","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:32:31.434515","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:33:02.909644","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:33:33.409501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:34:03.706166","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:34:34.119768","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:35:04.611808","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:35:34.729289","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:36:05.061592","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:36:36.343369","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:37:07.571899","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:37:38.099203","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:38:09.638617","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:38:40.248567","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:39:10.855532","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:39:41.483738","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:40:12.148823","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:40:42.722761","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:41:13.040655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:41:43.647973","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:42:14.140730","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:42:44.695469","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:43:15.080805","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:43:45.985107","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:44:16.456833","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:44:47.044505","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:45:17.443976","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:45:48.193770","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:46:18.724106","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:46:49.279381","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:47:19.623287","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:47:51.313353","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:48:21.399569","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:48:51.789370","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:49:22.120879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:49:52.789389","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:50:24.294073","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:50:54.656787","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:51:25.051170","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:51:55.474568","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:52:26.158626","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:52:56.774074","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:53:27.191729","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:53:57.668882","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:54:28.228615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:55:00.059920","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:10:18.134193","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:10:48.211779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:11:18.760653","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:11:50.253268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:12:21.674212","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:12:53.037313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:13:23.567647","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:13:55.220384","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:14:25.624142","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:14:56.506445","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:15:27.060604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:15:58.819091","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:16:29.296986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:16:59.935210","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:17:30.597876","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:18:02.069600","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:18:32.397667","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:19:03.118441","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:19:33.901211","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:20:05.343736","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:20:35.549988","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:21:05.964101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:21:37.421155","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:22:07.541094","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:22:37.879662","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:23:09.323500","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:23:40.912991","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:24:12.035896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:24:42.680493","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:25:13.337118","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:25:43.977512","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:26:14.546454","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:26:44.798082","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:27:15.332308","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:27:45.618939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:28:16.048972","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:28:47.345743","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:29:18.936971","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:29:50.494574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:30:22.062699","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:30:53.464809","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:31:25.124863","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:31:56.646686","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:32:28.324437","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:32:59.609143","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:33:31.062978","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:34:02.530683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:34:33.840947","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:35:05.369788","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:35:37.078153","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:36:08.379680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:36:39.879562","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:37:11.322224","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:37:42.563200","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:38:14.304703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:38:50.396294","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:39:24.128461","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:39:55.686233","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:40:27.378160","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:41:00.739780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:41:36.027817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:42:08.576777","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:42:39.897367","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:43:12.718884","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:43:43.079780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:44:14.717480","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:44:46.050123","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:45:17.588436","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:45:48.478182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:46:19.930246","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:46:51.407335","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:47:22.649821","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:47:53.999513","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:48:25.479780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:48:56.692040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/db/postgres_operations.py","logger":"airflow.models.dagbag.DagBag"}
