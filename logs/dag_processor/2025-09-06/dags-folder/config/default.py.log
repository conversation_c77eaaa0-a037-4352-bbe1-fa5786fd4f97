{"timestamp":"2025-09-06T09:12:57.292453","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:13:27.693151","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:13:58.356077","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:14:29.104960","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:14:59.618432","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:15:31.544831","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:16:03.190126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:16:34.475149","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:17:05.624518","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:17:36.635307","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:18:07.312873","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:18:37.488966","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:19:09.349039","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:19:40.363536","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:20:11.264074","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:20:41.935358","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:21:12.779325","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:21:43.979091","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:22:14.705631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:22:45.576160","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:23:16.428779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:23:46.905485","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:24:17.703366","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:24:48.141450","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:25:18.306071","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:25:49.628974","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:26:20.145012","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:26:51.403353","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:27:22.240064","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:27:53.052582","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:28:24.026434","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:28:54.516121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:29:25.062158","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:29:55.568440","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:30:26.681117","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:30:57.297768","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:31:28.468285","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:31:59.584431","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:32:30.515418","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:33:01.262949","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:33:32.053665","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:34:02.891481","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:34:34.072870","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:35:04.937354","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:35:35.961802","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:36:07.219955","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:36:37.996060","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:37:09.041940","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:37:40.037959","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:38:10.683972","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:38:41.686653","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:39:12.614185","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:39:43.151315","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:40:13.845098","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:40:44.437709","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:41:15.866860","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:41:47.017007","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:42:17.800037","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:42:48.906225","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:43:19.853191","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:43:51.439449","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:44:22.126661","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:44:52.951394","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:45:23.634776","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:45:54.418980","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:46:25.095077","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:46:55.447088","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:47:25.925134","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:47:57.009144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:48:28.274169","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:48:58.873692","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:49:30.425974","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:50:01.386215","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:50:32.531818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:51:03.848741","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:51:34.490266","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:52:05.308308","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:52:36.485680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:53:07.430665","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:53:38.593578","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:54:09.813323","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:54:40.460123","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:55:11.773667","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:55:42.700780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:56:13.306302","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:56:44.229817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:57:15.071845","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:57:45.826618","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:58:16.660076","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:58:47.198513","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:59:18.282869","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T09:59:49.136861","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:00:19.739771","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:00:50.822880","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:01:21.799584","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:01:52.632441","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:02:23.831109","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:02:55.301585","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:03:25.977963","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:03:56.834560","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:04:27.626906","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:04:58.619098","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:05:29.916277","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:06:00.534895","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:06:31.403512","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:07:03.356106","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:07:34.602080","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:08:06.009786","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:08:37.916101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:09:08.790735","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:09:39.668129","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:10:10.424560","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:10:41.474976","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:11:12.650487","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:11:43.291276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:12:14.298733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:12:45.181315","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:13:16.118617","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:13:47.395555","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:14:18.801144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:14:49.987726","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:15:20.835818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:15:51.408508","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:16:22.683394","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:16:53.407753","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:17:24.223547","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:17:55.205467","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:18:25.862152","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:18:56.821459","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:19:27.811332","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:19:58.369152","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:20:29.290434","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:21:00.384916","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:21:31.073662","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:22:02.590377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:22:34.765687","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:23:05.605920","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:23:36.553126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:24:07.364300","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:24:38.288141","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:25:09.855703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:25:40.525406","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:26:11.440403","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:26:42.098184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:27:12.991798","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:27:43.865559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:28:14.407482","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:28:45.323569","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:29:16.435968","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:29:47.310938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:30:18.347896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:30:48.989853","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:31:19.997267","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:31:50.754027","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:32:21.661926","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:32:52.751691","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:33:23.568075","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:33:54.311712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:34:24.611111","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:34:55.464736","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:35:26.422287","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:35:57.385062","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:36:28.004834","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:36:58.869751","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:37:29.226902","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:37:59.448013","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:38:30.321045","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:39:00.853707","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:39:31.875446","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:40:03.087122","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:40:33.884596","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:41:04.954454","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:41:35.958292","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:42:06.282316","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:42:36.715266","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:43:07.300289","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:43:38.454488","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:44:09.084909","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:44:39.644550","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:45:10.347631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:52:34.560252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:53:13.075049","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:53:44.427269","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:54:15.415265","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:54:46.795541","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:55:17.050954","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:55:48.319574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:56:20.036409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:56:51.315289","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:57:22.826389","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:57:53.911012","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:58:24.170897","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:58:55.457868","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:59:25.856477","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T10:59:57.019943","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:00:28.204648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:00:59.186158","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:01:30.487517","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:02:01.714303","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:02:32.967770","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:03:04.245284","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:03:35.244840","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:04:05.545480","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:04:36.693003","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:05:07.819109","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:05:38.978646","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:06:10.695235","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:06:41.271080","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:07:12.338548","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:07:43.720004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:08:14.133871","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:08:44.407991","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:09:15.405631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:09:45.998277","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:10:17.292886","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:10:47.749972","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:11:18.094725","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T11:11:49.086462","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:40:37.536312","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:41:08.760666","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:41:39.768546","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:42:10.076120","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:42:41.255327","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:43:12.470858","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:43:44.078848","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:44:15.409758","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:44:46.620289","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:45:17.713150","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:45:47.972999","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:46:18.966669","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:46:49.103560","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:47:20.128288","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:47:50.441581","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:48:20.751473","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:48:50.958518","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:49:21.228912","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:49:52.316643","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:50:23.353468","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:50:53.714358","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:51:24.189118","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:51:54.483677","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:52:24.940489","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:52:55.240970","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:53:25.642716","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:53:55.942230","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:54:26.257075","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:54:56.352290","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:55:26.790283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:55:57.003787","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:56:27.374902","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:56:57.991607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:57:28.480546","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:57:59.008660","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:58:29.396420","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:58:59.942005","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T12:59:30.248562","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:00:00.685376","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:00:31.013671","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:01:01.419751","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:01:31.717727","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:02:02.190929","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:02:32.751327","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:03:03.098819","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:03:33.539933","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:04:03.988649","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:04:34.466875","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:05:05.080986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:05:35.604921","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:06:06.014607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:06:36.329790","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:07:06.975616","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:07:37.598167","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:08:08.344133","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:08:38.617379","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:09:09.073690","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:09:39.492514","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:10:09.872159","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:10:40.406674","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:11:10.848626","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:11:41.180142","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:12:11.493941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:12:41.797934","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:13:12.087458","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:13:42.555719","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:14:12.820220","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:14:43.061728","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:15:13.454416","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:15:43.787793","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:16:14.300517","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:16:44.708149","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:17:15.227467","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:17:45.654704","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:18:16.059886","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:18:46.480549","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:19:16.893980","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:19:47.418291","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:20:17.832604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:20:48.273433","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:21:18.970738","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:21:49.329440","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:22:19.705993","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:22:50.034409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:23:20.801437","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:23:51.458823","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:24:22.136414","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:24:52.438939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:25:28.862021","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:25:59.999341","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:26:31.792626","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:27:02.950932","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:27:33.121882","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:28:03.564076","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:28:33.923629","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:29:04.379743","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:29:34.728604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:30:05.359340","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:30:35.749876","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:31:06.378141","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:31:36.878883","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:32:07.217625","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:32:38.158758","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:33:08.443871","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:51:25.958430","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:51:57.363026","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:52:28.832775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:53:00.205295","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:53:31.269235","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:54:01.848811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:54:32.271215","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:55:04.236117","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:55:35.792107","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:56:07.300313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:56:38.617203","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:57:08.955941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:57:40.423040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:58:10.764806","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:58:42.265629","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:59:13.430866","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T13:59:43.831453","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:00:15.263058","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:00:46.004771","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:01:16.650703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:01:47.011607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:02:17.310912","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:02:47.843303","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:03:18.192683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:03:50.015685","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:04:20.560452","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:04:51.532387","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:05:21.982241","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:05:52.365346","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:06:23.160306","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:06:53.714078","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:07:24.118033","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:07:54.473992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:08:24.909924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:08:55.022676","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:09:25.570950","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:09:55.941924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:10:26.656334","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:10:57.078687","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:11:27.370121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:11:57.890253","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:12:28.276837","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:12:58.849972","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:13:29.003253","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:13:59.426094","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:14:29.860426","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:15:00.338097","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:15:30.914589","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:16:01.479437","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:16:31.936675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:17:02.298837","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:17:33.096383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:18:04.046362","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:18:34.357686","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:19:05.085591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:19:35.428830","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:20:06.113656","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:20:37.075324","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:21:07.502702","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:21:38.046025","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:22:08.653474","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:22:38.977072","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:23:09.219274","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:23:40.382759","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:24:11.558219","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:24:41.999625","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:25:12.458507","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:25:43.802351","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:26:15.046051","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:26:45.391523","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:27:16.791826","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:27:47.684045","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:28:18.121265","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:28:48.226053","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:29:18.986321","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:29:49.517793","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:30:20.016147","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:30:50.495167","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:31:20.978611","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:31:51.486072","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:32:21.848687","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:32:52.331734","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:33:22.875764","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:33:53.334241","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:34:23.922830","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:34:54.345667","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:35:24.976056","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:35:55.734425","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:36:26.240056","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:36:57.191443","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:37:27.917357","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:37:58.443179","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:38:29.295469","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:39:00.071307","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:39:31.109575","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:40:02.121728","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:40:32.819806","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:41:03.932765","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:41:34.424345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:42:05.358891","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:42:35.939929","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:43:06.468184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:43:37.548716","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:44:08.084551","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:44:38.884651","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:45:09.339962","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:45:39.844879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:46:10.649216","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:46:41.336099","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:47:11.867201","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:47:42.280948","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:48:12.546788","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:48:42.900847","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:49:13.326252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:49:43.704048","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:50:14.010122","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:50:44.256817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:51:14.647454","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:51:45.282297","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:52:16.087389","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:52:46.358972","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:53:17.219591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T14:53:47.877802","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:09:02.970572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:09:34.176439","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:10:05.576250","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:10:36.886617","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:11:07.110995","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:11:38.451881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:12:09.060239","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:12:40.289742","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:13:10.685002","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:13:41.008332","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:14:11.380245","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:14:41.641879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:15:11.993749","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:15:42.460341","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:16:12.923125","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:16:43.178049","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:17:13.660028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:17:44.159886","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:18:14.682089","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:18:44.966647","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:19:15.761148","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:19:46.125879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:20:16.712823","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:20:46.952264","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:21:17.515364","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:21:47.880666","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:22:18.514086","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:22:48.760121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:23:19.229176","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:23:49.711560","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:24:20.265260","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:24:50.458882","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:25:21.017877","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:25:51.580564","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:26:22.037395","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:26:52.464513","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:27:22.829712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:27:53.505711","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:28:25.087524","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:28:55.483429","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:29:26.115415","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:29:56.634201","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:30:26.859090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:30:57.195682","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:31:27.791712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:31:59.457817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:32:30.985677","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:33:01.331037","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:33:32.795951","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:34:03.354929","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:34:33.812483","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:35:04.150607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:35:34.644005","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:36:04.975290","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:36:36.264125","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:37:07.503477","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:37:38.015819","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:38:08.446200","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:38:39.039636","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:39:09.461430","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:39:40.275158","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:40:10.949020","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:40:41.521720","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:41:11.848632","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:41:42.471017","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:42:12.930296","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:42:43.562167","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:43:13.913044","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:43:44.802459","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:44:15.269391","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:44:45.861779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:45:16.228883","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:45:47.036427","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:46:17.542042","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:46:49.167246","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:47:19.528121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:47:50.105026","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:48:20.183309","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:48:51.643622","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:49:21.944212","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:49:52.588594","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:50:23.121804","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:50:54.526450","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:51:24.873531","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:51:55.387557","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:52:26.024187","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:52:56.576341","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:53:26.838959","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:53:57.536003","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:54:28.102301","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T15:54:59.909078","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:10:17.585568","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:10:48.047385","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:11:18.629896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:11:49.917961","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:12:21.356868","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:12:52.568526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:13:22.980287","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:13:53.752745","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:14:24.278267","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:14:55.961591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:15:26.568351","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:15:57.028703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:16:27.917756","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:16:58.605257","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:17:30.166202","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:18:00.462407","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:18:32.039905","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:19:02.749704","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:19:33.623638","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:20:03.747099","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:20:35.251500","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:21:05.648055","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:21:36.871421","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:22:07.385439","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:22:37.743837","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:23:09.196593","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:23:40.363789","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:24:10.639586","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:24:42.359683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:25:12.608898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:25:43.266072","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:26:13.780141","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:26:44.374876","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:27:14.755074","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:27:45.315790","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:28:15.796516","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:28:46.820748","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:29:17.149887","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:29:47.620047","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:30:18.272349","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:30:48.732347","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:31:19.095473","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:31:49.781034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:32:20.295631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:32:51.083835","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:33:21.184239","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:33:51.474852","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:34:21.957297","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:34:52.246269","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:35:22.754940","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:35:53.505131","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:36:23.719093","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:36:54.223898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:37:24.697713","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:37:56.610090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:38:30.774357","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:39:01.254366","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:39:31.589496","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:40:02.455710","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:40:32.715566","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:41:03.318311","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:41:36.020733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:42:08.432950","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:42:38.765914","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:43:11.254793","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:43:42.523088","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:44:12.880062","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:44:43.351497","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:45:13.676246","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:45:44.528328","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:46:15.209021","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:46:45.556225","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:47:15.984031","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:47:46.160970","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:48:16.541048","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-06T16:48:46.978134","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/config/default.py","logger":"airflow.models.dagbag.DagBag"}
