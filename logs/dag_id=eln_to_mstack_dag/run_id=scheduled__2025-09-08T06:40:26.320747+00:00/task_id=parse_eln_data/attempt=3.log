{"timestamp":"2025-09-08T06:51:03.545951","level":"info","event":"DAG bundles loaded: dags-folder","logger":"airflow.dag_processing.bundles.manager.DagBundlesManager"}
{"timestamp":"2025-09-08T06:51:03.546393","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln_to_mstack_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:51:03.551459","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:51:03.555802","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:51:03.558810","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:51:03.602116","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:51:03.743043","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:51:03.747073","level":"info","event":"Creating or retrieving cached MongoClient for URI ending in ...IdleTimeMS=120000%22","logger":"root"}
{"timestamp":"2025-09-08T06:51:03.749376","level":"warning","event":"maxidletimems must be an integer or float","category":"UserWarning","filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/uri_parser_shared.py","lineno":379,"logger":"py.warnings"}
{"timestamp":"2025-09-08T06:51:03.749470","level":"info","event":"You appear to be connected to a CosmosDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/cosmosdb","logger":"pymongo.client"}
{"timestamp":"2025-09-08T06:51:03.881371","level":"info","event":"You appear to be connected to a CosmosDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/cosmosdb","logger":"pymongo.client"}
{"timestamp":"2025-09-08T06:51:06.659586","level":"info","event":"MongoClient connected and verified successfully.","logger":"root"}
{"timestamp":"2025-09-08T06:51:06.659774","level":"info","event":"MongoOperations initialized for database 'eln' using shared client for ...IdleTimeMS=120000%22","logger":"root"}
{"timestamp":"2025-09-08T06:51:06.659845","level":"info","event":"Getting all protocols from ELN","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:08.726292","level":"info","event":"Total protocols: 315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:08.726479","level":"info","event":"Processing protocol: 1000346","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:10.326422","level":"info","event":"Inserted data for protocol: 1000346","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:10.326649","level":"info","event":"Processed protocol: 1000346","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:10.326750","level":"info","event":"Processing protocol: 1000345","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:11.698303","level":"info","event":"Inserted data for protocol: 1000345","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:11.698435","level":"info","event":"Processed protocol: 1000345","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:11.698463","level":"info","event":"Processing protocol: 1000344","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:13.054816","level":"info","event":"Inserted data for protocol: 1000344","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:13.055241","level":"info","event":"Processed protocol: 1000344","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:13.055423","level":"info","event":"Processing protocol: 1000343","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:14.396757","level":"info","event":"Inserted data for protocol: 1000343","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:14.397043","level":"info","event":"Processed protocol: 1000343","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:14.397156","level":"info","event":"Processing protocol: 1000342","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:15.782401","level":"info","event":"Inserted data for protocol: 1000342","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:15.782580","level":"info","event":"Processed protocol: 1000342","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:15.782691","level":"info","event":"Processing protocol: 1000341","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:17.121286","level":"info","event":"Inserted data for protocol: 1000341","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:17.121342","level":"info","event":"Processed protocol: 1000341","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:17.121369","level":"info","event":"Processing protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:18.516743","level":"info","event":"Inserted data for protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:18.516814","level":"info","event":"Processed protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:18.516867","level":"info","event":"Processing protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:20.133857","level":"info","event":"Inserted data for protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:20.134413","level":"info","event":"Processed protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:20.134545","level":"info","event":"Processing protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:21.756576","level":"info","event":"Inserted data for protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:21.757425","level":"info","event":"Processed protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:21.757756","level":"info","event":"Processing protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:23.435603","level":"info","event":"Inserted data for protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:23.435757","level":"info","event":"Processed protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:23.435839","level":"info","event":"Processing protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:25.182574","level":"info","event":"Inserted data for protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:25.182817","level":"info","event":"Processed protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:25.182921","level":"info","event":"Processing protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:26.568706","level":"info","event":"Inserted data for protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:26.568913","level":"info","event":"Processed protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:26.569098","level":"info","event":"Processing protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:28.128875","level":"info","event":"Inserted data for protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:28.129010","level":"info","event":"Processed protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:28.129063","level":"info","event":"Processing protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:29.537219","level":"info","event":"Inserted data for protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:29.537592","level":"info","event":"Processed protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:29.537915","level":"info","event":"Processing protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:31.331114","level":"info","event":"Inserted data for protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:31.331407","level":"info","event":"Processed protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:31.331573","level":"info","event":"Processing protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:32.692498","level":"info","event":"Inserted data for protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:32.692633","level":"info","event":"Processed protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:32.692690","level":"info","event":"Processing protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:34.312582","level":"info","event":"Inserted data for protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:34.312813","level":"info","event":"Processed protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:34.313035","level":"info","event":"Processing protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:35.675644","level":"info","event":"Inserted data for protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:35.675918","level":"info","event":"Processed protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:35.676174","level":"info","event":"Processing protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:37.384860","level":"info","event":"Inserted data for protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:37.385263","level":"info","event":"Processed protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:37.385505","level":"info","event":"Processing protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:38.772446","level":"info","event":"Inserted data for protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:38.772512","level":"info","event":"Processed protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:38.772543","level":"info","event":"Processing protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:40.692109","level":"info","event":"Inserted data for protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:40.692392","level":"info","event":"Processed protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:40.692557","level":"info","event":"Processing protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:42.359688","level":"info","event":"Inserted data for protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:42.360032","level":"info","event":"Processed protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:42.360284","level":"info","event":"Processing protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:43.964315","level":"info","event":"Inserted data for protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:43.964503","level":"info","event":"Processed protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:43.964666","level":"info","event":"Processing protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:45.601169","level":"info","event":"Inserted data for protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:45.601233","level":"info","event":"Processed protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:45.601266","level":"info","event":"Processing protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:47.281730","level":"info","event":"Inserted data for protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:47.281896","level":"info","event":"Processed protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:47.282043","level":"info","event":"Processing protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:48.883258","level":"info","event":"Inserted data for protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:48.883493","level":"info","event":"Processed protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:48.883637","level":"info","event":"Processing protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:50.565130","level":"info","event":"Inserted data for protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:50.565483","level":"info","event":"Processed protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:50.566116","level":"info","event":"Processing protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:52.246937","level":"info","event":"Inserted data for protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:52.247198","level":"info","event":"Processed protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:52.247317","level":"info","event":"Processing protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:53.610579","level":"info","event":"Inserted data for protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:53.610739","level":"info","event":"Processed protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:53.610831","level":"info","event":"Processing protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:55.189217","level":"info","event":"Inserted data for protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:55.190017","level":"info","event":"Processed protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:55.190339","level":"info","event":"Processing protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:56.815350","level":"info","event":"Inserted data for protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:56.815530","level":"info","event":"Processed protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:56.815655","level":"info","event":"Processing protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:58.477393","level":"info","event":"Inserted data for protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:58.477699","level":"info","event":"Processed protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:58.478013","level":"info","event":"Processing protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:59.859043","level":"info","event":"Inserted data for protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:59.859343","level":"info","event":"Processed protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:59.859584","level":"info","event":"Processing protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:01.252682","level":"info","event":"Inserted data for protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:01.252898","level":"info","event":"Processed protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:01.253076","level":"info","event":"Processing protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:02.730034","level":"info","event":"Inserted data for protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:02.730164","level":"info","event":"Processed protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:02.730238","level":"info","event":"Processing protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:04.151899","level":"info","event":"Inserted data for protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:04.152425","level":"info","event":"Processed protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:04.152775","level":"info","event":"Processing protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:05.810110","level":"info","event":"Inserted data for protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:05.810188","level":"info","event":"Processed protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:05.810226","level":"info","event":"Processing protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:07.217289","level":"info","event":"Inserted data for protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:07.217545","level":"info","event":"Processed protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:07.217738","level":"info","event":"Processing protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:08.651273","level":"info","event":"Inserted data for protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:08.651620","level":"info","event":"Processed protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:08.651866","level":"info","event":"Processing protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:10.232587","level":"info","event":"Inserted data for protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:10.232919","level":"info","event":"Processed protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:10.233008","level":"info","event":"Processing protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:12.615070","level":"info","event":"Inserted data for protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:12.615708","level":"info","event":"Processed protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:12.616125","level":"info","event":"Processing protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:14.418844","level":"info","event":"Inserted data for protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:14.419096","level":"info","event":"Processed protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:14.419223","level":"info","event":"Processing protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:16.211755","level":"info","event":"Inserted data for protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:16.211827","level":"info","event":"Processed protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:16.211870","level":"info","event":"Processing protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:17.608594","level":"info","event":"Inserted data for protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:17.609201","level":"info","event":"Processed protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:17.609555","level":"info","event":"Processing protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:18.974537","level":"info","event":"Inserted data for protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:18.974845","level":"info","event":"Processed protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:18.975130","level":"info","event":"Processing protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:20.644887","level":"info","event":"Inserted data for protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:20.645374","level":"info","event":"Processed protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:20.645922","level":"info","event":"Processing protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:22.030713","level":"info","event":"Inserted data for protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:22.031104","level":"info","event":"Processed protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:22.031331","level":"info","event":"Processing protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:23.841181","level":"info","event":"Inserted data for protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:23.841665","level":"info","event":"Processed protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:23.841974","level":"info","event":"Processing protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:25.298554","level":"info","event":"Inserted data for protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:25.298784","level":"info","event":"Processed protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:25.298877","level":"info","event":"Processing protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:26.687046","level":"info","event":"Inserted data for protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:26.687677","level":"info","event":"Processed protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:26.687820","level":"info","event":"Processing protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:28.093926","level":"info","event":"Inserted data for protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:28.094285","level":"info","event":"Processed protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:28.094485","level":"info","event":"Processing protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:29.439893","level":"info","event":"Inserted data for protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:29.440301","level":"info","event":"Processed protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:29.440563","level":"info","event":"Processing protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:31.300481","level":"info","event":"Inserted data for protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:31.300651","level":"info","event":"Processed protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:31.300792","level":"info","event":"Processing protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:32.890430","level":"info","event":"Inserted data for protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:32.890805","level":"info","event":"Processed protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:32.891066","level":"info","event":"Processing protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:34.286911","level":"info","event":"Inserted data for protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:34.287101","level":"info","event":"Processed protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:34.287197","level":"info","event":"Processing protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:35.657952","level":"info","event":"Inserted data for protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:35.658348","level":"info","event":"Processed protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:35.658588","level":"info","event":"Processing protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:37.029704","level":"info","event":"Inserted data for protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:37.029779","level":"info","event":"Processed protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:37.029830","level":"info","event":"Processing protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:38.667263","level":"info","event":"Inserted data for protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:38.667612","level":"info","event":"Processed protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:38.667820","level":"info","event":"Processing protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:40.065144","level":"info","event":"Inserted data for protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:40.065625","level":"info","event":"Processed protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:40.066059","level":"info","event":"Processing protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:41.896820","level":"info","event":"Inserted data for protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:41.896893","level":"info","event":"Processed protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:41.896936","level":"info","event":"Processing protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:43.251965","level":"info","event":"Inserted data for protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:43.252166","level":"info","event":"Processed protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:43.252225","level":"info","event":"Processing protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:44.603405","level":"info","event":"Inserted data for protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:44.603650","level":"info","event":"Processed protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:44.603791","level":"info","event":"Processing protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:46.220893","level":"info","event":"Inserted data for protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:46.221207","level":"info","event":"Processed protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:46.221390","level":"info","event":"Processing protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:47.590169","level":"info","event":"Inserted data for protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:47.590550","level":"info","event":"Processed protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:47.590862","level":"info","event":"Processing protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:49.078502","level":"info","event":"Inserted data for protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:49.083449","level":"info","event":"Processed protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:49.084026","level":"info","event":"Processing protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:50.554649","level":"info","event":"Inserted data for protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:50.554919","level":"info","event":"Processed protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:50.555132","level":"info","event":"Processing protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:52.284581","level":"info","event":"Inserted data for protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:52.285213","level":"info","event":"Processed protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:52.285497","level":"info","event":"Processing protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:53.709073","level":"info","event":"Inserted data for protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:53.714895","level":"info","event":"Processed protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:53.716040","level":"info","event":"Processing protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:55.162523","level":"info","event":"Inserted data for protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:55.162792","level":"info","event":"Processed protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:55.163543","level":"info","event":"Processing protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:56.771079","level":"info","event":"Inserted data for protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:56.771481","level":"info","event":"Processed protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:56.771818","level":"info","event":"Processing protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:58.282294","level":"info","event":"Inserted data for protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:58.282659","level":"info","event":"Processed protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:58.282888","level":"info","event":"Processing protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:59.647628","level":"info","event":"Inserted data for protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:59.648341","level":"info","event":"Processed protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:52:59.648673","level":"info","event":"Processing protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:01.026187","level":"info","event":"Inserted data for protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:01.026259","level":"info","event":"Processed protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:01.026287","level":"info","event":"Processing protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:02.843595","level":"info","event":"Inserted data for protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:02.843812","level":"info","event":"Processed protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:02.843935","level":"info","event":"Processing protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:04.587220","level":"info","event":"Inserted data for protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:04.587930","level":"info","event":"Processed protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:04.588206","level":"info","event":"Processing protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:06.253764","level":"info","event":"Inserted data for protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:06.254269","level":"info","event":"Processed protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:06.254509","level":"info","event":"Processing protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:07.630247","level":"info","event":"Inserted data for protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:07.630494","level":"info","event":"Processed protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:07.630607","level":"info","event":"Processing protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:08.988943","level":"info","event":"Inserted data for protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:08.989119","level":"info","event":"Processed protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:08.989199","level":"info","event":"Processing protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:10.605303","level":"info","event":"Inserted data for protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:10.605486","level":"info","event":"Processed protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:10.605629","level":"info","event":"Processing protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:12.213556","level":"info","event":"Inserted data for protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:12.213958","level":"info","event":"Processed protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:12.214170","level":"info","event":"Processing protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:13.591827","level":"info","event":"Inserted data for protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:13.592057","level":"info","event":"Processed protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:13.592194","level":"info","event":"Processing protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:15.201185","level":"info","event":"Inserted data for protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:15.201307","level":"info","event":"Processed protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:15.201368","level":"info","event":"Processing protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:16.577171","level":"info","event":"Inserted data for protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:16.577716","level":"info","event":"Processed protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:16.578053","level":"info","event":"Processing protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:17.956401","level":"info","event":"Inserted data for protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:17.956628","level":"info","event":"Processed protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:17.956775","level":"info","event":"Processing protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:19.993785","level":"info","event":"Inserted data for protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:19.993847","level":"info","event":"Processed protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:19.993890","level":"info","event":"Processing protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:21.636129","level":"info","event":"Inserted data for protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:21.636381","level":"info","event":"Processed protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:21.636471","level":"info","event":"Processing protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:23.032427","level":"info","event":"Inserted data for protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:23.032665","level":"info","event":"Processed protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:23.032767","level":"info","event":"Processing protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:24.466841","level":"info","event":"Inserted data for protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:24.467121","level":"info","event":"Processed protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:24.467247","level":"info","event":"Processing protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:26.086742","level":"info","event":"Inserted data for protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:26.087263","level":"info","event":"Processed protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:26.087773","level":"info","event":"Processing protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:27.445825","level":"info","event":"Inserted data for protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:27.446233","level":"info","event":"Processed protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:27.446817","level":"info","event":"Processing protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:29.073556","level":"info","event":"Inserted data for protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:29.073645","level":"info","event":"Processed protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:29.073729","level":"info","event":"Processing protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:30.467263","level":"info","event":"Inserted data for protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:30.467857","level":"info","event":"Processed protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:30.468220","level":"info","event":"Processing protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:31.852202","level":"info","event":"Inserted data for protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:31.852589","level":"info","event":"Processed protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:31.852871","level":"info","event":"Processing protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:33.512232","level":"info","event":"Inserted data for protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:33.512509","level":"info","event":"Processed protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:33.512882","level":"info","event":"Processing protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:34.880364","level":"info","event":"Inserted data for protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:34.880562","level":"info","event":"Processed protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:34.880669","level":"info","event":"Processing protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:36.530710","level":"info","event":"Inserted data for protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:36.531378","level":"info","event":"Processed protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:36.531760","level":"info","event":"Processing protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:37.920149","level":"info","event":"Inserted data for protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:37.920237","level":"info","event":"Processed protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:37.920285","level":"info","event":"Processing protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:39.248497","level":"info","event":"Inserted data for protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:39.248743","level":"info","event":"Processed protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:39.248939","level":"info","event":"Processing protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:40.896455","level":"info","event":"Inserted data for protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:40.897031","level":"info","event":"Processed protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:40.897159","level":"info","event":"Processing protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:42.260090","level":"info","event":"Inserted data for protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:42.260251","level":"info","event":"Processed protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:42.260362","level":"info","event":"Processing protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:43.625310","level":"info","event":"Inserted data for protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:43.625652","level":"info","event":"Processed protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:43.625857","level":"info","event":"Processing protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:44.974085","level":"info","event":"Inserted data for protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:44.974389","level":"info","event":"Processed protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:44.974572","level":"info","event":"Processing protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:46.631503","level":"info","event":"Inserted data for protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:46.631580","level":"info","event":"Processed protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:46.631654","level":"info","event":"Processing protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:48.009885","level":"info","event":"Inserted data for protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:48.010180","level":"info","event":"Processed protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:48.010305","level":"info","event":"Processing protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:49.385734","level":"info","event":"Inserted data for protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:49.386288","level":"info","event":"Processed protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:49.386521","level":"info","event":"Processing protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:50.766250","level":"info","event":"Inserted data for protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:50.766811","level":"info","event":"Processed protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:50.766974","level":"info","event":"Processing protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:52.756512","level":"info","event":"Inserted data for protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:52.756911","level":"info","event":"Processed protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:52.757000","level":"info","event":"Processing protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:54.628745","level":"info","event":"Inserted data for protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:54.629165","level":"info","event":"Processed protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:54.629442","level":"info","event":"Processing protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:56.241783","level":"info","event":"Inserted data for protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:56.242049","level":"info","event":"Processed protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:56.242168","level":"info","event":"Processing protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:57.596904","level":"info","event":"Inserted data for protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:57.597081","level":"info","event":"Processed protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:57.597145","level":"info","event":"Processing protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:59.001249","level":"info","event":"Inserted data for protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:59.002390","level":"info","event":"Processed protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:53:59.002664","level":"info","event":"Processing protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:00.412947","level":"info","event":"Inserted data for protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:00.413376","level":"info","event":"Processed protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:00.413602","level":"info","event":"Processing protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:01.794200","level":"info","event":"Inserted data for protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:01.794263","level":"info","event":"Processed protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:01.794306","level":"info","event":"Processing protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:03.158066","level":"info","event":"Inserted data for protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:03.158564","level":"info","event":"Processed protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:03.158848","level":"info","event":"Processing protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:04.769489","level":"info","event":"Inserted data for protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:04.769952","level":"info","event":"Processed protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:04.770278","level":"info","event":"Processing protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:06.136624","level":"info","event":"Inserted data for protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:06.136966","level":"info","event":"Processed protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:06.137221","level":"info","event":"Processing protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:07.759116","level":"info","event":"Inserted data for protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:07.759435","level":"info","event":"Processed protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:07.759569","level":"info","event":"Processing protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:09.109748","level":"info","event":"Inserted data for protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:09.109836","level":"info","event":"Processed protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:09.109899","level":"info","event":"Processing protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:10.539770","level":"info","event":"Inserted data for protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:10.540292","level":"info","event":"Processed protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:10.541261","level":"info","event":"Processing protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:12.145708","level":"info","event":"Inserted data for protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:12.146012","level":"info","event":"Processed protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:12.146193","level":"info","event":"Processing protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:13.741395","level":"info","event":"Inserted data for protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:13.741682","level":"info","event":"Processed protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:13.741918","level":"info","event":"Processing protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:15.091351","level":"info","event":"Inserted data for protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:15.091560","level":"info","event":"Processed protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:15.091700","level":"info","event":"Processing protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:16.457795","level":"info","event":"Inserted data for protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:16.458229","level":"info","event":"Processed protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:16.458466","level":"info","event":"Processing protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:18.248328","level":"info","event":"Inserted data for protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:18.248457","level":"info","event":"Processed protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:18.248521","level":"info","event":"Processing protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:19.630878","level":"info","event":"Inserted data for protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:19.631033","level":"info","event":"Processed protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:19.631087","level":"info","event":"Processing protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:21.039075","level":"info","event":"Inserted data for protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:21.039817","level":"info","event":"Processed protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:21.039942","level":"info","event":"Processing protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:22.909225","level":"info","event":"Inserted data for protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:22.909682","level":"info","event":"Processed protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:22.910064","level":"info","event":"Processing protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:24.269848","level":"info","event":"Inserted data for protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:24.270214","level":"info","event":"Processed protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:24.270455","level":"info","event":"Processing protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:26.146641","level":"info","event":"Inserted data for protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:26.146713","level":"info","event":"Processed protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:26.146777","level":"info","event":"Processing protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:27.562543","level":"info","event":"Inserted data for protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:27.562947","level":"info","event":"Processed protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:27.563265","level":"info","event":"Processing protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:28.908150","level":"info","event":"Inserted data for protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:28.908594","level":"info","event":"Processed protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:28.908865","level":"info","event":"Processing protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:30.290838","level":"info","event":"Inserted data for protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:30.291305","level":"info","event":"Processed protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:30.291532","level":"info","event":"Processing protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:31.972948","level":"info","event":"Inserted data for protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:31.973379","level":"info","event":"Processed protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:31.973627","level":"info","event":"Processing protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:33.576093","level":"info","event":"Inserted data for protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:33.576760","level":"info","event":"Processed protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:33.577154","level":"info","event":"Processing protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:35.219770","level":"info","event":"Inserted data for protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:35.219854","level":"info","event":"Processed protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:35.219903","level":"info","event":"Processing protocol: 1000179","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:36.568785","level":"info","event":"Inserted data for protocol: 1000179","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:36.568946","level":"info","event":"Processed protocol: 1000179","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:36.569137","level":"info","event":"Processing protocol: 1000178","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:37.916402","level":"info","event":"Inserted data for protocol: 1000178","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:37.916850","level":"info","event":"Processed protocol: 1000178","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:37.917189","level":"info","event":"Processing protocol: 1000177","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:39.562828","level":"info","event":"Inserted data for protocol: 1000177","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:39.562992","level":"info","event":"Processed protocol: 1000177","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:39.563058","level":"info","event":"Processing protocol: 1000176","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:41.042762","level":"info","event":"Inserted data for protocol: 1000176","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:41.043234","level":"info","event":"Processed protocol: 1000176","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:41.043535","level":"info","event":"Processing protocol: 1000175","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:42.380845","level":"info","event":"Inserted data for protocol: 1000175","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:42.381111","level":"info","event":"Processed protocol: 1000175","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:42.381315","level":"info","event":"Processing protocol: 1000174","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:44.244264","level":"info","event":"Inserted data for protocol: 1000174","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:44.245008","level":"info","event":"Processed protocol: 1000174","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:44.245141","level":"info","event":"Processing protocol: 1000173","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:46.130038","level":"info","event":"Inserted data for protocol: 1000173","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:46.130305","level":"info","event":"Processed protocol: 1000173","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:46.130418","level":"info","event":"Processing protocol: 1000172","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:47.723229","level":"info","event":"Inserted data for protocol: 1000172","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:47.723386","level":"info","event":"Processed protocol: 1000172","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:47.723445","level":"info","event":"Processing protocol: 1000171","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:49.311235","level":"info","event":"Inserted data for protocol: 1000171","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:49.311430","level":"info","event":"Processed protocol: 1000171","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:49.311516","level":"info","event":"Processing protocol: 1000170","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:50.979651","level":"info","event":"Inserted data for protocol: 1000170","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:50.979765","level":"info","event":"Processed protocol: 1000170","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:50.979820","level":"info","event":"Processing protocol: 1000169","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:52.307798","level":"info","event":"Inserted data for protocol: 1000169","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:52.307920","level":"info","event":"Processed protocol: 1000169","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:52.307965","level":"info","event":"Processing protocol: 1000168","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:53.647913","level":"info","event":"Inserted data for protocol: 1000168","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:53.648356","level":"info","event":"Processed protocol: 1000168","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:53.648625","level":"info","event":"Processing protocol: 1000167","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:55.196091","level":"info","event":"Inserted data for protocol: 1000167","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:55.198238","level":"info","event":"Processed protocol: 1000167","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:55.198627","level":"info","event":"Processing protocol: 1000166","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:56.865846","level":"info","event":"Inserted data for protocol: 1000166","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:56.866435","level":"info","event":"Processed protocol: 1000166","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:56.866549","level":"info","event":"Processing protocol: 1000165","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:58.303356","level":"info","event":"Inserted data for protocol: 1000165","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:58.303676","level":"info","event":"Processed protocol: 1000165","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:58.303824","level":"info","event":"Processing protocol: 1000164","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:59.624403","level":"info","event":"Inserted data for protocol: 1000164","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:59.624524","level":"info","event":"Processed protocol: 1000164","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:54:59.624578","level":"info","event":"Processing protocol: 1000163","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:01.016225","level":"info","event":"Inserted data for protocol: 1000163","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:01.016479","level":"info","event":"Processed protocol: 1000163","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:01.016649","level":"info","event":"Processing protocol: 1000162","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:02.367429","level":"info","event":"Inserted data for protocol: 1000162","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:02.367660","level":"info","event":"Processed protocol: 1000162","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:02.368563","level":"info","event":"Processing protocol: 1000161","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:03.748894","level":"info","event":"Inserted data for protocol: 1000161","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:03.749064","level":"info","event":"Processed protocol: 1000161","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:03.749210","level":"info","event":"Processing protocol: 1000160","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:05.121887","level":"info","event":"Inserted data for protocol: 1000160","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:05.122414","level":"info","event":"Processed protocol: 1000160","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:05.122683","level":"info","event":"Processing protocol: 1000159","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:06.551909","level":"info","event":"Inserted data for protocol: 1000159","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:06.552372","level":"info","event":"Processed protocol: 1000159","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:06.552636","level":"info","event":"Processing protocol: 1000158","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:07.952630","level":"info","event":"Inserted data for protocol: 1000158","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:07.952746","level":"info","event":"Processed protocol: 1000158","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:07.952826","level":"info","event":"Processing protocol: 1000157","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:09.748135","level":"info","event":"Inserted data for protocol: 1000157","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:09.748373","level":"info","event":"Processed protocol: 1000157","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:09.748500","level":"info","event":"Processing protocol: 1000156","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:11.558955","level":"info","event":"Inserted data for protocol: 1000156","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:11.559559","level":"info","event":"Processed protocol: 1000156","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:11.559881","level":"info","event":"Processing protocol: 1000155","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:13.032038","level":"info","event":"Inserted data for protocol: 1000155","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:13.032950","level":"info","event":"Processed protocol: 1000155","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:13.033564","level":"info","event":"Processing protocol: 1000154","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:14.415056","level":"info","event":"Inserted data for protocol: 1000154","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:14.415470","level":"info","event":"Processed protocol: 1000154","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:14.415539","level":"info","event":"Processing protocol: 1000153","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:16.039613","level":"info","event":"Inserted data for protocol: 1000153","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:16.039909","level":"info","event":"Processed protocol: 1000153","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:16.040157","level":"info","event":"Processing protocol: 1000152","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:17.665267","level":"info","event":"Inserted data for protocol: 1000152","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:17.665598","level":"info","event":"Processed protocol: 1000152","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:17.665876","level":"info","event":"Processing protocol: 1000151","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:19.038363","level":"info","event":"Inserted data for protocol: 1000151","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:19.038522","level":"info","event":"Processed protocol: 1000151","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:19.038608","level":"info","event":"Processing protocol: 1000150","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:20.487163","level":"info","event":"Inserted data for protocol: 1000150","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:20.487467","level":"info","event":"Processed protocol: 1000150","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:20.487524","level":"info","event":"Processing protocol: 1000149","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:22.168969","level":"info","event":"Inserted data for protocol: 1000149","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:22.169451","level":"info","event":"Processed protocol: 1000149","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:22.169614","level":"info","event":"Processing protocol: 1000148","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:23.549803","level":"info","event":"Inserted data for protocol: 1000148","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:23.551171","level":"info","event":"Processed protocol: 1000148","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:23.551316","level":"info","event":"Processing protocol: 1000147","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:25.036372","level":"info","event":"Inserted data for protocol: 1000147","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:25.036474","level":"info","event":"Processed protocol: 1000147","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:25.036555","level":"info","event":"Processing protocol: 1000146","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:26.897879","level":"info","event":"Inserted data for protocol: 1000146","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:26.899273","level":"info","event":"Processed protocol: 1000146","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:26.899583","level":"info","event":"Processing protocol: 1000145","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:28.291792","level":"info","event":"Inserted data for protocol: 1000145","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:28.292112","level":"info","event":"Processed protocol: 1000145","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:28.292316","level":"info","event":"Processing protocol: 1000144","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:29.682896","level":"info","event":"Inserted data for protocol: 1000144","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:29.683135","level":"info","event":"Processed protocol: 1000144","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:29.683274","level":"info","event":"Processing protocol: 1000143","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:31.080030","level":"info","event":"Inserted data for protocol: 1000143","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:31.080537","level":"info","event":"Processed protocol: 1000143","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:31.080840","level":"info","event":"Processing protocol: 1000142","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:32.435563","level":"info","event":"Inserted data for protocol: 1000142","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:32.435640","level":"info","event":"Processed protocol: 1000142","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:32.435671","level":"info","event":"Processing protocol: 1000141","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:33.804319","level":"info","event":"Inserted data for protocol: 1000141","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:33.804501","level":"info","event":"Processed protocol: 1000141","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:33.804689","level":"info","event":"Processing protocol: 1000140","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:35.461066","level":"info","event":"Inserted data for protocol: 1000140","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:35.462331","level":"info","event":"Processed protocol: 1000140","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:35.462516","level":"info","event":"Processing protocol: 1000139","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:36.939860","level":"info","event":"Inserted data for protocol: 1000139","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:36.940098","level":"info","event":"Processed protocol: 1000139","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:36.940327","level":"info","event":"Processing protocol: 1000138","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:38.406660","level":"info","event":"Inserted data for protocol: 1000138","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:38.407145","level":"info","event":"Processed protocol: 1000138","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:38.407446","level":"info","event":"Processing protocol: 1000137","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:40.083085","level":"info","event":"Inserted data for protocol: 1000137","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:40.083464","level":"info","event":"Processed protocol: 1000137","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:40.083744","level":"info","event":"Processing protocol: 1000136","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:41.435038","level":"info","event":"Inserted data for protocol: 1000136","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:41.435374","level":"info","event":"Processed protocol: 1000136","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:41.435594","level":"info","event":"Processing protocol: 1000135","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:43.115535","level":"info","event":"Inserted data for protocol: 1000135","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:43.115873","level":"info","event":"Processed protocol: 1000135","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:43.115950","level":"info","event":"Processing protocol: 1000134","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:44.789511","level":"info","event":"Inserted data for protocol: 1000134","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:44.789875","level":"info","event":"Processed protocol: 1000134","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:44.790120","level":"info","event":"Processing protocol: 1000133","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:46.483208","level":"info","event":"Inserted data for protocol: 1000133","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:46.483394","level":"info","event":"Processed protocol: 1000133","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:46.484422","level":"info","event":"Processing protocol: 1000132","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:48.191024","level":"info","event":"Inserted data for protocol: 1000132","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:48.191365","level":"info","event":"Processed protocol: 1000132","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:48.191608","level":"info","event":"Processing protocol: 1000131","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:49.770070","level":"info","event":"Inserted data for protocol: 1000131","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:49.770180","level":"info","event":"Processed protocol: 1000131","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:49.770244","level":"info","event":"Processing protocol: 1000130","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:51.494426","level":"info","event":"Inserted data for protocol: 1000130","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:51.494632","level":"info","event":"Processed protocol: 1000130","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:51.494726","level":"info","event":"Processing protocol: 1000129","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:53.074396","level":"info","event":"Inserted data for protocol: 1000129","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:53.074670","level":"info","event":"Processed protocol: 1000129","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:53.074823","level":"info","event":"Processing protocol: 1000128","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:54.891576","level":"info","event":"Inserted data for protocol: 1000128","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:54.891769","level":"info","event":"Processed protocol: 1000128","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:54.891833","level":"info","event":"Processing protocol: 1000127","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:56.268627","level":"info","event":"Inserted data for protocol: 1000127","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:56.268769","level":"info","event":"Processed protocol: 1000127","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:56.268847","level":"info","event":"Processing protocol: 1000126","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:58.155821","level":"info","event":"Inserted data for protocol: 1000126","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:58.158620","level":"info","event":"Processed protocol: 1000126","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:58.159163","level":"info","event":"Processing protocol: 1000125","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:59.547163","level":"info","event":"Inserted data for protocol: 1000125","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:59.547422","level":"info","event":"Processed protocol: 1000125","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:55:59.547660","level":"info","event":"Processing protocol: 1000124","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:00.901703","level":"info","event":"Inserted data for protocol: 1000124","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:00.902058","level":"info","event":"Processed protocol: 1000124","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:00.902292","level":"info","event":"Processing protocol: 1000123","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:02.611155","level":"info","event":"Inserted data for protocol: 1000123","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:02.611522","level":"info","event":"Processed protocol: 1000123","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:02.611669","level":"info","event":"Processing protocol: 1000122","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:03.989569","level":"info","event":"Inserted data for protocol: 1000122","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:03.989914","level":"info","event":"Processed protocol: 1000122","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:03.990172","level":"info","event":"Processing protocol: 1000121","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:05.352517","level":"info","event":"Inserted data for protocol: 1000121","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:05.353036","level":"info","event":"Processed protocol: 1000121","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:05.353590","level":"info","event":"Processing protocol: 1000120","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:06.709440","level":"info","event":"Inserted data for protocol: 1000120","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:06.709835","level":"info","event":"Processed protocol: 1000120","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:06.710110","level":"info","event":"Processing protocol: 1000119","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:08.108448","level":"info","event":"Inserted data for protocol: 1000119","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:08.108742","level":"info","event":"Processed protocol: 1000119","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:08.108877","level":"info","event":"Processing protocol: 1000118","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:09.488644","level":"info","event":"Inserted data for protocol: 1000118","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:09.488942","level":"info","event":"Processed protocol: 1000118","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:09.489225","level":"info","event":"Processing protocol: 1000117","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:11.027953","level":"info","event":"Inserted data for protocol: 1000117","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:11.028297","level":"info","event":"Processed protocol: 1000117","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:11.028533","level":"info","event":"Processing protocol: 1000116","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:12.425957","level":"info","event":"Inserted data for protocol: 1000116","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:12.426359","level":"info","event":"Processed protocol: 1000116","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:12.426571","level":"info","event":"Processing protocol: 1000115","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:14.106799","level":"info","event":"Inserted data for protocol: 1000115","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:14.107397","level":"info","event":"Processed protocol: 1000115","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:14.107753","level":"info","event":"Processing protocol: 1000114","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:15.806594","level":"info","event":"Inserted data for protocol: 1000114","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:15.807062","level":"info","event":"Processed protocol: 1000114","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:15.807124","level":"info","event":"Processing protocol: 1000113","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:17.194326","level":"info","event":"Inserted data for protocol: 1000113","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:17.194866","level":"info","event":"Processed protocol: 1000113","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:17.195183","level":"info","event":"Processing protocol: 1000112","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:18.568498","level":"info","event":"Inserted data for protocol: 1000112","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:18.568937","level":"info","event":"Processed protocol: 1000112","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:18.569106","level":"info","event":"Processing protocol: 1000111","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:20.037276","level":"info","event":"Inserted data for protocol: 1000111","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:20.037347","level":"info","event":"Processed protocol: 1000111","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:20.037410","level":"info","event":"Processing protocol: 1000110","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:21.664772","level":"info","event":"Inserted data for protocol: 1000110","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:21.665362","level":"info","event":"Processed protocol: 1000110","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:21.665585","level":"info","event":"Processing protocol: 1000109","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:23.064330","level":"info","event":"Inserted data for protocol: 1000109","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:23.064830","level":"info","event":"Processed protocol: 1000109","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:23.065147","level":"info","event":"Processing protocol: 1000108","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:24.463870","level":"info","event":"Inserted data for protocol: 1000108","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:24.464194","level":"info","event":"Processed protocol: 1000108","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:24.464384","level":"info","event":"Processing protocol: 1000107","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:25.870859","level":"info","event":"Inserted data for protocol: 1000107","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:25.871048","level":"info","event":"Processed protocol: 1000107","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:25.871132","level":"info","event":"Processing protocol: 1000106","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:27.541047","level":"info","event":"Inserted data for protocol: 1000106","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:27.541488","level":"info","event":"Processed protocol: 1000106","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:27.541769","level":"info","event":"Processing protocol: 1000105","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:29.458302","level":"info","event":"Inserted data for protocol: 1000105","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:29.458473","level":"info","event":"Processed protocol: 1000105","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:29.458571","level":"info","event":"Processing protocol: 1000104","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:30.841527","level":"info","event":"Inserted data for protocol: 1000104","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:30.841634","level":"info","event":"Processed protocol: 1000104","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:30.841683","level":"info","event":"Processing protocol: 1000103","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:32.227998","level":"info","event":"Inserted data for protocol: 1000103","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:32.228097","level":"info","event":"Processed protocol: 1000103","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:32.228149","level":"info","event":"Processing protocol: 1000102","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:34.126663","level":"info","event":"Inserted data for protocol: 1000102","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:34.127178","level":"info","event":"Processed protocol: 1000102","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:34.127532","level":"info","event":"Processing protocol: 1000101","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:35.785414","level":"info","event":"Inserted data for protocol: 1000101","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:35.785508","level":"info","event":"Processed protocol: 1000101","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:35.785593","level":"info","event":"Processing protocol: 1000100","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:37.421346","level":"info","event":"Inserted data for protocol: 1000100","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:37.421693","level":"info","event":"Processed protocol: 1000100","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:37.421914","level":"info","event":"Processing protocol: 1000099","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:39.021851","level":"info","event":"Inserted data for protocol: 1000099","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:39.022045","level":"info","event":"Processed protocol: 1000099","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:39.022123","level":"info","event":"Processing protocol: 1000098","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:40.685344","level":"info","event":"Inserted data for protocol: 1000098","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:40.685583","level":"info","event":"Processed protocol: 1000098","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:40.685718","level":"info","event":"Processing protocol: 1000097","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:42.116557","level":"info","event":"Inserted data for protocol: 1000097","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:42.117115","level":"info","event":"Processed protocol: 1000097","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:42.117418","level":"info","event":"Processing protocol: 1000096","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:43.581461","level":"info","event":"Inserted data for protocol: 1000096","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:43.581564","level":"info","event":"Processed protocol: 1000096","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:43.581653","level":"info","event":"Processing protocol: 1000095","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:45.256509","level":"info","event":"Inserted data for protocol: 1000095","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:45.256943","level":"info","event":"Processed protocol: 1000095","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:45.257253","level":"info","event":"Processing protocol: 1000094","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:46.917218","level":"info","event":"Inserted data for protocol: 1000094","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:46.917560","level":"info","event":"Processed protocol: 1000094","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:46.918007","level":"info","event":"Processing protocol: 1000093","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:48.277384","level":"info","event":"Inserted data for protocol: 1000093","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:48.277551","level":"info","event":"Processed protocol: 1000093","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:48.277672","level":"info","event":"Processing protocol: 1000092","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:49.666764","level":"info","event":"Inserted data for protocol: 1000092","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:49.667081","level":"info","event":"Processed protocol: 1000092","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:49.667309","level":"info","event":"Processing protocol: 1000091","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:51.259246","level":"info","event":"Inserted data for protocol: 1000091","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:51.259459","level":"info","event":"Processed protocol: 1000091","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:51.259620","level":"info","event":"Processing protocol: 1000090","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:52.872928","level":"info","event":"Inserted data for protocol: 1000090","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:52.873705","level":"info","event":"Processed protocol: 1000090","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:52.874464","level":"info","event":"Processing protocol: 1000089","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:54.228464","level":"info","event":"Inserted data for protocol: 1000089","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:54.228883","level":"info","event":"Processed protocol: 1000089","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:54.229124","level":"info","event":"Processing protocol: 1000088","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:56.352203","level":"info","event":"Inserted data for protocol: 1000088","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:56.354685","level":"info","event":"Processed protocol: 1000088","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:56.355310","level":"info","event":"Processing protocol: 1000087","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:58.246950","level":"info","event":"Inserted data for protocol: 1000087","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:58.247383","level":"info","event":"Processed protocol: 1000087","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:58.247558","level":"info","event":"Processing protocol: 1000086","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:59.642796","level":"info","event":"Inserted data for protocol: 1000086","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:59.643225","level":"info","event":"Processed protocol: 1000086","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:56:59.643497","level":"info","event":"Processing protocol: 1000085","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:01.057550","level":"info","event":"Inserted data for protocol: 1000085","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:01.057932","level":"info","event":"Processed protocol: 1000085","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:01.058006","level":"info","event":"Processing protocol: 1000084","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:02.658861","level":"info","event":"Inserted data for protocol: 1000084","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:02.659634","level":"info","event":"Processed protocol: 1000084","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:02.659774","level":"info","event":"Processing protocol: 1000083","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:04.013128","level":"info","event":"Inserted data for protocol: 1000083","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:04.013479","level":"info","event":"Processed protocol: 1000083","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:04.013739","level":"info","event":"Processing protocol: 1000082","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:05.655300","level":"info","event":"Inserted data for protocol: 1000082","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:05.655590","level":"info","event":"Processed protocol: 1000082","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:05.655794","level":"info","event":"Processing protocol: 1000081","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:07.318257","level":"info","event":"Inserted data for protocol: 1000081","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:07.318459","level":"info","event":"Processed protocol: 1000081","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:07.322219","level":"info","event":"Processing protocol: 1000080","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:08.710590","level":"info","event":"Inserted data for protocol: 1000080","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:08.710707","level":"info","event":"Processed protocol: 1000080","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:08.711975","level":"info","event":"Processing protocol: 1000079","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:10.367784","level":"info","event":"Inserted data for protocol: 1000079","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:10.367959","level":"info","event":"Processed protocol: 1000079","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:10.368077","level":"info","event":"Processing protocol: 1000078","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:11.732541","level":"info","event":"Inserted data for protocol: 1000078","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:11.733084","level":"info","event":"Processed protocol: 1000078","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:11.733363","level":"info","event":"Processing protocol: 1000077","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:13.094614","level":"info","event":"Inserted data for protocol: 1000077","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:13.094813","level":"info","event":"Processed protocol: 1000077","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:13.094966","level":"info","event":"Processing protocol: 1000076","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:14.449307","level":"info","event":"Inserted data for protocol: 1000076","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:14.449933","level":"info","event":"Processed protocol: 1000076","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:14.450380","level":"info","event":"Processing protocol: 1000075","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:15.871815","level":"info","event":"Inserted data for protocol: 1000075","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:15.872144","level":"info","event":"Processed protocol: 1000075","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:15.872325","level":"info","event":"Processing protocol: 1000074","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:17.515902","level":"info","event":"Inserted data for protocol: 1000074","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:17.516221","level":"info","event":"Processed protocol: 1000074","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:17.516456","level":"info","event":"Processing protocol: 1000073","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:19.544528","level":"info","event":"Inserted data for protocol: 1000073","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:19.545045","level":"info","event":"Processed protocol: 1000073","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:19.547049","level":"info","event":"Processing protocol: 1000072","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:20.932256","level":"info","event":"Inserted data for protocol: 1000072","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:20.932555","level":"info","event":"Processed protocol: 1000072","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:20.934044","level":"info","event":"Processing protocol: 1000071","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:22.339059","level":"info","event":"Inserted data for protocol: 1000071","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:22.339346","level":"info","event":"Processed protocol: 1000071","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:22.340626","level":"info","event":"Processing protocol: 1000070","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:23.694202","level":"info","event":"Inserted data for protocol: 1000070","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:23.694696","level":"info","event":"Processed protocol: 1000070","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:23.696614","level":"info","event":"Processing protocol: 1000069","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:25.321317","level":"info","event":"Inserted data for protocol: 1000069","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:25.321708","level":"info","event":"Processed protocol: 1000069","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:25.321965","level":"info","event":"Processing protocol: 1000068","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:26.711895","level":"info","event":"Inserted data for protocol: 1000068","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:26.712330","level":"info","event":"Processed protocol: 1000068","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:26.712732","level":"info","event":"Processing protocol: 1000067","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:28.098830","level":"info","event":"Inserted data for protocol: 1000067","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:28.099178","level":"info","event":"Processed protocol: 1000067","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:28.099423","level":"info","event":"Processing protocol: 1000066","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:29.468815","level":"info","event":"Inserted data for protocol: 1000066","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:29.469050","level":"info","event":"Processed protocol: 1000066","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:29.469193","level":"info","event":"Processing protocol: 1000065","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:31.122645","level":"info","event":"Inserted data for protocol: 1000065","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:31.122816","level":"info","event":"Processed protocol: 1000065","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:31.122900","level":"info","event":"Processing protocol: 1000064","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:32.538328","level":"info","event":"Inserted data for protocol: 1000064","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:32.538670","level":"info","event":"Processed protocol: 1000064","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:32.538900","level":"info","event":"Processing protocol: 1000063","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:34.499100","level":"info","event":"Inserted data for protocol: 1000063","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:34.499864","level":"info","event":"Processed protocol: 1000063","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:34.500423","level":"info","event":"Processing protocol: 1000062","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:37.671970","level":"info","event":"Inserted data for protocol: 1000062","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:37.673370","level":"info","event":"Processed protocol: 1000062","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:37.673712","level":"info","event":"Processing protocol: 1000061","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:39.323592","level":"info","event":"Inserted data for protocol: 1000061","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:39.324042","level":"info","event":"Processed protocol: 1000061","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:39.324701","level":"info","event":"Processing protocol: 1000060","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:40.978125","level":"info","event":"Inserted data for protocol: 1000060","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:40.978771","level":"info","event":"Processed protocol: 1000060","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:40.980043","level":"info","event":"Processing protocol: 1000059","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:42.611562","level":"info","event":"Inserted data for protocol: 1000059","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:42.612747","level":"info","event":"Processed protocol: 1000059","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:42.613766","level":"info","event":"Processing protocol: 1000058","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:44.015556","level":"info","event":"Inserted data for protocol: 1000058","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:44.015670","level":"info","event":"Processed protocol: 1000058","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:44.015735","level":"info","event":"Processing protocol: 1000057","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:45.396965","level":"info","event":"Inserted data for protocol: 1000057","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:45.397225","level":"info","event":"Processed protocol: 1000057","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:45.397290","level":"info","event":"Processing protocol: 1000056","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:46.876661","level":"info","event":"Inserted data for protocol: 1000056","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:46.877107","level":"info","event":"Processed protocol: 1000056","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:46.878572","level":"info","event":"Processing protocol: 1000055","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:48.514527","level":"info","event":"Inserted data for protocol: 1000055","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:48.514691","level":"info","event":"Processed protocol: 1000055","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:48.514787","level":"info","event":"Processing protocol: 1000054","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:50.276096","level":"info","event":"Inserted data for protocol: 1000054","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:50.276199","level":"info","event":"Processed protocol: 1000054","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:50.276266","level":"info","event":"Processing protocol: 1000053","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:51.906298","level":"info","event":"Inserted data for protocol: 1000053","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:51.906942","level":"info","event":"Processed protocol: 1000053","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:51.907386","level":"info","event":"Processing protocol: 1000052","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:53.345770","level":"info","event":"Inserted data for protocol: 1000052","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:53.346114","level":"info","event":"Processed protocol: 1000052","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:53.346261","level":"info","event":"Processing protocol: 1000051","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:54.793483","level":"info","event":"Inserted data for protocol: 1000051","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:54.793603","level":"info","event":"Processed protocol: 1000051","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:54.793660","level":"info","event":"Processing protocol: 1000050","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:56.258357","level":"info","event":"Inserted data for protocol: 1000050","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:56.265172","level":"info","event":"Processed protocol: 1000050","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:56.265584","level":"info","event":"Processing protocol: 1000049","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:57.922594","level":"info","event":"Inserted data for protocol: 1000049","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:57.922727","level":"info","event":"Processed protocol: 1000049","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:57.922787","level":"info","event":"Processing protocol: 1000048","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:59.403329","level":"info","event":"Inserted data for protocol: 1000048","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:59.403650","level":"info","event":"Processed protocol: 1000048","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:57:59.403804","level":"info","event":"Processing protocol: 1000047","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:00.825811","level":"info","event":"Inserted data for protocol: 1000047","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:00.825957","level":"info","event":"Processed protocol: 1000047","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:00.826050","level":"info","event":"Processing protocol: 1000046","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:02.369522","level":"info","event":"Inserted data for protocol: 1000046","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:02.370031","level":"info","event":"Processed protocol: 1000046","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:02.370219","level":"info","event":"Processing protocol: 1000045","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:03.771201","level":"info","event":"Inserted data for protocol: 1000045","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:03.771631","level":"info","event":"Processed protocol: 1000045","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:03.771700","level":"info","event":"Processing protocol: 1000044","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:05.496853","level":"info","event":"Inserted data for protocol: 1000044","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:05.497009","level":"info","event":"Processed protocol: 1000044","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:05.497138","level":"info","event":"Processing protocol: 1000043","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:06.967169","level":"info","event":"Inserted data for protocol: 1000043","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:06.967577","level":"info","event":"Processed protocol: 1000043","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:06.967644","level":"info","event":"Processing protocol: 1000042","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:08.593296","level":"info","event":"Inserted data for protocol: 1000042","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:08.593521","level":"info","event":"Processed protocol: 1000042","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:08.593995","level":"info","event":"Processing protocol: 1000041","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:10.537681","level":"info","event":"Inserted data for protocol: 1000041","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:10.538668","level":"info","event":"Processed protocol: 1000041","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:10.538910","level":"info","event":"Processing protocol: 1000040","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:12.030372","level":"info","event":"Inserted data for protocol: 1000040","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:12.031029","level":"info","event":"Processed protocol: 1000040","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:12.031227","level":"info","event":"Processing protocol: 1000039","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:16.767893","level":"info","event":"Inserted data for protocol: 1000039","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:16.768333","level":"info","event":"Processed protocol: 1000039","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:16.768424","level":"info","event":"Processing protocol: 1000038","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:16.808559Z","level":"error","event":"Server indicated the task shouldn't be running anymore. Terminating process","detail":{"detail":{"reason":"not_running","message":"TI is no longer in the running state and task should terminate","current_state":"success"}},"logger":"task"}
{"timestamp":"2025-09-08T06:58:16.829172Z","level":"error","event":"Task killed!","logger":"task"}
