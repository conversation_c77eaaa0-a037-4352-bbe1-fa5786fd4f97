{"timestamp":"2025-09-08T06:40:44.619020","level":"info","event":"DAG bundles loaded: dags-folder","logger":"airflow.dag_processing.bundles.manager.DagBundlesManager"}
{"timestamp":"2025-09-08T06:40:44.619384","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln_to_mstack_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:40:44.623389","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:40:44.976289","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:40:44.979023","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:40:45.074722","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:40:45.088433","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:40:45.091196","level":"info","event":"Creating or retrieving cached MongoClient for URI ending in ...IdleTimeMS=120000%22","logger":"root"}
{"timestamp":"2025-09-08T06:40:45.097359","level":"warning","event":"maxidletimems must be an integer or float","category":"UserWarning","filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/uri_parser_shared.py","lineno":379,"logger":"py.warnings"}
{"timestamp":"2025-09-08T06:40:45.097464","level":"info","event":"You appear to be connected to a CosmosDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/cosmosdb","logger":"pymongo.client"}
{"timestamp":"2025-09-08T06:40:45.378366","level":"info","event":"You appear to be connected to a CosmosDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/cosmosdb","logger":"pymongo.client"}
{"timestamp":"2025-09-08T06:40:48.444488","level":"info","event":"MongoClient connected and verified successfully.","logger":"root"}
{"timestamp":"2025-09-08T06:40:48.445198","level":"info","event":"MongoOperations initialized for database 'eln' using shared client for ...IdleTimeMS=120000%22","logger":"root"}
{"timestamp":"2025-09-08T06:40:48.445299","level":"info","event":"Getting all protocols from ELN","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:50.714529","level":"info","event":"Total protocols: 315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:50.714626","level":"info","event":"Processing protocol: 1000346","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:51.909390","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:51.910226Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:40:52.191597","level":"info","event":"Inserted data for protocol: 1000346","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:52.191683","level":"info","event":"Processed protocol: 1000346","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:52.191727","level":"info","event":"Processing protocol: 1000345","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:53.348076","level":"error","event":"Headers not as expected: ['AMBA', 151.2, 50, 0.3, 1, '', '', 'Source- china', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:53.348880Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:40:53.631894","level":"info","event":"Inserted data for protocol: 1000345","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:53.632009","level":"info","event":"Processed protocol: 1000345","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:53.632192","level":"info","event":"Processing protocol: 1000344","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:55.029387","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:55.032674Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:40:55.398129","level":"info","event":"Inserted data for protocol: 1000344","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:55.398264","level":"info","event":"Processed protocol: 1000344","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:55.398343","level":"info","event":"Processing protocol: 1000343","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:56.597072","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:56.598477","level":"error","event":"Headers not as expected: ['TMP(i)', 55.2, '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:56.619317Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:40:56.619550Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:40:56.978735","level":"info","event":"Inserted data for protocol: 1000343","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:56.979051","level":"info","event":"Processed protocol: 1000343","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:56.979112","level":"info","event":"Processing protocol: 1000342","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:58.663054","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:58.670419Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:40:58.946690","level":"info","event":"Inserted data for protocol: 1000342","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:58.946843","level":"info","event":"Processed protocol: 1000342","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:40:58.946921","level":"info","event":"Processing protocol: 1000341","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:00.206333","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:00.207487Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:00.483918","level":"info","event":"Inserted data for protocol: 1000341","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:00.484194","level":"info","event":"Processed protocol: 1000341","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:00.484361","level":"info","event":"Processing protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:01.603677","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:01.631556Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:02.394302","level":"info","event":"Inserted data for protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:02.394620","level":"info","event":"Processed protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:02.394758","level":"info","event":"Processing protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:03.820226","level":"error","event":"Headers not as expected: ['', '', 98.96, 99, 10.770294494238, 10.662591549296, 0.10774647887324, 1.02, 'Commercial', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:03.822021Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:04.099785","level":"info","event":"Inserted data for protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:04.100184","level":"info","event":"Processed protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:04.100406","level":"info","event":"Processing protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:05.569967","level":"error","event":"Headers not as expected: ['', '', 98.96, 99, 9.7029680128273, 9.605938332699, 0.097068899885802, 1.02, 'Commercial', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:05.572694Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:05.859152","level":"info","event":"Inserted data for protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:05.859471","level":"info","event":"Processed protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:05.859647","level":"info","event":"Processing protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:07.289628","level":"error","event":"Headers not as expected: ['', '', 98.96, 99, 10.770294494238, 10.662591549296, 0.10774647887324, 1.02, 'Commercial', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:07.291198Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:07.580069","level":"info","event":"Inserted data for protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:07.580166","level":"info","event":"Processed protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:07.580205","level":"info","event":"Processing protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:09.049033","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:09.050184Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:09.354853","level":"info","event":"Inserted data for protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:09.355904","level":"info","event":"Processed protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:09.356199","level":"info","event":"Processing protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:10.811380","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:10.813218Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:11.417577","level":"info","event":"Inserted data for protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:11.417963","level":"info","event":"Processed protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:11.418187","level":"info","event":"Processing protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:13.675091","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:13.676232Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:14.308836","level":"info","event":"Inserted data for protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:14.309343","level":"info","event":"Processed protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:14.309484","level":"info","event":"Processing protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:15.499664","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:15.501637Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:15.784678","level":"info","event":"Inserted data for protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:15.785028","level":"info","event":"Processed protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:15.785323","level":"info","event":"Processing protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:17.150785","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:17.153380Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:17.434180","level":"info","event":"Inserted data for protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:17.434389","level":"info","event":"Processed protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:17.434539","level":"info","event":"Processing protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:18.592318","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:18.593293Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:18.866895","level":"info","event":"Inserted data for protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:18.867203","level":"info","event":"Processed protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:18.867300","level":"info","event":"Processing protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:20.208862","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:20.210329Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:20.493105","level":"info","event":"Inserted data for protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:20.493445","level":"info","event":"Processed protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:20.493636","level":"info","event":"Processing protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:21.582807","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:21.583836Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:21.855896","level":"info","event":"Inserted data for protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:21.856322","level":"info","event":"Processed protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:21.856516","level":"info","event":"Processing protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:23.473101","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:23.475907Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:23.758797","level":"info","event":"Inserted data for protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:23.759393","level":"info","event":"Processed protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:23.759567","level":"info","event":"Processing protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:24.874594","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:24.889953Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:25.159212","level":"info","event":"Inserted data for protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:25.159278","level":"info","event":"Processed protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:25.159323","level":"info","event":"Processing protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:28.309353","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:28.310750Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:28.909108","level":"info","event":"Inserted data for protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:28.909178","level":"info","event":"Processed protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:28.909223","level":"info","event":"Processing protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:31.729896","level":"error","event":"Headers not as expected: ['', '', 98.96, 99, 9.7029680128273, 9.605938332699, 0.097068899885802, 1.02, 'Commercial', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:31.730634Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:32.916175","level":"info","event":"Inserted data for protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:32.916476","level":"info","event":"Processed protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:32.916758","level":"info","event":"Processing protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:35.293681","level":"error","event":"Headers not as expected: ['', '', 98.96, 99, 9.7029680128273, 9.605938332699, 0.097068899885802, 1.02, 'Commercial', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:35.295678Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:35.633549","level":"info","event":"Inserted data for protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:35.634024","level":"info","event":"Processed protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:35.634295","level":"info","event":"Processing protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:38.376635","level":"error","event":"Headers not as expected: ['', '', 98.96, 99, 9.7029680128273, 9.605938332699, 0.097068899885802, 1.02, 'Commercial', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:38.391743Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:38.691824","level":"info","event":"Inserted data for protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:38.692202","level":"info","event":"Processed protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:38.692455","level":"info","event":"Processing protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:40.088583","level":"error","event":"Headers not as expected: ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:40.090079Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:40.371283","level":"info","event":"Inserted data for protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:40.371670","level":"info","event":"Processed protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:40.371942","level":"info","event":"Processing protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:41.747967","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:41.748652Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:42.290624","level":"info","event":"Inserted data for protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:42.290886","level":"info","event":"Processed protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:42.291330","level":"info","event":"Processing protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:43.648636","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:43.662916Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:43.929894","level":"info","event":"Inserted data for protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:43.930367","level":"info","event":"Processed protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:43.930601","level":"info","event":"Processing protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:45.329395","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:45.330289Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:45.617258","level":"info","event":"Inserted data for protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:45.617565","level":"info","event":"Processed protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:45.617730","level":"info","event":"Processing protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:46.945221","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:46.946271Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:47.219847","level":"info","event":"Inserted data for protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:47.220043","level":"info","event":"Processed protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:47.220123","level":"info","event":"Processing protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:48.581289","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:48.583952Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:48.869283","level":"info","event":"Inserted data for protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:48.869431","level":"info","event":"Processed protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:48.869530","level":"info","event":"Processing protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:50.553125","level":"error","event":"Headers not as expected: ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:50.555261Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:50.846094","level":"info","event":"Inserted data for protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:50.846424","level":"info","event":"Processed protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:50.846625","level":"info","event":"Processing protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:52.206882","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:52.208502Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:52.531161","level":"info","event":"Inserted data for protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:52.532084","level":"info","event":"Processed protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:52.532363","level":"info","event":"Processing protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:54.768486","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:54.806688Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:55.054657","level":"info","event":"Inserted data for protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:55.054762","level":"info","event":"Processed protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:55.054818","level":"info","event":"Processing protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:56.226702","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:56.228544Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:56.518893","level":"info","event":"Inserted data for protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:56.518997","level":"info","event":"Processed protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:56.519042","level":"info","event":"Processing protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:57.635055","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:57.637879Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:57.940948","level":"info","event":"Inserted data for protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:57.941034","level":"info","event":"Processed protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:57.941099","level":"info","event":"Processing protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:59.264224","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:59.265112Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:41:59.575595","level":"info","event":"Inserted data for protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:59.575947","level":"info","event":"Processed protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:41:59.576190","level":"info","event":"Processing protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:01.113782","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:01.133308Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:01.412503","level":"info","event":"Inserted data for protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:01.412926","level":"info","event":"Processed protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:01.413230","level":"info","event":"Processing protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:02.564198","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:02.565927Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:02.566806Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:02.566106","level":"error","event":"Headers not as expected: ['Linseed oil', 166.8, '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:02.859598","level":"info","event":"Inserted data for protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:02.859924","level":"info","event":"Processed protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:02.860075","level":"info","event":"Processing protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:03.994151","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:03.995037Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:04.282208","level":"info","event":"Inserted data for protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:04.282542","level":"info","event":"Processed protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:04.282876","level":"info","event":"Processing protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:05.457710","level":"error","event":"Headers not as expected: ['', '2-methyl THF', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', 'Hyma', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:05.458478Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:05.735158","level":"info","event":"Inserted data for protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:05.735717","level":"info","event":"Processed protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:05.736122","level":"info","event":"Processing protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:07.404433","level":"error","event":"Headers not as expected: ['', 'Thionyl chloride', 118.97, 98, 42.018330262593, 41.177963657341, 0.34612056533026, 1.2, 'AVRA', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:07.423789Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:07.698639","level":"info","event":"Inserted data for protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:07.698827","level":"info","event":"Processed protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:07.698955","level":"info","event":"Processing protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:09.116740","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:09.118558Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:09.426314","level":"info","event":"Inserted data for protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:09.426867","level":"info","event":"Processed protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:09.427243","level":"info","event":"Processing protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:11.076150","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:11.077279Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:11.436789","level":"info","event":"Inserted data for protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:11.437156","level":"info","event":"Processed protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:11.437493","level":"info","event":"Processing protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:12.586273","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:12.607888Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:12.865439","level":"info","event":"Inserted data for protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:12.865717","level":"info","event":"Processed protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:12.865867","level":"info","event":"Processing protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:13.974900","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:13.976007Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:13.976504Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:13.975613","level":"error","event":"Headers not as expected: ['Lauric acid', 280, '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:14.255417","level":"info","event":"Inserted data for protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:14.255604","level":"info","event":"Processed protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:14.255664","level":"info","event":"Processing protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:15.820581","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:15.822252Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:16.116680","level":"info","event":"Inserted data for protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:16.116757","level":"info","event":"Processed protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:16.116803","level":"info","event":"Processing protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:17.682929","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:17.696186Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:18.025763","level":"info","event":"Inserted data for protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:18.026068","level":"info","event":"Processed protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:18.026263","level":"info","event":"Processing protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:19.620183","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:19.622110Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:20.750154","level":"info","event":"Inserted data for protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:20.751248","level":"info","event":"Processed protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:20.751533","level":"info","event":"Processing protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:22.235438","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:22.237378Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:22.515617","level":"info","event":"Inserted data for protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:22.515933","level":"info","event":"Processed protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:22.516173","level":"info","event":"Processing protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:23.710438","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:23.739920Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:23.988206","level":"info","event":"Inserted data for protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:23.988397","level":"info","event":"Processed protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:23.988533","level":"info","event":"Processing protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:25.132061","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:25.133180Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:25.418752","level":"info","event":"Inserted data for protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:25.419076","level":"info","event":"Processed protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:25.419362","level":"info","event":"Processing protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:26.583760","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:26.584871Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:26.865671","level":"info","event":"Inserted data for protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:26.865771","level":"info","event":"Processed protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:26.865822","level":"info","event":"Processing protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:28.431887","level":"error","event":"Headers not as expected: ['', 'Thionyl chloride', 118.97, 98, 42.018330262593, 41.177963657341, 0.34612056533026, 1.2, 'AVRA', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:28.434305Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:29.271754","level":"info","event":"Inserted data for protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:29.271821","level":"info","event":"Processed protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:29.271867","level":"info","event":"Processing protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:30.693835","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:30.695410Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:31.245614","level":"info","event":"Inserted data for protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:31.245794","level":"info","event":"Processed protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:31.245880","level":"info","event":"Processing protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:32.307620","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:32.309426Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:32.584058","level":"info","event":"Inserted data for protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:32.584248","level":"info","event":"Processed protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:32.584352","level":"info","event":"Processing protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:33.705907","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:33.707167Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:33.982191","level":"info","event":"Inserted data for protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:33.982472","level":"info","event":"Processed protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:33.982828","level":"info","event":"Processing protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:35.190847","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:35.205069Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:35.468194","level":"info","event":"Inserted data for protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:35.468769","level":"info","event":"Processed protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:35.469306","level":"info","event":"Processing protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:37.127225","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:37.129208Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:37.419498","level":"info","event":"Inserted data for protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:37.419918","level":"info","event":"Processed protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:37.420297","level":"info","event":"Processing protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:38.569242","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:38.570373Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:38.570608","level":"error","event":"Headers not as expected: ['linseed oil', 166.8, '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:38.571566Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:38.939050","level":"info","event":"Inserted data for protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:38.939278","level":"info","event":"Processed protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:38.939421","level":"info","event":"Processing protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:40.712327","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:40.713103Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:40.991545","level":"info","event":"Inserted data for protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:40.991607","level":"info","event":"Processed protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:40.991651","level":"info","event":"Processing protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:42.095631","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:42.100723Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:42.381486","level":"info","event":"Inserted data for protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:42.381688","level":"info","event":"Processed protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:42.381782","level":"info","event":"Processing protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:43.483873","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:43.485288Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:43.780121","level":"info","event":"Inserted data for protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:43.780374","level":"info","event":"Processed protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:43.780601","level":"info","event":"Processing protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:45.152308","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:45.152791Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:45.443331","level":"info","event":"Inserted data for protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:45.443603","level":"info","event":"Processed protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:45.443790","level":"info","event":"Processing protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:46.595543","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:46.596786Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:46.865499","level":"info","event":"Inserted data for protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:46.865718","level":"info","event":"Processed protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:46.865924","level":"info","event":"Processing protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:48.299717","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:48.300419Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:48.300605Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:48.300198","level":"error","event":"Headers not as expected: ['SOFA', 29.7, 1069.2, '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:48.577877","level":"info","event":"Inserted data for protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:48.578209","level":"info","event":"Processed protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:48.578402","level":"info","event":"Processing protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:49.702664","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:49.705469","level":"error","event":"Headers not as expected: ['SOFA', 29.7, 1069.2, '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:49.713348Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:49.713648Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:49.988483","level":"info","event":"Inserted data for protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:49.988854","level":"info","event":"Processed protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:49.989179","level":"info","event":"Processing protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:51.363101","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:51.364422Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:51.642238","level":"info","event":"Inserted data for protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:51.642486","level":"info","event":"Processed protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:51.642616","level":"info","event":"Processing protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:52.751545","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:52.765704Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:53.043645","level":"info","event":"Inserted data for protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:53.043770","level":"info","event":"Processed protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:53.043847","level":"info","event":"Processing protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:54.155904","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:54.157153Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:54.527845","level":"info","event":"Inserted data for protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:54.528320","level":"info","event":"Processed protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:54.528662","level":"info","event":"Processing protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:56.016413","level":"error","event":"Headers not as expected: ['', '', 18, 100, {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', 'tap water', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:56.017549Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:56.301916","level":"info","event":"Inserted data for protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:56.302146","level":"info","event":"Processed protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:56.302230","level":"info","event":"Processing protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:57.400047","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:57.461453Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:57.762574","level":"info","event":"Inserted data for protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:57.762924","level":"info","event":"Processed protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:57.763202","level":"info","event":"Processing protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:59.013931","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:59.015180Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:42:59.301109","level":"info","event":"Inserted data for protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:59.301176","level":"info","event":"Processed protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:42:59.301239","level":"info","event":"Processing protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:00.361111","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:00.361906Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:00.648282","level":"info","event":"Inserted data for protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:00.648438","level":"info","event":"Processed protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:00.648501","level":"info","event":"Processing protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:02.051433","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:02.051915Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:02.337891","level":"info","event":"Inserted data for protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:02.338015","level":"info","event":"Processed protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:02.338077","level":"info","event":"Processing protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:03.689351","level":"error","event":"Headers not as expected: ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:03.691521Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:03.968011","level":"info","event":"Inserted data for protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:03.968178","level":"info","event":"Processed protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:03.968338","level":"info","event":"Processing protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:05.383338","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:05.383900Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:05.675490","level":"info","event":"Inserted data for protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:05.675839","level":"info","event":"Processed protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:05.676286","level":"info","event":"Processing protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:06.790705","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:06.791709Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:06.792041","level":"error","event":"Headers not as expected: ['Lauric acid ', 280, '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:06.792964Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:07.076335","level":"info","event":"Inserted data for protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:07.076631","level":"info","event":"Processed protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:07.076867","level":"info","event":"Processing protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:08.160715","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:08.161934Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:08.436114","level":"info","event":"Inserted data for protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:08.436456","level":"info","event":"Processed protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:08.436709","level":"info","event":"Processing protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:09.794791","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:09.815838Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:10.084636","level":"info","event":"Inserted data for protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:10.084698","level":"info","event":"Processed protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:10.084741","level":"info","event":"Processing protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:11.500391","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:11.508184Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:11.793479","level":"info","event":"Inserted data for protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:11.793837","level":"info","event":"Processed protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:11.794073","level":"info","event":"Processing protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:12.909308","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:12.910404Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:13.185318","level":"info","event":"Inserted data for protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:13.185393","level":"info","event":"Processed protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:13.185439","level":"info","event":"Processing protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:14.598467","level":"error","event":"Headers not as expected: ['', '', 172.7, 1.4124293785311, 243.92655367232, '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:14.599137Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:14.876953","level":"info","event":"Inserted data for protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:14.877406","level":"info","event":"Processed protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:14.877620","level":"info","event":"Processing protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:15.993075","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:15.995103","level":"error","event":"Headers not as expected: ['Linseed Oil', 175.8, '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:15.995879Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:15.996214Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:16.284154","level":"info","event":"Inserted data for protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:16.284617","level":"info","event":"Processed protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:16.284908","level":"info","event":"Processing protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:17.437111","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:17.438883Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:17.719362","level":"info","event":"Inserted data for protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:17.719636","level":"info","event":"Processed protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:17.719859","level":"info","event":"Processing protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:19.094637","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:19.096074Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:19.376173","level":"info","event":"Inserted data for protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:19.376620","level":"info","event":"Processed protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:19.376736","level":"info","event":"Processing protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:20.785562","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:20.787340Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:21.094764","level":"info","event":"Inserted data for protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:21.095079","level":"info","event":"Processed protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:21.095268","level":"info","event":"Processing protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:22.205023","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:22.206822Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:22.209081Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:22.206374","level":"error","event":"Headers not as expected: ['n-Butylacetate', 67.5, '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:22.482665","level":"info","event":"Inserted data for protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:22.482820","level":"info","event":"Processed protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:22.482910","level":"info","event":"Processing protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:23.717331","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:23.718178Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:24.050272","level":"info","event":"Inserted data for protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:24.050408","level":"info","event":"Processed protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:24.050489","level":"info","event":"Processing protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:25.523162","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:25.524950Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:25.811651","level":"info","event":"Inserted data for protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:25.811730","level":"info","event":"Processed protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:25.811775","level":"info","event":"Processing protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:26.958797","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:26.959862Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:27.233317","level":"info","event":"Inserted data for protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:27.233506","level":"info","event":"Processed protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:27.233598","level":"info","event":"Processing protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:28.606740","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:28.607415Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:28.893132","level":"info","event":"Inserted data for protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:28.893405","level":"info","event":"Processed protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:28.893607","level":"info","event":"Processing protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:30.523032","level":"info","event":"Inserted data for protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:30.523191","level":"info","event":"Processed protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:30.523300","level":"info","event":"Processing protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:31.630554","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:31.655997Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:31.909585","level":"info","event":"Inserted data for protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:31.910041","level":"info","event":"Processed protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:31.910251","level":"info","event":"Processing protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:33.247357","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:33.248608Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:33.526714","level":"info","event":"Inserted data for protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:33.526789","level":"info","event":"Processed protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:33.526838","level":"info","event":"Processing protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:34.632863","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:34.633207Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:34.906135","level":"info","event":"Inserted data for protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:34.906538","level":"info","event":"Processed protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:34.906809","level":"info","event":"Processing protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:36.495906","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:36.497506Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:36.792227","level":"info","event":"Inserted data for protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:36.792296","level":"info","event":"Processed protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:36.792339","level":"info","event":"Processing protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:38.134735","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:38.136174Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:38.412764","level":"info","event":"Inserted data for protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:38.413167","level":"info","event":"Processed protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:38.413451","level":"info","event":"Processing protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:39.534513","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:39.536675Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:39.537965Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:39.535882","level":"error","event":"Headers not as expected: ['n-Butylacetate', 67.5, ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:39.811868","level":"info","event":"Inserted data for protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:39.812287","level":"info","event":"Processed protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:39.812550","level":"info","event":"Processing protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:41.171157","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:41.172474Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:41.454185","level":"info","event":"Inserted data for protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:41.454473","level":"info","event":"Processed protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:41.454654","level":"info","event":"Processing protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:42.543393","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:42.544891Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:42.830800","level":"info","event":"Inserted data for protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:42.833110","level":"info","event":"Processed protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:42.833409","level":"info","event":"Processing protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:43.958486","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:43.959896Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:44.238309","level":"info","event":"Inserted data for protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:44.238515","level":"info","event":"Processed protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:44.238685","level":"info","event":"Processing protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:45.349787","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:45.351190Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:45.351730Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:45.351458","level":"error","event":"Headers not as expected: [\"Name of Rm's\", 'Qty of RM for 100g scale in g', \"Qty of Rm's for 200g scale in g\", '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:45.625860","level":"info","event":"Inserted data for protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:45.626161","level":"info","event":"Processed protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:45.626539","level":"info","event":"Processing protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:47.020942","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:47.022698Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:47.304350","level":"info","event":"Inserted data for protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:47.304599","level":"info","event":"Processed protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:47.304786","level":"info","event":"Processing protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:48.378789","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:48.380167","level":"error","event":"Headers not as expected: ['linoleic acid', 178.2, ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:48.397165Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:48.397393Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:48.654968","level":"info","event":"Inserted data for protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:48.655320","level":"info","event":"Processed protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:48.655519","level":"info","event":"Processing protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:49.905049","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:49.926506Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:50.227232","level":"info","event":"Inserted data for protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:50.227395","level":"info","event":"Processed protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:50.227498","level":"info","event":"Processing protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:51.336428","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:51.337424Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:51.618687","level":"info","event":"Inserted data for protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:51.619028","level":"info","event":"Processed protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:51.619226","level":"info","event":"Processing protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:53.228465","level":"error","event":"Headers not as expected: ['', 'Thionyl chloride', 118.97, 98, 21.009165131297, 20.588981828671, 0.17306028266513, 1.2, 'AVRA', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:53.228868Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:53.520158","level":"info","event":"Inserted data for protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:53.520523","level":"info","event":"Processed protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:53.520753","level":"info","event":"Processing protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:55.315328","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:55.321336Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:55.598604","level":"info","event":"Inserted data for protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:55.598926","level":"info","event":"Processed protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:55.599091","level":"info","event":"Processing protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:56.924354","level":"error","event":"Headers not as expected: ['', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:56.925178Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:57.216036","level":"info","event":"Inserted data for protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:57.216180","level":"info","event":"Processed protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:57.216273","level":"info","event":"Processing protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:58.340209","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:58.342780Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:43:58.619215","level":"info","event":"Inserted data for protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:58.619489","level":"info","event":"Processed protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:43:58.619715","level":"info","event":"Processing protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:00.473630","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:00.475284Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:00.752271","level":"info","event":"Inserted data for protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:00.752450","level":"info","event":"Processed protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:00.752729","level":"info","event":"Processing protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:01.877508","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:01.879383Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:02.160247","level":"info","event":"Inserted data for protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:02.160435","level":"info","event":"Processed protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:02.160517","level":"info","event":"Processing protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:03.339036","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:03.339573Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:03.620633","level":"info","event":"Inserted data for protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:03.620714","level":"info","event":"Processed protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:03.620771","level":"info","event":"Processing protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:04.761158","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:04.762114","level":"error","event":"Headers not as expected: ['n-Butyl acetate', 67.5, '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:04.762763Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:04.763102Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:05.268323","level":"info","event":"Inserted data for protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:05.268415","level":"info","event":"Processed protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:05.268503","level":"info","event":"Processing protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:06.626190","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', 0, {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:06.626827Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:06.900757","level":"info","event":"Inserted data for protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:06.900859","level":"info","event":"Processed protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:06.900911","level":"info","event":"Processing protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:08.055817","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:08.056723Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:08.343786","level":"info","event":"Inserted data for protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:08.344183","level":"info","event":"Processed protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:08.344412","level":"info","event":"Processing protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:09.775641","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:09.776738Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:10.063594","level":"info","event":"Inserted data for protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:10.063672","level":"info","event":"Processed protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:10.063732","level":"info","event":"Processing protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:11.287567","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:11.289146Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:11.290466Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:11.288821","level":"error","event":"Headers not as expected: [\"Name of Rm's\", 'Qty of Rm in g', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:11.567469","level":"info","event":"Inserted data for protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:11.567785","level":"info","event":"Processed protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:11.568025","level":"info","event":"Processing protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:12.709619","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:12.710091Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:12.988316","level":"info","event":"Inserted data for protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:12.988798","level":"info","event":"Processed protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:12.989948","level":"info","event":"Processing protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:14.388666","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:14.390670Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:14.668382","level":"info","event":"Inserted data for protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:14.668769","level":"info","event":"Processed protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:14.669039","level":"info","event":"Processing protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:16.047642","level":"error","event":"Headers not as expected: ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:16.049064Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:16.324906","level":"info","event":"Inserted data for protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:16.325303","level":"info","event":"Processed protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:16.325583","level":"info","event":"Processing protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:17.477642","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', 100, {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:17.479249Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:17.762202","level":"info","event":"Inserted data for protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:17.762535","level":"info","event":"Processed protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:17.762767","level":"info","event":"Processing protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:19.395718","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:19.397133","level":"error","event":"Headers not as expected: ['n-butylacetate', 67.5, ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:19.398050Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:19.398754Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:19.712380","level":"info","event":"Inserted data for protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:19.712584","level":"info","event":"Processed protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:19.712723","level":"info","event":"Processing protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:21.256311","level":"error","event":"Headers not as expected: ['', '', '', '', '', '', '', '', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:21.256759Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:21.544737","level":"info","event":"Inserted data for protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:21.545187","level":"info","event":"Processed protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:21.545400","level":"info","event":"Processing protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:22.668956","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:22.670496Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:22.943686","level":"info","event":"Inserted data for protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:22.943839","level":"info","event":"Processed protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:22.943972","level":"info","event":"Processing protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:24.242835","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:24.243670Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:24.531013","level":"info","event":"Inserted data for protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:24.531687","level":"info","event":"Processed protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:24.531890","level":"info","event":"Processing protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:26.284053","level":"error","event":"Headers not as expected: ['', 'Thionyl chloride', 118.97, 98, 10.504582565648, 10.294490914335, 0.086530141332564, 1.2, 'AVRA', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:26.284833Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:26.574051","level":"info","event":"Inserted data for protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:26.574433","level":"info","event":"Processed protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:26.574709","level":"info","event":"Processing protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:27.680187","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:27.680953Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:27.989642","level":"info","event":"Inserted data for protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:27.989730","level":"info","event":"Processed protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:27.989781","level":"info","event":"Processing protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:29.580824","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:29.581935Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:29.867742","level":"info","event":"Inserted data for protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:29.868318","level":"info","event":"Processed protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:29.868543","level":"info","event":"Processing protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:30.975621","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:30.977726Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:31.261156","level":"info","event":"Inserted data for protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:31.261426","level":"info","event":"Processed protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:31.261644","level":"info","event":"Processing protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:32.400694","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:32.423967Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:32.731805","level":"info","event":"Inserted data for protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:32.732144","level":"info","event":"Processed protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:32.732319","level":"info","event":"Processing protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:33.905431","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:33.905822Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:34.183810","level":"info","event":"Inserted data for protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:34.184065","level":"info","event":"Processed protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:34.184203","level":"info","event":"Processing protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:35.577403","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:35.578394Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:35.861115","level":"info","event":"Inserted data for protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:35.861427","level":"info","event":"Processed protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:35.861644","level":"info","event":"Processing protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:37.208886","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:37.212399Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:37.491553","level":"info","event":"Inserted data for protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:37.491839","level":"info","event":"Processed protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:37.492180","level":"info","event":"Processing protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:38.910949","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:38.912003Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:39.188438","level":"info","event":"Inserted data for protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:39.188524","level":"info","event":"Processed protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:39.188586","level":"info","event":"Processing protocol: 1000179","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:40.469307","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', 'Quiligens', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:40.469904Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:41.123953","level":"info","event":"Inserted data for protocol: 1000179","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:41.124044","level":"info","event":"Processed protocol: 1000179","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:41.124090","level":"info","event":"Processing protocol: 1000178","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:42.567958","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:42.577909Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:42.872192","level":"info","event":"Inserted data for protocol: 1000178","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:42.872263","level":"info","event":"Processed protocol: 1000178","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:42.872305","level":"info","event":"Processing protocol: 1000177","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:44.310913","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:44.312505Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:44.602361","level":"info","event":"Inserted data for protocol: 1000177","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:44.602519","level":"info","event":"Processed protocol: 1000177","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:44.602642","level":"info","event":"Processing protocol: 1000176","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:49.021857","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:49.024397Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:49.024611","level":"error","event":"Headers not as expected: ['n-Butyl Acetate', 7.5, '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:49.062303Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:49.996949","level":"info","event":"Inserted data for protocol: 1000176","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:49.997250","level":"info","event":"Processed protocol: 1000176","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:49.997421","level":"info","event":"Processing protocol: 1000175","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:56.783704","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:56.785937Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:44:57.413510","level":"info","event":"Inserted data for protocol: 1000175","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:57.413792","level":"info","event":"Processed protocol: 1000175","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:57.413958","level":"info","event":"Processing protocol: 1000174","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:59.874666","level":"error","event":"Headers not as expected: ['', 'Thionyl chloride', 118.97, 98, 42.018330262593, 41.177963657341, 0.34612056533026, 1.2, 'AVRA', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:44:59.875973Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:00.169739","level":"info","event":"Inserted data for protocol: 1000174","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:00.169848","level":"info","event":"Processed protocol: 1000174","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:00.169905","level":"info","event":"Processing protocol: 1000173","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:01.518086","level":"error","event":"Headers not as expected: ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:01.520391Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:01.800555","level":"info","event":"Inserted data for protocol: 1000173","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:01.800877","level":"info","event":"Processed protocol: 1000173","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:01.801099","level":"info","event":"Processing protocol: 1000172","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:03.161086","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:03.161593Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:03.440236","level":"info","event":"Inserted data for protocol: 1000172","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:03.440453","level":"info","event":"Processed protocol: 1000172","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:03.440744","level":"info","event":"Processing protocol: 1000171","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:04.780395","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:04.781169Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:05.056584","level":"info","event":"Inserted data for protocol: 1000171","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:05.056666","level":"info","event":"Processed protocol: 1000171","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:05.056737","level":"info","event":"Processing protocol: 1000170","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:06.850660","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:06.852371Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:07.128006","level":"info","event":"Inserted data for protocol: 1000170","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:07.128129","level":"info","event":"Processed protocol: 1000170","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:07.128234","level":"info","event":"Processing protocol: 1000169","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:08.461064","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:08.462222Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:08.739468","level":"info","event":"Inserted data for protocol: 1000169","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:08.740648","level":"info","event":"Processed protocol: 1000169","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:08.740827","level":"info","event":"Processing protocol: 1000168","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:10.009743","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:10.010169Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:10.010343Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:10.010057","level":"error","event":"Headers not as expected: ['SOFA', 162.6, '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:10.289887","level":"info","event":"Inserted data for protocol: 1000168","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:10.289958","level":"info","event":"Processed protocol: 1000168","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:10.290006","level":"info","event":"Processing protocol: 1000167","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:11.376443","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:11.376793Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:11.376918Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:11.376740","level":"error","event":"Headers not as expected: ['Soya fatty acids', 29.7, 356.4, ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:11.662178","level":"info","event":"Inserted data for protocol: 1000167","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:11.662457","level":"info","event":"Processed protocol: 1000167","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:11.662673","level":"info","event":"Processing protocol: 1000166","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:13.093269","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:13.101996Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:13.391164","level":"info","event":"Inserted data for protocol: 1000166","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:13.391224","level":"info","event":"Processed protocol: 1000166","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:13.391265","level":"info","event":"Processing protocol: 1000165","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:14.504125","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', 'Hyma', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:14.505250Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:14.783224","level":"info","event":"Inserted data for protocol: 1000165","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:14.783469","level":"info","event":"Processed protocol: 1000165","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:14.783614","level":"info","event":"Processing protocol: 1000164","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:15.875727","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:15.877850Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:16.148223","level":"info","event":"Inserted data for protocol: 1000164","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:16.148298","level":"info","event":"Processed protocol: 1000164","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:16.148340","level":"info","event":"Processing protocol: 1000163","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:17.348581","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:17.349753Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:17.625633","level":"info","event":"Inserted data for protocol: 1000163","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:17.625907","level":"info","event":"Processed protocol: 1000163","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:17.626169","level":"info","event":"Processing protocol: 1000162","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:18.784959","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:18.806203Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:19.090385","level":"info","event":"Inserted data for protocol: 1000162","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:19.090716","level":"info","event":"Processed protocol: 1000162","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:19.090916","level":"info","event":"Processing protocol: 1000161","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:20.200501","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:20.202695Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:20.484925","level":"info","event":"Inserted data for protocol: 1000161","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:20.485327","level":"info","event":"Processed protocol: 1000161","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:20.485759","level":"info","event":"Processing protocol: 1000160","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:21.892833","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:21.893510Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:21.893803Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:21.893184","level":"error","event":"Headers not as expected: ['Glycerin', 672, ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:22.172869","level":"info","event":"Inserted data for protocol: 1000160","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:22.173238","level":"info","event":"Processed protocol: 1000160","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:22.173389","level":"info","event":"Processing protocol: 1000159","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:23.347609","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:23.348622Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:23.638259","level":"info","event":"Inserted data for protocol: 1000159","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:23.638526","level":"info","event":"Processed protocol: 1000159","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:23.638638","level":"info","event":"Processing protocol: 1000158","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:24.906587","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:24.907631Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:25.191547","level":"info","event":"Inserted data for protocol: 1000158","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:25.191805","level":"info","event":"Processed protocol: 1000158","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:25.191921","level":"info","event":"Processing protocol: 1000157","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:26.845247","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:26.846295Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:27.123902","level":"info","event":"Inserted data for protocol: 1000157","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:27.124034","level":"info","event":"Processed protocol: 1000157","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:27.124075","level":"info","event":"Processing protocol: 1000156","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:28.580970","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:28.581890Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:28.864632","level":"info","event":"Inserted data for protocol: 1000156","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:28.864944","level":"info","event":"Processed protocol: 1000156","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:28.865158","level":"info","event":"Processing protocol: 1000155","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:30.046048","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:30.047260Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:30.320177","level":"info","event":"Inserted data for protocol: 1000155","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:30.320273","level":"info","event":"Processed protocol: 1000155","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:30.320310","level":"info","event":"Processing protocol: 1000154","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:31.493540","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:31.494733Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:31.770933","level":"info","event":"Inserted data for protocol: 1000154","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:31.771286","level":"info","event":"Processed protocol: 1000154","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:31.771457","level":"info","event":"Processing protocol: 1000153","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:33.152415","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:33.153679Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:33.457146","level":"info","event":"Inserted data for protocol: 1000153","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:33.457405","level":"info","event":"Processed protocol: 1000153","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:33.457591","level":"info","event":"Processing protocol: 1000152","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:35.227809","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:35.238803Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:35.965070","level":"info","event":"Inserted data for protocol: 1000152","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:35.965335","level":"info","event":"Processed protocol: 1000152","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:35.965486","level":"info","event":"Processing protocol: 1000151","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:37.181587","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:37.182386Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:37.460481","level":"info","event":"Inserted data for protocol: 1000151","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:37.460739","level":"info","event":"Processed protocol: 1000151","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:37.460884","level":"info","event":"Processing protocol: 1000150","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:38.573888","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:38.575072Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:38.856693","level":"info","event":"Inserted data for protocol: 1000150","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:38.857045","level":"info","event":"Processed protocol: 1000150","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:38.857350","level":"info","event":"Processing protocol: 1000149","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:40.248587","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:40.249885Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:40.538897","level":"info","event":"Inserted data for protocol: 1000149","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:40.539830","level":"info","event":"Processed protocol: 1000149","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:40.540150","level":"info","event":"Processing protocol: 1000148","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:41.726343","level":"error","event":"Headers not as expected: ['', '[Manual Field]', '[Numeric Field]', '[Numeric Field]', {'code': 'VALUE'}, {'code': 'VALUE'}, {'code': 'VALUE'}, '[Numeric Field]', '[Manual Field]', 'Reactant/Catalyst (based on mol eq. of limiting reagent)', '', '', '', '', '', '', '', '', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:41.727297Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:42.002552","level":"info","event":"Inserted data for protocol: 1000148","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:42.002616","level":"info","event":"Processed protocol: 1000148","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:42.002669","level":"info","event":"Processing protocol: 1000147","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:43.075950","level":"error","event":"Headers not as expected: ['', 'Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category', '', '', '', ''] != ['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:43.076458Z","level":"info","event":"['Reactnant name', 'Molecular wt.', 'Strength (%)', 'Sample qty. (gm)', 'Reactant qty. (gm)', 'No. of moles', 'Mol. eq./Rel. wt.', 'Source', 'Category']","chan":"stdout","logger":"task"}
{"timestamp":"2025-09-08T06:45:43.351377","level":"info","event":"Inserted data for protocol: 1000147","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:43.351457","level":"info","event":"Processed protocol: 1000147","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:43.351513","level":"info","event":"Processing protocol: 1000146","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:45.084328","level":"error","event":"Process timed out, PID: 39","logger":"airflow.utils.timeout.TimeoutPosix"}
{"timestamp":"2025-09-08T06:45:45.086218","level":"error","event":"Task failed with exception","logger":"task","error_detail":[{"exc_type":"AirflowTaskTimeout","exc_value":"Timeout, PID: 39","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/sdk/execution_time/task_runner.py","lineno":838,"name":"run"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/sdk/execution_time/task_runner.py","lineno":1125,"name":"_execute_task"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/sdk/bases/operator.py","lineno":408,"name":"wrapper"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/providers/standard/operators/python.py","lineno":212,"name":"execute"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/providers/standard/operators/python.py","lineno":235,"name":"execute_callable"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/sdk/execution_time/callback_runner.py","lineno":81,"name":"run"},{"filename":"/opt/airflow/dags/eln_to_mstack_dag.py","lineno":13,"name":"parse_eln_data"},{"filename":"/opt/airflow/dags/eln/eln_parser.py","lineno":454,"name":"get_all_data"},{"filename":"/opt/airflow/dags/eln/eln_parser.py","lineno":431,"name":"get_protocol_by_id"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/requests/api.py","lineno":73,"name":"get"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/requests/api.py","lineno":59,"name":"request"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/requests/sessions.py","lineno":589,"name":"request"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/requests/sessions.py","lineno":746,"name":"send"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py","lineno":902,"name":"content"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/requests/models.py","lineno":820,"name":"generate"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/urllib3/response.py","lineno":1063,"name":"stream"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/urllib3/response.py","lineno":1219,"name":"read_chunked"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/urllib3/response.py","lineno":1138,"name":"_update_chunk_length"},{"filename":"/usr/local/lib/python3.12/socket.py","lineno":720,"name":"readinto"},{"filename":"/usr/local/lib/python3.12/ssl.py","lineno":1251,"name":"recv_into"},{"filename":"/usr/local/lib/python3.12/ssl.py","lineno":1103,"name":"read"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/utils/timeout.py","lineno":69,"name":"handle_timeout"}]}]}
