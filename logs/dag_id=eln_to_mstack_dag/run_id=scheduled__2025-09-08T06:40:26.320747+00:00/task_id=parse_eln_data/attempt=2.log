{"timestamp":"2025-09-08T06:50:46.880076","level":"info","event":"DAG bundles loaded: dags-folder","logger":"airflow.dag_processing.bundles.manager.DagBundlesManager"}
{"timestamp":"2025-09-08T06:50:46.880788","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln_to_mstack_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:50:46.904702","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:50:46.910474","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:50:46.913371","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:50:46.984941","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:50:46.997955","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:50:47.000995","level":"info","event":"Creating or retrieving cached MongoClient for URI ending in ...IdleTimeMS=120000%22","logger":"root"}
{"timestamp":"2025-09-08T06:50:47.007782","level":"warning","event":"maxidletimems must be an integer or float","category":"UserWarning","filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/uri_parser_shared.py","lineno":379,"logger":"py.warnings"}
{"timestamp":"2025-09-08T06:50:47.007971","level":"info","event":"You appear to be connected to a CosmosDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/cosmosdb","logger":"pymongo.client"}
{"timestamp":"2025-09-08T06:50:47.157356","level":"info","event":"You appear to be connected to a CosmosDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/cosmosdb","logger":"pymongo.client"}
{"timestamp":"2025-09-08T06:50:49.989292","level":"info","event":"MongoClient connected and verified successfully.","logger":"root"}
{"timestamp":"2025-09-08T06:50:49.989925","level":"info","event":"MongoOperations initialized for database 'eln' using shared client for ...IdleTimeMS=120000%22","logger":"root"}
{"timestamp":"2025-09-08T06:50:49.990339","level":"info","event":"Getting all protocols from ELN","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:52.223607","level":"info","event":"Total protocols: 315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:52.223849","level":"info","event":"Processing protocol: 1000346","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:53.731165","level":"info","event":"Inserted data for protocol: 1000346","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:53.731502","level":"info","event":"Processed protocol: 1000346","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:53.731674","level":"info","event":"Processing protocol: 1000345","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:55.681413","level":"info","event":"Inserted data for protocol: 1000345","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:55.681639","level":"info","event":"Processed protocol: 1000345","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:55.681767","level":"info","event":"Processing protocol: 1000344","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:57.593486","level":"info","event":"Inserted data for protocol: 1000344","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:57.593875","level":"info","event":"Processed protocol: 1000344","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:57.594052","level":"info","event":"Processing protocol: 1000343","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:58.977198","level":"info","event":"Inserted data for protocol: 1000343","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:58.977669","level":"info","event":"Processed protocol: 1000343","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:58.977972","level":"info","event":"Processing protocol: 1000342","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:01.485559","level":"info","event":"Inserted data for protocol: 1000342","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:01.485667","level":"info","event":"Processed protocol: 1000342","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:01.485812","level":"info","event":"Processing protocol: 1000341","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:02.906951","level":"info","event":"Inserted data for protocol: 1000341","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:02.935123Z","level":"error","event":"Server indicated the task shouldn't be running anymore. Terminating process","detail":{"detail":{"reason":"not_found","message":"Task Instance not found"}},"logger":"task"}
{"timestamp":"2025-09-08T06:51:02.907188","level":"info","event":"Processed protocol: 1000341","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:02.907273","level":"info","event":"Processing protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:51:02.948834Z","level":"error","event":"Task killed!","logger":"task"}
