{"timestamp":"2025-09-08T07:08:18.570702","level":"info","event":"DAG bundles loaded: dags-folder","logger":"airflow.dag_processing.bundles.manager.DagBundlesManager"}
{"timestamp":"2025-09-08T07:08:18.571593","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln_to_mstack_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T07:08:18.584183","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T07:08:18.592150","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T07:08:18.596233","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T07:08:18.662329","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T07:08:18.685368","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T07:08:18.687760","level":"info","event":"Creating or retrieving cached MongoClient for URI ending in ...IdleTimeMS=120000%22","logger":"root"}
{"timestamp":"2025-09-08T07:08:18.691105","level":"warning","event":"maxidletimems must be an integer or float","category":"UserWarning","filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/uri_parser_shared.py","lineno":379,"logger":"py.warnings"}
{"timestamp":"2025-09-08T07:08:18.691205","level":"info","event":"You appear to be connected to a CosmosDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/cosmosdb","logger":"pymongo.client"}
{"timestamp":"2025-09-08T07:08:18.862874","level":"info","event":"You appear to be connected to a CosmosDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/cosmosdb","logger":"pymongo.client"}
{"timestamp":"2025-09-08T07:08:22.659644","level":"info","event":"MongoClient connected and verified successfully.","logger":"root"}
{"timestamp":"2025-09-08T07:08:22.660108","level":"info","event":"MongoOperations initialized for database 'eln' using shared client for ...IdleTimeMS=120000%22","logger":"root"}
{"timestamp":"2025-09-08T07:08:22.660356","level":"info","event":"Getting all protocols from ELN","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:24.719164","level":"info","event":"Total protocols: 315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:24.719284","level":"info","event":"Processing protocol: 1000346","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:26.118154","level":"info","event":"Inserted data for protocol: 1000346","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:26.118479","level":"info","event":"Processed protocol: 1000346","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:26.118656","level":"info","event":"Processing protocol: 1000345","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:27.516097","level":"info","event":"Inserted data for protocol: 1000345","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:27.517177","level":"info","event":"Processed protocol: 1000345","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:27.517460","level":"info","event":"Processing protocol: 1000344","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:28.900710","level":"info","event":"Inserted data for protocol: 1000344","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:28.901160","level":"info","event":"Processed protocol: 1000344","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:28.901448","level":"info","event":"Processing protocol: 1000343","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:30.289356","level":"info","event":"Inserted data for protocol: 1000343","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:30.289452","level":"info","event":"Processed protocol: 1000343","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:30.289503","level":"info","event":"Processing protocol: 1000342","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:31.723484","level":"info","event":"Inserted data for protocol: 1000342","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:31.723871","level":"info","event":"Processed protocol: 1000342","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:31.724204","level":"info","event":"Processing protocol: 1000341","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:33.215325","level":"info","event":"Inserted data for protocol: 1000341","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:33.215598","level":"info","event":"Processed protocol: 1000341","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:33.215816","level":"info","event":"Processing protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:34.632574","level":"info","event":"Inserted data for protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:34.633038","level":"info","event":"Processed protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:34.633294","level":"info","event":"Processing protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:36.704432","level":"info","event":"Inserted data for protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:36.704805","level":"info","event":"Processed protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:36.704892","level":"info","event":"Processing protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:38.325174","level":"info","event":"Inserted data for protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:38.325402","level":"info","event":"Processed protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:38.325510","level":"info","event":"Processing protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:40.017570","level":"info","event":"Inserted data for protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:40.018099","level":"info","event":"Processed protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:40.018284","level":"info","event":"Processing protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:42.237510","level":"info","event":"Inserted data for protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:42.237870","level":"info","event":"Processed protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:42.238137","level":"info","event":"Processing protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:43.703211","level":"info","event":"Inserted data for protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:43.703635","level":"info","event":"Processed protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:43.703912","level":"info","event":"Processing protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:45.135212","level":"info","event":"Inserted data for protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:45.135319","level":"info","event":"Processed protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:45.135369","level":"info","event":"Processing protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:46.572321","level":"info","event":"Inserted data for protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:46.572707","level":"info","event":"Processed protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:46.572921","level":"info","event":"Processing protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:48.204414","level":"info","event":"Inserted data for protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:48.204518","level":"info","event":"Processed protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:48.204585","level":"info","event":"Processing protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:49.528489","level":"info","event":"Inserted data for protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:49.529394","level":"info","event":"Processed protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:49.529708","level":"info","event":"Processing protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:51.193922","level":"info","event":"Inserted data for protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:51.194110","level":"info","event":"Processed protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:51.194195","level":"info","event":"Processing protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:52.544598","level":"info","event":"Inserted data for protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:52.544715","level":"info","event":"Processed protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:52.544774","level":"info","event":"Processing protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:54.194648","level":"info","event":"Inserted data for protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:54.194924","level":"info","event":"Processed protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:54.195077","level":"info","event":"Processing protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:55.939490","level":"info","event":"Inserted data for protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:55.941128","level":"info","event":"Processed protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:55.941612","level":"info","event":"Processing protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:57.840563","level":"info","event":"Inserted data for protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:57.841161","level":"info","event":"Processed protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:57.841401","level":"info","event":"Processing protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:59.518488","level":"info","event":"Inserted data for protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:59.518760","level":"info","event":"Processed protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:59.518869","level":"info","event":"Processing protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:01.181737","level":"info","event":"Inserted data for protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:01.182080","level":"info","event":"Processed protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:01.182296","level":"info","event":"Processing protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:02.814961","level":"info","event":"Inserted data for protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:02.815437","level":"info","event":"Processed protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:02.815740","level":"info","event":"Processing protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:04.543478","level":"info","event":"Inserted data for protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:04.543822","level":"info","event":"Processed protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:04.544125","level":"info","event":"Processing protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:06.267530","level":"info","event":"Inserted data for protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:06.267612","level":"info","event":"Processed protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:06.267657","level":"info","event":"Processing protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:08.017501","level":"info","event":"Inserted data for protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:08.017563","level":"info","event":"Processed protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:08.017598","level":"info","event":"Processing protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:09.885674","level":"info","event":"Inserted data for protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:09.886591","level":"info","event":"Processed protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:09.886887","level":"info","event":"Processing protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:11.264255","level":"info","event":"Inserted data for protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:11.264444","level":"info","event":"Processed protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:11.264504","level":"info","event":"Processing protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:12.878442","level":"info","event":"Inserted data for protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:12.878843","level":"info","event":"Processed protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:12.879217","level":"info","event":"Processing protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:14.486286","level":"info","event":"Inserted data for protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:14.486631","level":"info","event":"Processed protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:14.486833","level":"info","event":"Processing protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:16.285145","level":"info","event":"Inserted data for protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:16.285474","level":"info","event":"Processed protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:16.285619","level":"info","event":"Processing protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:17.644781","level":"info","event":"Inserted data for protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:17.645144","level":"info","event":"Processed protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:17.645369","level":"info","event":"Processing protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:19.004125","level":"info","event":"Inserted data for protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:19.004214","level":"info","event":"Processed protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:19.004251","level":"info","event":"Processing protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:20.460953","level":"info","event":"Inserted data for protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:20.461028","level":"info","event":"Processed protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:20.461057","level":"info","event":"Processing protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:21.807421","level":"info","event":"Inserted data for protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:21.807955","level":"info","event":"Processed protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:21.808253","level":"info","event":"Processing protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:23.426467","level":"info","event":"Inserted data for protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:23.427238","level":"info","event":"Processed protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:23.427604","level":"info","event":"Processing protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:24.871262","level":"info","event":"Inserted data for protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:24.871589","level":"info","event":"Processed protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:24.871728","level":"info","event":"Processing protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:26.274889","level":"info","event":"Inserted data for protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:26.275077","level":"info","event":"Processed protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:26.275349","level":"info","event":"Processing protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:27.624635","level":"info","event":"Inserted data for protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:27.624873","level":"info","event":"Processed protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:27.625112","level":"info","event":"Processing protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:29.672209","level":"info","event":"Inserted data for protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:29.672357","level":"info","event":"Processed protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:29.672405","level":"info","event":"Processing protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:31.475870","level":"info","event":"Inserted data for protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:31.476046","level":"info","event":"Processed protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:31.476137","level":"info","event":"Processing protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:33.085238","level":"info","event":"Inserted data for protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:33.085554","level":"info","event":"Processed protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:33.085774","level":"info","event":"Processing protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:34.476494","level":"info","event":"Inserted data for protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:34.476637","level":"info","event":"Processed protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:34.476733","level":"info","event":"Processing protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:35.894623","level":"info","event":"Inserted data for protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:35.895159","level":"info","event":"Processed protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:35.895537","level":"info","event":"Processing protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:37.627056","level":"info","event":"Inserted data for protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:37.627346","level":"info","event":"Processed protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:37.627519","level":"info","event":"Processing protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:39.206644","level":"info","event":"Inserted data for protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:39.207373","level":"info","event":"Processed protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:39.207767","level":"info","event":"Processing protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:40.825727","level":"info","event":"Inserted data for protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:40.825808","level":"info","event":"Processed protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:40.825838","level":"info","event":"Processing protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:42.200707","level":"info","event":"Inserted data for protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:42.200938","level":"info","event":"Processed protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:42.201135","level":"info","event":"Processing protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:43.578863","level":"info","event":"Inserted data for protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:43.579355","level":"info","event":"Processed protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:43.579655","level":"info","event":"Processing protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:44.939630","level":"info","event":"Inserted data for protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:44.939956","level":"info","event":"Processed protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:44.940231","level":"info","event":"Processing protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:46.476889","level":"info","event":"Inserted data for protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:46.476950","level":"info","event":"Processed protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:46.476999","level":"info","event":"Processing protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:48.346520","level":"info","event":"Inserted data for protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:48.346812","level":"info","event":"Processed protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:48.347021","level":"info","event":"Processing protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:50.164299","level":"info","event":"Inserted data for protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:50.164639","level":"info","event":"Processed protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:50.164907","level":"info","event":"Processing protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:51.596704","level":"info","event":"Inserted data for protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:51.596970","level":"info","event":"Processed protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:51.597143","level":"info","event":"Processing protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:53.130672","level":"info","event":"Inserted data for protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:53.130769","level":"info","event":"Processed protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:53.130822","level":"info","event":"Processing protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:54.557170","level":"info","event":"Inserted data for protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:54.557964","level":"info","event":"Processed protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:54.558122","level":"info","event":"Processing protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:56.227167","level":"info","event":"Inserted data for protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:56.227572","level":"info","event":"Processed protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:56.227850","level":"info","event":"Processing protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:57.601825","level":"info","event":"Inserted data for protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:57.602180","level":"info","event":"Processed protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:57.602423","level":"info","event":"Processing protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:59.431751","level":"info","event":"Inserted data for protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:59.432183","level":"info","event":"Processed protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:09:59.432410","level":"info","event":"Processing protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:00.912936","level":"info","event":"Inserted data for protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:00.913120","level":"info","event":"Processed protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:00.913191","level":"info","event":"Processing protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:02.297656","level":"info","event":"Inserted data for protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:02.300804","level":"info","event":"Processed protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:02.301168","level":"info","event":"Processing protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:04.039938","level":"info","event":"Inserted data for protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:04.040533","level":"info","event":"Processed protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:04.040871","level":"info","event":"Processing protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:05.442852","level":"info","event":"Inserted data for protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:05.443292","level":"info","event":"Processed protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:05.443571","level":"info","event":"Processing protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:06.826498","level":"info","event":"Inserted data for protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:06.826627","level":"info","event":"Processed protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:06.826768","level":"info","event":"Processing protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:08.184280","level":"info","event":"Inserted data for protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:08.184816","level":"info","event":"Processed protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:08.185515","level":"info","event":"Processing protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:09.974056","level":"info","event":"Inserted data for protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:09.975278","level":"info","event":"Processed protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:09.975620","level":"info","event":"Processing protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:11.370535","level":"info","event":"Inserted data for protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:11.370852","level":"info","event":"Processed protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:11.370933","level":"info","event":"Processing protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:12.741052","level":"info","event":"Inserted data for protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:12.741191","level":"info","event":"Processed protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:12.741293","level":"info","event":"Processing protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:14.373064","level":"info","event":"Inserted data for protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:14.373387","level":"info","event":"Processed protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:14.380598","level":"info","event":"Processing protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:15.757392","level":"info","event":"Inserted data for protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:15.757699","level":"info","event":"Processed protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:15.757907","level":"info","event":"Processing protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:17.110586","level":"info","event":"Inserted data for protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:17.110702","level":"info","event":"Processed protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:17.110758","level":"info","event":"Processing protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:18.538820","level":"info","event":"Inserted data for protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:18.539963","level":"info","event":"Processed protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:18.540240","level":"info","event":"Processing protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:20.171914","level":"info","event":"Inserted data for protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:20.172314","level":"info","event":"Processed protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:20.172554","level":"info","event":"Processing protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:21.759817","level":"info","event":"Inserted data for protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:21.759902","level":"info","event":"Processed protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:21.759944","level":"info","event":"Processing protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:23.380325","level":"info","event":"Inserted data for protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:23.380708","level":"info","event":"Processed protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:23.380844","level":"info","event":"Processing protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:24.777553","level":"info","event":"Inserted data for protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:24.777632","level":"info","event":"Processed protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:24.777683","level":"info","event":"Processing protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:26.118849","level":"info","event":"Inserted data for protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:26.119216","level":"info","event":"Processed protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:26.119502","level":"info","event":"Processing protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:27.892715","level":"info","event":"Inserted data for protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:27.893296","level":"info","event":"Processed protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:27.893379","level":"info","event":"Processing protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:29.667638","level":"info","event":"Inserted data for protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:29.668124","level":"info","event":"Processed protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:29.668416","level":"info","event":"Processing protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:31.105296","level":"info","event":"Inserted data for protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:31.105443","level":"info","event":"Processed protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:31.105510","level":"info","event":"Processing protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:32.758842","level":"info","event":"Inserted data for protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:32.759193","level":"info","event":"Processed protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:32.759366","level":"info","event":"Processing protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:34.412955","level":"info","event":"Inserted data for protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:34.414270","level":"info","event":"Processed protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:34.414503","level":"info","event":"Processing protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:35.862062","level":"info","event":"Inserted data for protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:35.862224","level":"info","event":"Processed protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:35.862277","level":"info","event":"Processing protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:37.495891","level":"info","event":"Inserted data for protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:37.496668","level":"info","event":"Processed protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:37.497168","level":"info","event":"Processing protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:39.032949","level":"info","event":"Inserted data for protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:39.033152","level":"info","event":"Processed protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:39.033252","level":"info","event":"Processing protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:40.515680","level":"info","event":"Inserted data for protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:40.516056","level":"info","event":"Processed protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:40.516275","level":"info","event":"Processing protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:41.999257","level":"info","event":"Inserted data for protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:41.999489","level":"info","event":"Processed protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:41.999583","level":"info","event":"Processing protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:43.611717","level":"info","event":"Inserted data for protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:43.611815","level":"info","event":"Processed protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:43.611862","level":"info","event":"Processing protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:44.971681","level":"info","event":"Inserted data for protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:44.971850","level":"info","event":"Processed protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:44.971965","level":"info","event":"Processing protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:46.584686","level":"info","event":"Inserted data for protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:46.585040","level":"info","event":"Processed protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:46.585205","level":"info","event":"Processing protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:47.920785","level":"info","event":"Inserted data for protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:47.920882","level":"info","event":"Processed protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:47.920959","level":"info","event":"Processing protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:49.311071","level":"info","event":"Inserted data for protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:49.311411","level":"info","event":"Processed protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:49.311616","level":"info","event":"Processing protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:50.958225","level":"info","event":"Inserted data for protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:50.958692","level":"info","event":"Processed protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:50.959064","level":"info","event":"Processing protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:52.338573","level":"info","event":"Inserted data for protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:52.338657","level":"info","event":"Processed protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:52.338703","level":"info","event":"Processing protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:54.145881","level":"info","event":"Inserted data for protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:54.146308","level":"info","event":"Processed protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:54.146428","level":"info","event":"Processing protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:55.560130","level":"info","event":"Inserted data for protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:55.560624","level":"info","event":"Processed protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:55.560827","level":"info","event":"Processing protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:56.908338","level":"info","event":"Inserted data for protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:56.908420","level":"info","event":"Processed protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:56.908469","level":"info","event":"Processing protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:58.529663","level":"info","event":"Inserted data for protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:58.530065","level":"info","event":"Processed protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:58.530219","level":"info","event":"Processing protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:59.895888","level":"info","event":"Inserted data for protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:59.896388","level":"info","event":"Processed protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:10:59.896650","level":"info","event":"Processing protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:01.249499","level":"info","event":"Inserted data for protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:01.249962","level":"info","event":"Processed protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:01.250300","level":"info","event":"Processing protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:02.610554","level":"info","event":"Inserted data for protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:02.611214","level":"info","event":"Processed protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:02.611794","level":"info","event":"Processing protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:04.232475","level":"info","event":"Inserted data for protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:04.232831","level":"info","event":"Processed protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:04.233111","level":"info","event":"Processing protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:05.571170","level":"info","event":"Inserted data for protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:05.571913","level":"info","event":"Processed protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:05.572467","level":"info","event":"Processing protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:06.966354","level":"info","event":"Inserted data for protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:06.966773","level":"info","event":"Processed protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:06.966896","level":"info","event":"Processing protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:08.348356","level":"info","event":"Inserted data for protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:08.348663","level":"info","event":"Processed protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:08.348884","level":"info","event":"Processing protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:10.229918","level":"info","event":"Inserted data for protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:10.230227","level":"info","event":"Processed protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:10.230362","level":"info","event":"Processing protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:12.097190","level":"info","event":"Inserted data for protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:12.097383","level":"info","event":"Processed protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:12.097478","level":"info","event":"Processing protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:13.785168","level":"info","event":"Inserted data for protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:13.785595","level":"info","event":"Processed protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:13.785838","level":"info","event":"Processing protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:15.153101","level":"info","event":"Inserted data for protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:15.153373","level":"info","event":"Processed protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:15.153539","level":"info","event":"Processing protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:16.564743","level":"info","event":"Inserted data for protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:16.565173","level":"info","event":"Processed protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:16.565442","level":"info","event":"Processing protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:17.941953","level":"info","event":"Inserted data for protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:17.942382","level":"info","event":"Processed protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:17.942646","level":"info","event":"Processing protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:19.290653","level":"info","event":"Inserted data for protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:19.291264","level":"info","event":"Processed protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:19.291624","level":"info","event":"Processing protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:20.645784","level":"info","event":"Inserted data for protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:20.646128","level":"info","event":"Processed protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:20.646302","level":"info","event":"Processing protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:22.258934","level":"info","event":"Inserted data for protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:22.259391","level":"info","event":"Processed protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:22.259590","level":"info","event":"Processing protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:23.625628","level":"info","event":"Inserted data for protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:23.626256","level":"info","event":"Processed protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:23.627105","level":"info","event":"Processing protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:25.292817","level":"info","event":"Inserted data for protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:25.292899","level":"info","event":"Processed protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:25.292948","level":"info","event":"Processing protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:26.648965","level":"info","event":"Inserted data for protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:26.649719","level":"info","event":"Processed protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:26.651020","level":"info","event":"Processing protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:28.039632","level":"info","event":"Inserted data for protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:28.039755","level":"info","event":"Processed protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:28.039803","level":"info","event":"Processing protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:29.638598","level":"info","event":"Inserted data for protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:29.639043","level":"info","event":"Processed protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:29.639383","level":"info","event":"Processing protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:31.341445","level":"info","event":"Inserted data for protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:31.341826","level":"info","event":"Processed protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:31.342193","level":"info","event":"Processing protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:32.992171","level":"info","event":"Inserted data for protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:32.992630","level":"info","event":"Processed protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:32.992821","level":"info","event":"Processing protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:34.417188","level":"info","event":"Inserted data for protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:34.417575","level":"info","event":"Processed protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:34.417808","level":"info","event":"Processing protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:36.155552","level":"info","event":"Inserted data for protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:36.155948","level":"info","event":"Processed protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:36.156288","level":"info","event":"Processing protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:37.624498","level":"info","event":"Inserted data for protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:37.625238","level":"info","event":"Processed protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:37.625380","level":"info","event":"Processing protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:39.018410","level":"info","event":"Inserted data for protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:39.018714","level":"info","event":"Processed protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:39.018862","level":"info","event":"Processing protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:40.935972","level":"info","event":"Inserted data for protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:40.936439","level":"info","event":"Processed protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:40.936753","level":"info","event":"Processing protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:42.371906","level":"info","event":"Inserted data for protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:42.372411","level":"info","event":"Processed protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:42.372671","level":"info","event":"Processing protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:44.273422","level":"info","event":"Inserted data for protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:44.273748","level":"info","event":"Processed protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:44.273894","level":"info","event":"Processing protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:45.738530","level":"info","event":"Inserted data for protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:45.738777","level":"info","event":"Processed protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:45.738946","level":"info","event":"Processing protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:47.106796","level":"info","event":"Inserted data for protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:47.107313","level":"info","event":"Processed protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:47.107559","level":"info","event":"Processing protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:48.462057","level":"info","event":"Inserted data for protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:48.462521","level":"info","event":"Processed protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:48.462785","level":"info","event":"Processing protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:50.109732","level":"info","event":"Inserted data for protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:50.110141","level":"info","event":"Processed protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:50.110391","level":"info","event":"Processing protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:51.709926","level":"info","event":"Inserted data for protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:51.710205","level":"info","event":"Processed protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:51.710293","level":"info","event":"Processing protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:53.347710","level":"info","event":"Inserted data for protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:53.347789","level":"info","event":"Processed protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:53.347834","level":"info","event":"Processing protocol: 1000179","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:54.729849","level":"info","event":"Inserted data for protocol: 1000179","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:54.730577","level":"info","event":"Processed protocol: 1000179","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:54.731022","level":"info","event":"Processing protocol: 1000178","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:56.120932","level":"info","event":"Inserted data for protocol: 1000178","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:56.121338","level":"info","event":"Processed protocol: 1000178","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:56.121559","level":"info","event":"Processing protocol: 1000177","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:57.770670","level":"info","event":"Inserted data for protocol: 1000177","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:57.770935","level":"info","event":"Processed protocol: 1000177","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:57.771283","level":"info","event":"Processing protocol: 1000176","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:59.184657","level":"info","event":"Inserted data for protocol: 1000176","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:59.185034","level":"info","event":"Processed protocol: 1000176","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:11:59.185247","level":"info","event":"Processing protocol: 1000175","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:00.592755","level":"info","event":"Inserted data for protocol: 1000175","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:00.592820","level":"info","event":"Processed protocol: 1000175","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:00.592857","level":"info","event":"Processing protocol: 1000174","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:02.669483","level":"info","event":"Inserted data for protocol: 1000174","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:02.669837","level":"info","event":"Processed protocol: 1000174","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:02.670122","level":"info","event":"Processing protocol: 1000173","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:04.375512","level":"info","event":"Inserted data for protocol: 1000173","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:04.375958","level":"info","event":"Processed protocol: 1000173","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:04.376293","level":"info","event":"Processing protocol: 1000172","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:06.072226","level":"info","event":"Inserted data for protocol: 1000172","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:06.072852","level":"info","event":"Processed protocol: 1000172","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:06.073461","level":"info","event":"Processing protocol: 1000171","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:07.690808","level":"info","event":"Inserted data for protocol: 1000171","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:07.691126","level":"info","event":"Processed protocol: 1000171","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:07.691268","level":"info","event":"Processing protocol: 1000170","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:09.403206","level":"info","event":"Inserted data for protocol: 1000170","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:09.403544","level":"info","event":"Processed protocol: 1000170","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:09.403874","level":"info","event":"Processing protocol: 1000169","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:10.769651","level":"info","event":"Inserted data for protocol: 1000169","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:10.770011","level":"info","event":"Processed protocol: 1000169","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:10.770149","level":"info","event":"Processing protocol: 1000168","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:12.323820","level":"info","event":"Inserted data for protocol: 1000168","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:12.324647","level":"info","event":"Processed protocol: 1000168","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:12.325045","level":"info","event":"Processing protocol: 1000167","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:13.729930","level":"info","event":"Inserted data for protocol: 1000167","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:13.730160","level":"info","event":"Processed protocol: 1000167","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:13.730254","level":"info","event":"Processing protocol: 1000166","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:15.318930","level":"info","event":"Inserted data for protocol: 1000166","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:15.319364","level":"info","event":"Processed protocol: 1000166","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:15.319608","level":"info","event":"Processing protocol: 1000165","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:16.831121","level":"info","event":"Inserted data for protocol: 1000165","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:16.837154","level":"info","event":"Processed protocol: 1000165","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:16.839520","level":"info","event":"Processing protocol: 1000164","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:18.271120","level":"info","event":"Inserted data for protocol: 1000164","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:18.272908","level":"info","event":"Processed protocol: 1000164","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:18.273378","level":"info","event":"Processing protocol: 1000163","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:19.635252","level":"info","event":"Inserted data for protocol: 1000163","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:19.635467","level":"info","event":"Processed protocol: 1000163","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:19.635545","level":"info","event":"Processing protocol: 1000162","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:21.079425","level":"info","event":"Inserted data for protocol: 1000162","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:21.079704","level":"info","event":"Processed protocol: 1000162","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:21.080113","level":"info","event":"Processing protocol: 1000161","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:22.536447","level":"info","event":"Inserted data for protocol: 1000161","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:22.536816","level":"info","event":"Processed protocol: 1000161","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:22.537084","level":"info","event":"Processing protocol: 1000160","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:24.069811","level":"info","event":"Inserted data for protocol: 1000160","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:24.070090","level":"info","event":"Processed protocol: 1000160","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:24.070169","level":"info","event":"Processing protocol: 1000159","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:25.622169","level":"info","event":"Inserted data for protocol: 1000159","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:25.622785","level":"info","event":"Processed protocol: 1000159","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:25.623547","level":"info","event":"Processing protocol: 1000158","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:27.139646","level":"info","event":"Inserted data for protocol: 1000158","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:27.139896","level":"info","event":"Processed protocol: 1000158","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:27.140152","level":"info","event":"Processing protocol: 1000157","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:28.780660","level":"info","event":"Inserted data for protocol: 1000157","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:28.781393","level":"info","event":"Processed protocol: 1000157","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:28.781785","level":"info","event":"Processing protocol: 1000156","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:30.416285","level":"info","event":"Inserted data for protocol: 1000156","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:30.416641","level":"info","event":"Processed protocol: 1000156","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:30.416920","level":"info","event":"Processing protocol: 1000155","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:31.813695","level":"info","event":"Inserted data for protocol: 1000155","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:31.813780","level":"info","event":"Processed protocol: 1000155","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:31.813821","level":"info","event":"Processing protocol: 1000154","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:33.194689","level":"info","event":"Inserted data for protocol: 1000154","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:33.195005","level":"info","event":"Processed protocol: 1000154","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:33.195082","level":"info","event":"Processing protocol: 1000153","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:34.826606","level":"info","event":"Inserted data for protocol: 1000153","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:34.827041","level":"info","event":"Processed protocol: 1000153","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:34.827122","level":"info","event":"Processing protocol: 1000152","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:36.460083","level":"info","event":"Inserted data for protocol: 1000152","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:36.460489","level":"info","event":"Processed protocol: 1000152","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:36.460751","level":"info","event":"Processing protocol: 1000151","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:37.897772","level":"info","event":"Inserted data for protocol: 1000151","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:37.897942","level":"info","event":"Processed protocol: 1000151","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:37.898054","level":"info","event":"Processing protocol: 1000150","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:39.407452","level":"info","event":"Inserted data for protocol: 1000150","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:39.407834","level":"info","event":"Processed protocol: 1000150","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:39.407969","level":"info","event":"Processing protocol: 1000149","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:41.056914","level":"info","event":"Inserted data for protocol: 1000149","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:41.057899","level":"info","event":"Processed protocol: 1000149","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:41.058076","level":"info","event":"Processing protocol: 1000148","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:42.456903","level":"info","event":"Inserted data for protocol: 1000148","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:42.459754","level":"info","event":"Processed protocol: 1000148","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:42.459953","level":"info","event":"Processing protocol: 1000147","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:43.872694","level":"info","event":"Inserted data for protocol: 1000147","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:43.872952","level":"info","event":"Processed protocol: 1000147","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:43.873130","level":"info","event":"Processing protocol: 1000146","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:45.713740","level":"info","event":"Inserted data for protocol: 1000146","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:45.714901","level":"info","event":"Processed protocol: 1000146","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:45.715190","level":"info","event":"Processing protocol: 1000145","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:47.129109","level":"info","event":"Inserted data for protocol: 1000145","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:47.129637","level":"info","event":"Processed protocol: 1000145","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:47.130163","level":"info","event":"Processing protocol: 1000144","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:48.470778","level":"info","event":"Inserted data for protocol: 1000144","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:48.471189","level":"info","event":"Processed protocol: 1000144","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:48.471308","level":"info","event":"Processing protocol: 1000143","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:49.866441","level":"info","event":"Inserted data for protocol: 1000143","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:49.866714","level":"info","event":"Processed protocol: 1000143","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:49.866887","level":"info","event":"Processing protocol: 1000142","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:51.265334","level":"info","event":"Inserted data for protocol: 1000142","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:51.266045","level":"info","event":"Processed protocol: 1000142","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:51.266428","level":"info","event":"Processing protocol: 1000141","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:52.652097","level":"info","event":"Inserted data for protocol: 1000141","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:52.652521","level":"info","event":"Processed protocol: 1000141","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:52.652743","level":"info","event":"Processing protocol: 1000140","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:54.394225","level":"info","event":"Inserted data for protocol: 1000140","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:54.395950","level":"info","event":"Processed protocol: 1000140","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:54.408655","level":"info","event":"Processing protocol: 1000139","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:55.988719","level":"info","event":"Inserted data for protocol: 1000139","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:55.989302","level":"info","event":"Processed protocol: 1000139","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:55.989470","level":"info","event":"Processing protocol: 1000138","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:57.402520","level":"info","event":"Inserted data for protocol: 1000138","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:57.403172","level":"info","event":"Processed protocol: 1000138","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:57.403426","level":"info","event":"Processing protocol: 1000137","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:59.085026","level":"info","event":"Inserted data for protocol: 1000137","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:59.085677","level":"info","event":"Processed protocol: 1000137","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:12:59.085775","level":"info","event":"Processing protocol: 1000136","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:00.446108","level":"info","event":"Inserted data for protocol: 1000136","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:00.446227","level":"info","event":"Processed protocol: 1000136","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:00.446309","level":"info","event":"Processing protocol: 1000135","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:02.105340","level":"info","event":"Inserted data for protocol: 1000135","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:02.105927","level":"info","event":"Processed protocol: 1000135","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:02.106265","level":"info","event":"Processing protocol: 1000134","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:03.753353","level":"info","event":"Inserted data for protocol: 1000134","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:03.754154","level":"info","event":"Processed protocol: 1000134","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:03.754535","level":"info","event":"Processing protocol: 1000133","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:05.437197","level":"info","event":"Inserted data for protocol: 1000133","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:05.437300","level":"info","event":"Processed protocol: 1000133","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:05.437344","level":"info","event":"Processing protocol: 1000132","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:07.051445","level":"info","event":"Inserted data for protocol: 1000132","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:07.051923","level":"info","event":"Processed protocol: 1000132","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:07.052210","level":"info","event":"Processing protocol: 1000131","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:08.419789","level":"info","event":"Inserted data for protocol: 1000131","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:08.420063","level":"info","event":"Processed protocol: 1000131","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:08.420158","level":"info","event":"Processing protocol: 1000130","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:10.048401","level":"info","event":"Inserted data for protocol: 1000130","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:10.048489","level":"info","event":"Processed protocol: 1000130","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:10.048544","level":"info","event":"Processing protocol: 1000129","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:11.613795","level":"info","event":"Inserted data for protocol: 1000129","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:11.614300","level":"info","event":"Processed protocol: 1000129","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:11.614725","level":"info","event":"Processing protocol: 1000128","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:13.213862","level":"info","event":"Inserted data for protocol: 1000128","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:13.214616","level":"info","event":"Processed protocol: 1000128","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:13.215057","level":"info","event":"Processing protocol: 1000127","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:14.626260","level":"info","event":"Inserted data for protocol: 1000127","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:14.626897","level":"info","event":"Processed protocol: 1000127","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:14.628338","level":"info","event":"Processing protocol: 1000126","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:16.494416","level":"info","event":"Inserted data for protocol: 1000126","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:16.494739","level":"info","event":"Processed protocol: 1000126","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:16.494975","level":"info","event":"Processing protocol: 1000125","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:17.868683","level":"info","event":"Inserted data for protocol: 1000125","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:17.868884","level":"info","event":"Processed protocol: 1000125","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:17.869038","level":"info","event":"Processing protocol: 1000124","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:19.227208","level":"info","event":"Inserted data for protocol: 1000124","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:19.227529","level":"info","event":"Processed protocol: 1000124","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:19.227613","level":"info","event":"Processing protocol: 1000123","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:20.866765","level":"info","event":"Inserted data for protocol: 1000123","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:20.866900","level":"info","event":"Processed protocol: 1000123","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:20.866997","level":"info","event":"Processing protocol: 1000122","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:22.309841","level":"info","event":"Inserted data for protocol: 1000122","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:22.310702","level":"info","event":"Processed protocol: 1000122","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:22.311115","level":"info","event":"Processing protocol: 1000121","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:23.767507","level":"info","event":"Inserted data for protocol: 1000121","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:23.767758","level":"info","event":"Processed protocol: 1000121","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:23.767871","level":"info","event":"Processing protocol: 1000120","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:25.221318","level":"info","event":"Inserted data for protocol: 1000120","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:25.221642","level":"info","event":"Processed protocol: 1000120","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:25.221797","level":"info","event":"Processing protocol: 1000119","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:26.636879","level":"info","event":"Inserted data for protocol: 1000119","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:26.637014","level":"info","event":"Processed protocol: 1000119","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:26.637053","level":"info","event":"Processing protocol: 1000118","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:28.021236","level":"info","event":"Inserted data for protocol: 1000118","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:28.022049","level":"info","event":"Processed protocol: 1000118","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:28.022215","level":"info","event":"Processing protocol: 1000117","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:29.392942","level":"info","event":"Inserted data for protocol: 1000117","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:29.393362","level":"info","event":"Processed protocol: 1000117","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:29.393888","level":"info","event":"Processing protocol: 1000116","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:30.816347","level":"info","event":"Inserted data for protocol: 1000116","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:30.816600","level":"info","event":"Processed protocol: 1000116","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:30.816719","level":"info","event":"Processing protocol: 1000115","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:32.472834","level":"info","event":"Inserted data for protocol: 1000115","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:32.473106","level":"info","event":"Processed protocol: 1000115","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:32.473214","level":"info","event":"Processing protocol: 1000114","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:34.156435","level":"info","event":"Inserted data for protocol: 1000114","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:34.157198","level":"info","event":"Processed protocol: 1000114","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:34.157515","level":"info","event":"Processing protocol: 1000113","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:35.567648","level":"info","event":"Inserted data for protocol: 1000113","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:35.567841","level":"info","event":"Processed protocol: 1000113","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:35.567928","level":"info","event":"Processing protocol: 1000112","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:36.927778","level":"info","event":"Inserted data for protocol: 1000112","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:36.927861","level":"info","event":"Processed protocol: 1000112","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:36.927902","level":"info","event":"Processing protocol: 1000111","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:38.371936","level":"info","event":"Inserted data for protocol: 1000111","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:38.372013","level":"info","event":"Processed protocol: 1000111","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:38.372057","level":"info","event":"Processing protocol: 1000110","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:40.257029","level":"info","event":"Inserted data for protocol: 1000110","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:40.258434","level":"info","event":"Processed protocol: 1000110","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:40.258532","level":"info","event":"Processing protocol: 1000109","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:41.656154","level":"info","event":"Inserted data for protocol: 1000109","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:41.658576","level":"info","event":"Processed protocol: 1000109","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:41.658756","level":"info","event":"Processing protocol: 1000108","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:43.061522","level":"info","event":"Inserted data for protocol: 1000108","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:43.061659","level":"info","event":"Processed protocol: 1000108","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:43.061723","level":"info","event":"Processing protocol: 1000107","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:44.479942","level":"info","event":"Inserted data for protocol: 1000107","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:44.480280","level":"info","event":"Processed protocol: 1000107","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:44.480424","level":"info","event":"Processing protocol: 1000106","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:46.202479","level":"info","event":"Inserted data for protocol: 1000106","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:46.202930","level":"info","event":"Processed protocol: 1000106","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:46.203002","level":"info","event":"Processing protocol: 1000105","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:48.112054","level":"info","event":"Inserted data for protocol: 1000105","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:48.112476","level":"info","event":"Processed protocol: 1000105","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:48.112846","level":"info","event":"Processing protocol: 1000104","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:49.475676","level":"info","event":"Inserted data for protocol: 1000104","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:49.475937","level":"info","event":"Processed protocol: 1000104","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:49.476227","level":"info","event":"Processing protocol: 1000103","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:50.974671","level":"info","event":"Inserted data for protocol: 1000103","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:50.975200","level":"info","event":"Processed protocol: 1000103","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:50.975750","level":"info","event":"Processing protocol: 1000102","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:52.849035","level":"info","event":"Inserted data for protocol: 1000102","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:52.849115","level":"info","event":"Processed protocol: 1000102","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:52.849168","level":"info","event":"Processing protocol: 1000101","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:54.455445","level":"info","event":"Inserted data for protocol: 1000101","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:54.457207","level":"info","event":"Processed protocol: 1000101","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:54.519240","level":"info","event":"Processing protocol: 1000100","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:56.219954","level":"info","event":"Inserted data for protocol: 1000100","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:56.221544","level":"info","event":"Processed protocol: 1000100","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:56.221842","level":"info","event":"Processing protocol: 1000099","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:57.887576","level":"info","event":"Inserted data for protocol: 1000099","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:57.888086","level":"info","event":"Processed protocol: 1000099","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:57.888403","level":"info","event":"Processing protocol: 1000098","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:59.521517","level":"info","event":"Inserted data for protocol: 1000098","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:59.521814","level":"info","event":"Processed protocol: 1000098","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:13:59.521890","level":"info","event":"Processing protocol: 1000097","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:00.902414","level":"info","event":"Inserted data for protocol: 1000097","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:00.902911","level":"info","event":"Processed protocol: 1000097","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:00.903297","level":"info","event":"Processing protocol: 1000096","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:02.348382","level":"info","event":"Inserted data for protocol: 1000096","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:02.348701","level":"info","event":"Processed protocol: 1000096","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:02.349594","level":"info","event":"Processing protocol: 1000095","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:04.008669","level":"info","event":"Inserted data for protocol: 1000095","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:04.009103","level":"info","event":"Processed protocol: 1000095","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:04.009321","level":"info","event":"Processing protocol: 1000094","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:05.701506","level":"info","event":"Inserted data for protocol: 1000094","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:05.701626","level":"info","event":"Processed protocol: 1000094","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:05.701698","level":"info","event":"Processing protocol: 1000093","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:07.031416","level":"info","event":"Inserted data for protocol: 1000093","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:07.031661","level":"info","event":"Processed protocol: 1000093","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:07.031899","level":"info","event":"Processing protocol: 1000092","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:08.438268","level":"info","event":"Inserted data for protocol: 1000092","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:08.438631","level":"info","event":"Processed protocol: 1000092","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:08.438874","level":"info","event":"Processing protocol: 1000091","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:10.112256","level":"info","event":"Inserted data for protocol: 1000091","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:10.112439","level":"info","event":"Processed protocol: 1000091","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:10.112505","level":"info","event":"Processing protocol: 1000090","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:11.791904","level":"info","event":"Inserted data for protocol: 1000090","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:11.792379","level":"info","event":"Processed protocol: 1000090","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:11.793135","level":"info","event":"Processing protocol: 1000089","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:13.171153","level":"info","event":"Inserted data for protocol: 1000089","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:13.171406","level":"info","event":"Processed protocol: 1000089","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:13.171562","level":"info","event":"Processing protocol: 1000088","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:15.095647","level":"info","event":"Inserted data for protocol: 1000088","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:15.096073","level":"info","event":"Processed protocol: 1000088","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:15.096352","level":"info","event":"Processing protocol: 1000087","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:17.056034","level":"info","event":"Inserted data for protocol: 1000087","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:17.056137","level":"info","event":"Processed protocol: 1000087","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:17.056208","level":"info","event":"Processing protocol: 1000086","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:18.408261","level":"info","event":"Inserted data for protocol: 1000086","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:18.408831","level":"info","event":"Processed protocol: 1000086","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:18.409070","level":"info","event":"Processing protocol: 1000085","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:19.796934","level":"info","event":"Inserted data for protocol: 1000085","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:19.797759","level":"info","event":"Processed protocol: 1000085","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:19.798082","level":"info","event":"Processing protocol: 1000084","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:21.462855","level":"info","event":"Inserted data for protocol: 1000084","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:21.463274","level":"info","event":"Processed protocol: 1000084","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:21.463549","level":"info","event":"Processing protocol: 1000083","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:22.858350","level":"info","event":"Inserted data for protocol: 1000083","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:22.858547","level":"info","event":"Processed protocol: 1000083","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:22.858651","level":"info","event":"Processing protocol: 1000082","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:24.489393","level":"info","event":"Inserted data for protocol: 1000082","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:24.489544","level":"info","event":"Processed protocol: 1000082","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:24.489639","level":"info","event":"Processing protocol: 1000081","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:26.014074","level":"info","event":"Inserted data for protocol: 1000081","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:26.014479","level":"info","event":"Processed protocol: 1000081","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:26.014774","level":"info","event":"Processing protocol: 1000080","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:27.433471","level":"info","event":"Inserted data for protocol: 1000080","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:27.433704","level":"info","event":"Processed protocol: 1000080","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:27.433825","level":"info","event":"Processing protocol: 1000079","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:29.036316","level":"info","event":"Inserted data for protocol: 1000079","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:29.036810","level":"info","event":"Processed protocol: 1000079","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:29.037297","level":"info","event":"Processing protocol: 1000078","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:30.446080","level":"info","event":"Inserted data for protocol: 1000078","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:30.446530","level":"info","event":"Processed protocol: 1000078","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:30.446798","level":"info","event":"Processing protocol: 1000077","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:31.883421","level":"info","event":"Inserted data for protocol: 1000077","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:31.883638","level":"info","event":"Processed protocol: 1000077","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:31.883734","level":"info","event":"Processing protocol: 1000076","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:33.301107","level":"info","event":"Inserted data for protocol: 1000076","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:33.301551","level":"info","event":"Processed protocol: 1000076","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:33.302081","level":"info","event":"Processing protocol: 1000075","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:34.708504","level":"info","event":"Inserted data for protocol: 1000075","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:34.708744","level":"info","event":"Processed protocol: 1000075","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:34.708929","level":"info","event":"Processing protocol: 1000074","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:36.350741","level":"info","event":"Inserted data for protocol: 1000074","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:36.351110","level":"info","event":"Processed protocol: 1000074","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:36.351345","level":"info","event":"Processing protocol: 1000073","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:38.215082","level":"info","event":"Inserted data for protocol: 1000073","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:38.215413","level":"info","event":"Processed protocol: 1000073","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:38.215628","level":"info","event":"Processing protocol: 1000072","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:39.717457","level":"info","event":"Inserted data for protocol: 1000072","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:39.717793","level":"info","event":"Processed protocol: 1000072","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:39.718090","level":"info","event":"Processing protocol: 1000071","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:41.183102","level":"info","event":"Inserted data for protocol: 1000071","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:41.183177","level":"info","event":"Processed protocol: 1000071","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:41.183214","level":"info","event":"Processing protocol: 1000070","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:42.583210","level":"info","event":"Inserted data for protocol: 1000070","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:42.583574","level":"info","event":"Processed protocol: 1000070","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:42.583923","level":"info","event":"Processing protocol: 1000069","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:44.315235","level":"info","event":"Inserted data for protocol: 1000069","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:44.315612","level":"info","event":"Processed protocol: 1000069","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:44.317287","level":"info","event":"Processing protocol: 1000068","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:45.757430","level":"info","event":"Inserted data for protocol: 1000068","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:45.757754","level":"info","event":"Processed protocol: 1000068","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:45.757973","level":"info","event":"Processing protocol: 1000067","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:47.182686","level":"info","event":"Inserted data for protocol: 1000067","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:47.183018","level":"info","event":"Processed protocol: 1000067","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:47.184543","level":"info","event":"Processing protocol: 1000066","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:48.629778","level":"info","event":"Inserted data for protocol: 1000066","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:48.629846","level":"info","event":"Processed protocol: 1000066","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:48.629882","level":"info","event":"Processing protocol: 1000065","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:51.145807","level":"info","event":"Inserted data for protocol: 1000065","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:51.146192","level":"info","event":"Processed protocol: 1000065","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:51.146445","level":"info","event":"Processing protocol: 1000064","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:52.526588","level":"info","event":"Inserted data for protocol: 1000064","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:52.527039","level":"info","event":"Processed protocol: 1000064","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:52.527255","level":"info","event":"Processing protocol: 1000063","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:54.387130","level":"info","event":"Inserted data for protocol: 1000063","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:54.389127","level":"info","event":"Processed protocol: 1000063","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:54.392702","level":"info","event":"Processing protocol: 1000062","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:56.083740","level":"info","event":"Inserted data for protocol: 1000062","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:56.084544","level":"info","event":"Processed protocol: 1000062","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:56.084931","level":"info","event":"Processing protocol: 1000061","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:57.713952","level":"info","event":"Inserted data for protocol: 1000061","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:57.714416","level":"info","event":"Processed protocol: 1000061","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:57.714683","level":"info","event":"Processing protocol: 1000060","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:59.389408","level":"info","event":"Inserted data for protocol: 1000060","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:59.389742","level":"info","event":"Processed protocol: 1000060","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:14:59.390033","level":"info","event":"Processing protocol: 1000059","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:01.052555","level":"info","event":"Inserted data for protocol: 1000059","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:01.052824","level":"info","event":"Processed protocol: 1000059","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:01.053056","level":"info","event":"Processing protocol: 1000058","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:02.459477","level":"info","event":"Inserted data for protocol: 1000058","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:02.459946","level":"info","event":"Processed protocol: 1000058","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:02.460415","level":"info","event":"Processing protocol: 1000057","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:03.885080","level":"info","event":"Inserted data for protocol: 1000057","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:03.885183","level":"info","event":"Processed protocol: 1000057","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:03.885258","level":"info","event":"Processing protocol: 1000056","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:05.285375","level":"info","event":"Inserted data for protocol: 1000056","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:05.285614","level":"info","event":"Processed protocol: 1000056","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:05.285823","level":"info","event":"Processing protocol: 1000055","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:06.901590","level":"info","event":"Inserted data for protocol: 1000055","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:06.901721","level":"info","event":"Processed protocol: 1000055","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:06.901833","level":"info","event":"Processing protocol: 1000054","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:08.495892","level":"info","event":"Inserted data for protocol: 1000054","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:08.496283","level":"info","event":"Processed protocol: 1000054","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:08.496417","level":"info","event":"Processing protocol: 1000053","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:10.112264","level":"info","event":"Inserted data for protocol: 1000053","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:10.112550","level":"info","event":"Processed protocol: 1000053","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:10.112783","level":"info","event":"Processing protocol: 1000052","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:11.553631","level":"info","event":"Inserted data for protocol: 1000052","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:11.553800","level":"info","event":"Processed protocol: 1000052","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:11.554314","level":"info","event":"Processing protocol: 1000051","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:12.967600","level":"info","event":"Inserted data for protocol: 1000051","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:12.967669","level":"info","event":"Processed protocol: 1000051","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:12.967705","level":"info","event":"Processing protocol: 1000050","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:14.396362","level":"info","event":"Inserted data for protocol: 1000050","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:14.396461","level":"info","event":"Processed protocol: 1000050","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:14.396530","level":"info","event":"Processing protocol: 1000049","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:16.029453","level":"info","event":"Inserted data for protocol: 1000049","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:16.029780","level":"info","event":"Processed protocol: 1000049","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:16.030043","level":"info","event":"Processing protocol: 1000048","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:17.382501","level":"info","event":"Inserted data for protocol: 1000048","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:17.382844","level":"info","event":"Processed protocol: 1000048","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:17.383094","level":"info","event":"Processing protocol: 1000047","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:18.748909","level":"info","event":"Inserted data for protocol: 1000047","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:18.749354","level":"info","event":"Processed protocol: 1000047","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:18.749607","level":"info","event":"Processing protocol: 1000046","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:20.115578","level":"info","event":"Inserted data for protocol: 1000046","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:20.116013","level":"info","event":"Processed protocol: 1000046","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:20.116187","level":"info","event":"Processing protocol: 1000045","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:21.501254","level":"info","event":"Inserted data for protocol: 1000045","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:21.501616","level":"info","event":"Processed protocol: 1000045","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:21.501843","level":"info","event":"Processing protocol: 1000044","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:23.123824","level":"info","event":"Inserted data for protocol: 1000044","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:23.124122","level":"info","event":"Processed protocol: 1000044","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:23.124300","level":"info","event":"Processing protocol: 1000043","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:24.480199","level":"info","event":"Inserted data for protocol: 1000043","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:24.480280","level":"info","event":"Processed protocol: 1000043","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:24.480333","level":"info","event":"Processing protocol: 1000042","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:26.094670","level":"info","event":"Inserted data for protocol: 1000042","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:26.095547","level":"info","event":"Processed protocol: 1000042","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:26.095958","level":"info","event":"Processing protocol: 1000041","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:27.735232","level":"info","event":"Inserted data for protocol: 1000041","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:27.735666","level":"info","event":"Processed protocol: 1000041","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:27.735932","level":"info","event":"Processing protocol: 1000040","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:29.112848","level":"info","event":"Inserted data for protocol: 1000040","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:29.113360","level":"info","event":"Processed protocol: 1000040","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:29.113622","level":"info","event":"Processing protocol: 1000039","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:30.467911","level":"info","event":"Inserted data for protocol: 1000039","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:30.468366","level":"info","event":"Processed protocol: 1000039","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:30.468750","level":"info","event":"Processing protocol: 1000038","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:31.879879","level":"info","event":"Inserted data for protocol: 1000038","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:31.880349","level":"info","event":"Processed protocol: 1000038","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:31.880622","level":"info","event":"Processing protocol: 1000037","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:33.471184","level":"info","event":"Inserted data for protocol: 1000037","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:33.471336","level":"info","event":"Processed protocol: 1000037","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:33.471443","level":"info","event":"Processing protocol: 1000036","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:34.865688","level":"info","event":"Inserted data for protocol: 1000036","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:34.866167","level":"info","event":"Processed protocol: 1000036","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:34.866383","level":"info","event":"Processing protocol: 1000035","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:36.280595","level":"info","event":"Inserted data for protocol: 1000035","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:36.280801","level":"info","event":"Processed protocol: 1000035","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:36.281013","level":"info","event":"Processing protocol: 1000034","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:37.880185","level":"info","event":"Inserted data for protocol: 1000034","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:37.880492","level":"info","event":"Processed protocol: 1000034","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:37.880708","level":"info","event":"Processing protocol: 1000033","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:39.492786","level":"info","event":"Inserted data for protocol: 1000033","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:39.494017","level":"info","event":"Processed protocol: 1000033","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:39.494418","level":"info","event":"Processing protocol: 1000032","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:41.137042","level":"info","event":"Inserted data for protocol: 1000032","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:41.137327","level":"info","event":"Processed protocol: 1000032","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:41.137513","level":"info","event":"Processing protocol: 1000031","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:42.768066","level":"info","event":"Inserted data for protocol: 1000031","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:42.768448","level":"info","event":"Processed protocol: 1000031","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:42.768773","level":"info","event":"Processing protocol: 1000030","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:44.435185","level":"info","event":"Inserted data for protocol: 1000030","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:44.435335","level":"info","event":"Processed protocol: 1000030","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:44.435460","level":"info","event":"Processing protocol: 1000029","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:45.795019","level":"info","event":"Inserted data for protocol: 1000029","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:45.795086","level":"info","event":"Processed protocol: 1000029","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:45.795121","level":"info","event":"Processing protocol: 1000028","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:47.443275","level":"info","event":"Inserted data for protocol: 1000028","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:47.443544","level":"info","event":"Processed protocol: 1000028","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:47.443756","level":"info","event":"Processing protocol: 1000027","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:49.091670","level":"info","event":"Inserted data for protocol: 1000027","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:49.092079","level":"info","event":"Processed protocol: 1000027","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:49.092330","level":"info","event":"Processing protocol: 1000026","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:50.719655","level":"info","event":"Inserted data for protocol: 1000026","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:50.720049","level":"info","event":"Processed protocol: 1000026","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:50.720351","level":"info","event":"Processing protocol: 1000025","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:52.149672","level":"info","event":"Inserted data for protocol: 1000025","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:52.150077","level":"info","event":"Processed protocol: 1000025","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:52.150303","level":"info","event":"Processing protocol: 1000024","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:53.529451","level":"info","event":"Inserted data for protocol: 1000024","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:53.529555","level":"info","event":"Processed protocol: 1000024","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:53.529611","level":"info","event":"Processing protocol: 1000023","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:54.914794","level":"info","event":"Inserted data for protocol: 1000023","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:54.915097","level":"info","event":"Processed protocol: 1000023","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:54.915208","level":"info","event":"Processing protocol: 1000022","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:56.290242","level":"info","event":"Inserted data for protocol: 1000022","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:56.290633","level":"info","event":"Processed protocol: 1000022","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:56.290867","level":"info","event":"Processing protocol: 1000021","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:57.656740","level":"info","event":"Inserted data for protocol: 1000021","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:57.657455","level":"info","event":"Processed protocol: 1000021","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:57.657969","level":"info","event":"Processing protocol: 1000020","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:59.291877","level":"info","event":"Inserted data for protocol: 1000020","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:59.292520","level":"info","event":"Processed protocol: 1000020","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:15:59.292773","level":"info","event":"Processing protocol: 1000019","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:00.653447","level":"info","event":"Inserted data for protocol: 1000019","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:00.653668","level":"info","event":"Processed protocol: 1000019","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:00.653800","level":"info","event":"Processing protocol: 1000018","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:02.037117","level":"info","event":"Inserted data for protocol: 1000018","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:02.037472","level":"info","event":"Processed protocol: 1000018","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:02.037675","level":"info","event":"Processing protocol: 1000017","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:03.436680","level":"info","event":"Inserted data for protocol: 1000017","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:03.437120","level":"info","event":"Processed protocol: 1000017","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:03.437308","level":"info","event":"Processing protocol: 1000016","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:05.069350","level":"info","event":"Inserted data for protocol: 1000016","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:05.069827","level":"info","event":"Processed protocol: 1000016","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:05.070348","level":"info","event":"Processing protocol: 1000015","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:06.427565","level":"info","event":"Inserted data for protocol: 1000015","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:06.427731","level":"info","event":"Processed protocol: 1000015","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:06.427827","level":"info","event":"Processing protocol: 1000014","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:07.818520","level":"info","event":"Inserted data for protocol: 1000014","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:07.818607","level":"info","event":"Processed protocol: 1000014","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:07.818661","level":"info","event":"Processing protocol: 1000013","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:09.227915","level":"info","event":"Inserted data for protocol: 1000013","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:09.228071","level":"info","event":"Processed protocol: 1000013","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:09.228116","level":"info","event":"Processing protocol: 1000012","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:10.852965","level":"info","event":"Inserted data for protocol: 1000012","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:10.853081","level":"info","event":"Processed protocol: 1000012","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:10.853136","level":"info","event":"Processing protocol: 1000011","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:12.273784","level":"info","event":"Inserted data for protocol: 1000011","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:12.274356","level":"info","event":"Processed protocol: 1000011","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:12.274701","level":"info","event":"Processing protocol: 1000010","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:13.668883","level":"info","event":"Inserted data for protocol: 1000010","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:13.669163","level":"info","event":"Processed protocol: 1000010","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:13.669314","level":"info","event":"Processing protocol: 1000009","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:15.025437","level":"info","event":"Inserted data for protocol: 1000009","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:15.025564","level":"info","event":"Processed protocol: 1000009","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:15.025663","level":"info","event":"Processing protocol: 1000008","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:16.422762","level":"info","event":"Inserted data for protocol: 1000008","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:16.422922","level":"info","event":"Processed protocol: 1000008","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:16.423069","level":"info","event":"Processing protocol: 1000007","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:17.758028","level":"info","event":"Inserted data for protocol: 1000007","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:17.758088","level":"info","event":"Processed protocol: 1000007","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:17.758121","level":"info","event":"Processing protocol: 1000006","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:19.104854","level":"info","event":"Inserted data for protocol: 1000006","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:19.105271","level":"info","event":"Processed protocol: 1000006","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:19.105589","level":"info","event":"Processing protocol: 1000005","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:20.472270","level":"info","event":"Inserted data for protocol: 1000005","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:20.472666","level":"info","event":"Processed protocol: 1000005","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:20.472933","level":"info","event":"Processing protocol: 1000004","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:21.853249","level":"info","event":"Inserted data for protocol: 1000004","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:21.853599","level":"info","event":"Processed protocol: 1000004","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:21.853836","level":"info","event":"Processing protocol: 1000003","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:23.197391","level":"info","event":"Inserted data for protocol: 1000003","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:23.197775","level":"info","event":"Processed protocol: 1000003","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:23.198187","level":"info","event":"Processing protocol: 1000002","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:24.554659","level":"info","event":"Inserted data for protocol: 1000002","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:24.555083","level":"info","event":"Processed protocol: 1000002","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:24.555366","level":"info","event":"Processing protocol: 1000001","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:25.924409","level":"info","event":"Inserted data for protocol: 1000001","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:25.924884","level":"info","event":"Processed protocol: 1000001","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:25.925155","level":"info","event":"Processing protocol: 1000000","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:27.285324","level":"info","event":"Inserted data for protocol: 1000000","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:27.285712","level":"info","event":"Processed protocol: 1000000","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:16:27.337656","level":"info","event":"Parsed ELN data","logger":"root"}
{"timestamp":"2025-09-08T07:16:27.346246","level":"info","event":"Done. Returned value was: None","logger":"airflow.task.operators.airflow.providers.standard.operators.python.PythonOperator"}
