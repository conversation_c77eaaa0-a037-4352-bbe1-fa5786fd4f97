{"timestamp":"2025-09-05T07:47:38.899056","level":"info","event":"DAG bundles loaded: dags-folder","logger":"airflow.dag_processing.bundles.manager.DagBundlesManager"}
{"timestamp":"2025-09-05T07:47:38.899426","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln_to_mstack_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:47:38.921308","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T07:47:38.925653","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T07:47:38.930193","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T07:47:38.986062","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T07:47:39.006840","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T07:47:39.009575","level":"info","event":"Creating or retrieving cached MongoClient for URI ending in ...IdleTimeMS=120000%22","logger":"root"}
{"timestamp":"2025-09-05T07:47:39.012354","level":"warning","event":"maxidletimems must be an integer or float","category":"UserWarning","filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/uri_parser_shared.py","lineno":379,"logger":"py.warnings"}
{"timestamp":"2025-09-05T07:47:39.012492","level":"info","event":"You appear to be connected to a CosmosDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/cosmosdb","logger":"pymongo.client"}
{"timestamp":"2025-09-05T07:47:39.216364","level":"info","event":"You appear to be connected to a CosmosDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/cosmosdb","logger":"pymongo.client"}
{"timestamp":"2025-09-05T07:47:41.510888","level":"info","event":"MongoClient connected and verified successfully.","logger":"root"}
{"timestamp":"2025-09-05T07:47:41.511030","level":"info","event":"MongoOperations initialized for database 'eln' using shared client for ...IdleTimeMS=120000%22","logger":"root"}
{"timestamp":"2025-09-05T07:47:41.511090","level":"info","event":"Getting all protocols from ELN","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:42.719150","level":"info","event":"Total protocols: 8","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:42.719277","level":"info","event":"Processing protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:44.295157","level":"info","event":"Inserted data for protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:44.295301","level":"info","event":"Processed protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:44.295348","level":"info","event":"Processing protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:46.065718","level":"info","event":"Inserted data for protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:46.066145","level":"info","event":"Processed protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:46.066372","level":"info","event":"Processing protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:47.662280","level":"info","event":"Inserted data for protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:47.662663","level":"info","event":"Processed protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:47.662815","level":"info","event":"Processing protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:49.270450","level":"info","event":"Inserted data for protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:49.270564","level":"info","event":"Processed protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:49.270590","level":"info","event":"Processing protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:51.058366","level":"info","event":"Inserted data for protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:51.058933","level":"info","event":"Processed protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:51.059201","level":"info","event":"Processing protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:52.401663","level":"info","event":"Inserted data for protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:52.401758","level":"info","event":"Processed protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:52.401792","level":"info","event":"Processing protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:53.720328","level":"info","event":"Inserted data for protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:53.720619","level":"info","event":"Processed protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:53.720682","level":"info","event":"Processing protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:55.035720","level":"info","event":"Inserted data for protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:55.035812","level":"info","event":"Processed protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T07:47:55.036508","level":"info","event":"Parsed ELN data","logger":"root"}
{"timestamp":"2025-09-05T07:47:55.041605","level":"info","event":"Done. Returned value was: None","logger":"airflow.task.operators.airflow.providers.standard.operators.python.PythonOperator"}
