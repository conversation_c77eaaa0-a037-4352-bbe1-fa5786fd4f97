{"timestamp":"2025-09-05T07:44:07.753269","level":"info","event":"DAG bundles loaded: dags-folder","logger":"airflow.dag_processing.bundles.manager.DagBundlesManager"}
{"timestamp":"2025-09-05T07:44:07.753560","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln_to_mstack_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:44:07.756604","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T07:44:08.498338","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T07:44:08.500689","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T07:44:08.618387","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T07:44:09.277649","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T07:44:09.280899","level":"info","event":"Creating or retrieving cached MongoClient for URI ending in ...mple@localhost:27017","logger":"root"}
{"timestamp":"2025-09-05T07:44:39.437591","level":"error","event":"Failed to connect to MongoDB at mple@localhost:27017: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 30s, Topology Description: <TopologyDescription id: 68ba94c9d4b2f0b21e3a2ab8, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>","logger":"root"}
{"timestamp":"2025-09-05T07:44:39.441989","level":"error","event":"Task failed with exception","logger":"task","error_detail":[{"exc_type":"ServerSelectionTimeoutError","exc_value":"localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 30s, Topology Description: <TopologyDescription id: 68ba94c9d4b2f0b21e3a2ab8, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/sdk/execution_time/task_runner.py","lineno":838,"name":"run"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/sdk/execution_time/task_runner.py","lineno":1125,"name":"_execute_task"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/sdk/bases/operator.py","lineno":408,"name":"wrapper"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/providers/standard/operators/python.py","lineno":212,"name":"execute"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/providers/standard/operators/python.py","lineno":235,"name":"execute_callable"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/sdk/execution_time/callback_runner.py","lineno":81,"name":"run"},{"filename":"/opt/airflow/dags/eln_to_mstack_dag.py","lineno":12,"name":"parse_eln_data"},{"filename":"/opt/airflow/dags/eln/eln_parser.py","lineno":17,"name":"__init__"},{"filename":"/opt/airflow/dags/db/mongo_operations.py","lineno":49,"name":"__init__"},{"filename":"/opt/airflow/dags/db/mongo_operations.py","lineno":34,"name":"get_mongo_client"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/_csot.py","lineno":125,"name":"csot_wrapper"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/synchronous/database.py","lineno":926,"name":"command"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/synchronous/mongo_client.py","lineno":1879,"name":"_conn_for_reads"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/synchronous/mongo_client.py","lineno":1827,"name":"_select_server"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/synchronous/topology.py","lineno":409,"name":"select_server"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/synchronous/topology.py","lineno":387,"name":"_select_server"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/synchronous/topology.py","lineno":294,"name":"select_servers"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/synchronous/topology.py","lineno":344,"name":"_select_servers_loop"}]}]}
