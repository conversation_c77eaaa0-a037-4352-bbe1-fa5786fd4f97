{"timestamp":"2025-09-08T06:45:46.219286","level":"info","event":"DAG bundles loaded: dags-folder","logger":"airflow.dag_processing.bundles.manager.DagBundlesManager"}
{"timestamp":"2025-09-08T06:45:46.219866","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln_to_mstack_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:45:46.227129","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:45:46.234510","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:45:46.238869","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:45:46.296883","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:45:46.309863","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:45:46.311955","level":"info","event":"Creating or retrieving cached MongoClient for URI ending in ...IdleTimeMS=120000%22","logger":"root"}
{"timestamp":"2025-09-08T06:45:46.314669","level":"warning","event":"maxidletimems must be an integer or float","category":"UserWarning","filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/uri_parser_shared.py","lineno":379,"logger":"py.warnings"}
{"timestamp":"2025-09-08T06:45:46.314784","level":"info","event":"You appear to be connected to a CosmosDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/cosmosdb","logger":"pymongo.client"}
{"timestamp":"2025-09-08T06:45:46.454691","level":"info","event":"You appear to be connected to a CosmosDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/cosmosdb","logger":"pymongo.client"}
{"timestamp":"2025-09-08T06:45:49.296807","level":"info","event":"MongoClient connected and verified successfully.","logger":"root"}
{"timestamp":"2025-09-08T06:45:49.297319","level":"info","event":"MongoOperations initialized for database 'eln' using shared client for ...IdleTimeMS=120000%22","logger":"root"}
{"timestamp":"2025-09-08T06:45:49.297552","level":"info","event":"Getting all protocols from ELN","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:51.427736","level":"info","event":"Total protocols: 309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:51.427883","level":"info","event":"Processing protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:52.922269","level":"info","event":"Inserted data for protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:52.922528","level":"info","event":"Processed protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:52.922686","level":"info","event":"Processing protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:54.902403","level":"info","event":"Inserted data for protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:54.902928","level":"info","event":"Processed protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:54.903088","level":"info","event":"Processing protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:57.003796","level":"info","event":"Inserted data for protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:57.004284","level":"info","event":"Processed protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:57.004601","level":"info","event":"Processing protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:58.644333","level":"info","event":"Inserted data for protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:58.644675","level":"info","event":"Processed protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:45:58.644907","level":"info","event":"Processing protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:00.722455","level":"info","event":"Inserted data for protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:00.723358","level":"info","event":"Processed protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:00.723659","level":"info","event":"Processing protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:02.515244","level":"info","event":"Inserted data for protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:02.515517","level":"info","event":"Processed protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:02.515703","level":"info","event":"Processing protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:03.960762","level":"info","event":"Inserted data for protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:03.961209","level":"info","event":"Processed protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:03.961425","level":"info","event":"Processing protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:05.537451","level":"info","event":"Inserted data for protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:05.537665","level":"info","event":"Processed protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:05.537762","level":"info","event":"Processing protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:07.395555","level":"info","event":"Inserted data for protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:07.395869","level":"info","event":"Processed protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:07.396007","level":"info","event":"Processing protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:09.009014","level":"info","event":"Inserted data for protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:09.009461","level":"info","event":"Processed protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:09.009744","level":"info","event":"Processing protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:10.925965","level":"info","event":"Inserted data for protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:10.926216","level":"info","event":"Processed protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:10.926293","level":"info","event":"Processing protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:12.551767","level":"info","event":"Inserted data for protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:12.552290","level":"info","event":"Processed protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:12.552643","level":"info","event":"Processing protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:14.697047","level":"info","event":"Inserted data for protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:14.697170","level":"info","event":"Processed protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:14.697233","level":"info","event":"Processing protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:16.293463","level":"info","event":"Inserted data for protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:16.293692","level":"info","event":"Processed protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:16.293736","level":"info","event":"Processing protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:18.215164","level":"info","event":"Inserted data for protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:18.215719","level":"info","event":"Processed protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:18.216076","level":"info","event":"Processing protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:19.905699","level":"info","event":"Inserted data for protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:19.906780","level":"info","event":"Processed protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:19.907428","level":"info","event":"Processing protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:21.776170","level":"info","event":"Inserted data for protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:21.776394","level":"info","event":"Processed protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:21.776461","level":"info","event":"Processing protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:23.456560","level":"info","event":"Inserted data for protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:23.457527","level":"info","event":"Processed protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:23.457720","level":"info","event":"Processing protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:25.150410","level":"info","event":"Inserted data for protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:25.150621","level":"info","event":"Processed protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:25.150713","level":"info","event":"Processing protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:26.844828","level":"info","event":"Inserted data for protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:26.844901","level":"info","event":"Processed protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:26.844948","level":"info","event":"Processing protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:28.721784","level":"info","event":"Inserted data for protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:28.722116","level":"info","event":"Processed protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:28.722339","level":"info","event":"Processing protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:30.376774","level":"info","event":"Inserted data for protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:30.377186","level":"info","event":"Processed protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:30.377399","level":"info","event":"Processing protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:31.746727","level":"info","event":"Inserted data for protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:31.747351","level":"info","event":"Processed protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:31.747608","level":"info","event":"Processing protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:33.391035","level":"info","event":"Inserted data for protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:33.391526","level":"info","event":"Processed protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:33.391882","level":"info","event":"Processing protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:35.241534","level":"info","event":"Inserted data for protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:35.241817","level":"info","event":"Processed protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:35.242049","level":"info","event":"Processing protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:37.066365","level":"info","event":"Inserted data for protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:37.066690","level":"info","event":"Processed protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:37.066916","level":"info","event":"Processing protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:38.488858","level":"info","event":"Inserted data for protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:38.489291","level":"info","event":"Processed protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:38.489393","level":"info","event":"Processing protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:39.904663","level":"info","event":"Inserted data for protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:39.904743","level":"info","event":"Processed protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:39.904808","level":"info","event":"Processing protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:41.254456","level":"info","event":"Inserted data for protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:41.254828","level":"info","event":"Processed protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:41.255109","level":"info","event":"Processing protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:42.718540","level":"info","event":"Inserted data for protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:42.718832","level":"info","event":"Processed protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:42.719086","level":"info","event":"Processing protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:44.380345","level":"info","event":"Inserted data for protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:44.380521","level":"info","event":"Processed protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:44.380575","level":"info","event":"Processing protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:45.793609","level":"info","event":"Inserted data for protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:45.794037","level":"info","event":"Processed protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:45.794214","level":"info","event":"Processing protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:47.155845","level":"info","event":"Inserted data for protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:47.155937","level":"info","event":"Processed protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:47.155992","level":"info","event":"Processing protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:48.536754","level":"info","event":"Inserted data for protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:48.536888","level":"info","event":"Processed protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:48.536951","level":"info","event":"Processing protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:50.648158","level":"info","event":"Inserted data for protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:50.648599","level":"info","event":"Processed protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:50.648908","level":"info","event":"Processing protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:52.359075","level":"info","event":"Inserted data for protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:52.359402","level":"info","event":"Processed protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:52.359606","level":"info","event":"Processing protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:54.171590","level":"info","event":"Inserted data for protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:54.171955","level":"info","event":"Processed protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:54.172299","level":"info","event":"Processing protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:55.527610","level":"info","event":"Inserted data for protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:55.527971","level":"info","event":"Processed protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:55.528118","level":"info","event":"Processing protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:56.925972","level":"info","event":"Inserted data for protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:56.926377","level":"info","event":"Processed protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:56.926603","level":"info","event":"Processing protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:58.585621","level":"info","event":"Inserted data for protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:58.585688","level":"info","event":"Processed protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:58.585733","level":"info","event":"Processing protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:59.978859","level":"info","event":"Inserted data for protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:59.979427","level":"info","event":"Processed protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:46:59.982312","level":"info","event":"Processing protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:01.700165","level":"info","event":"Inserted data for protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:01.700349","level":"info","event":"Processed protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:01.700505","level":"info","event":"Processing protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:03.306789","level":"info","event":"Inserted data for protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:03.307147","level":"info","event":"Processed protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:03.307320","level":"info","event":"Processing protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:04.727595","level":"info","event":"Inserted data for protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:04.728063","level":"info","event":"Processed protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:04.728273","level":"info","event":"Processing protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:06.194251","level":"info","event":"Inserted data for protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:06.194324","level":"info","event":"Processed protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:06.194365","level":"info","event":"Processing protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:07.652026","level":"info","event":"Inserted data for protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:07.652098","level":"info","event":"Processed protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:07.652155","level":"info","event":"Processing protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:09.724853","level":"info","event":"Inserted data for protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:09.725129","level":"info","event":"Processed protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:09.725198","level":"info","event":"Processing protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:11.385610","level":"info","event":"Inserted data for protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:11.385852","level":"info","event":"Processed protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:11.386032","level":"info","event":"Processing protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:12.745582","level":"info","event":"Inserted data for protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:12.745862","level":"info","event":"Processed protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:12.746122","level":"info","event":"Processing protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:14.198734","level":"info","event":"Inserted data for protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:14.198920","level":"info","event":"Processed protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:14.199060","level":"info","event":"Processing protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:15.607377","level":"info","event":"Inserted data for protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:15.607520","level":"info","event":"Processed protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:15.607835","level":"info","event":"Processing protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:17.285199","level":"info","event":"Inserted data for protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:17.285433","level":"info","event":"Processed protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:17.285473","level":"info","event":"Processing protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:18.798804","level":"info","event":"Inserted data for protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:18.799002","level":"info","event":"Processed protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:18.799089","level":"info","event":"Processing protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:20.377739","level":"info","event":"Inserted data for protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:20.377841","level":"info","event":"Processed protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:20.377897","level":"info","event":"Processing protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:21.771358","level":"info","event":"Inserted data for protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:21.771886","level":"info","event":"Processed protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:21.772233","level":"info","event":"Processing protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:23.192414","level":"info","event":"Inserted data for protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:23.192797","level":"info","event":"Processed protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:23.193109","level":"info","event":"Processing protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:24.908889","level":"info","event":"Inserted data for protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:24.909362","level":"info","event":"Processed protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:24.909674","level":"info","event":"Processing protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:26.311674","level":"info","event":"Inserted data for protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:26.311848","level":"info","event":"Processed protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:26.312126","level":"info","event":"Processing protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:27.769347","level":"info","event":"Inserted data for protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:27.769489","level":"info","event":"Processed protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:27.769557","level":"info","event":"Processing protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:29.148089","level":"info","event":"Inserted data for protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:29.148382","level":"info","event":"Processed protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:29.148515","level":"info","event":"Processing protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:30.775204","level":"info","event":"Inserted data for protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:30.775293","level":"info","event":"Processed protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:30.775328","level":"info","event":"Processing protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:32.201912","level":"info","event":"Inserted data for protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:32.202450","level":"info","event":"Processed protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:32.202761","level":"info","event":"Processing protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:33.583787","level":"info","event":"Inserted data for protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:33.584248","level":"info","event":"Processed protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:33.584473","level":"info","event":"Processing protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:35.235252","level":"info","event":"Inserted data for protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:35.235593","level":"info","event":"Processed protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:35.235822","level":"info","event":"Processing protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:36.602264","level":"info","event":"Inserted data for protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:36.602660","level":"info","event":"Processed protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:36.602873","level":"info","event":"Processing protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:38.116766","level":"info","event":"Inserted data for protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:38.117323","level":"info","event":"Processed protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:38.117657","level":"info","event":"Processing protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:39.530505","level":"info","event":"Inserted data for protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:39.531091","level":"info","event":"Processed protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:39.531537","level":"info","event":"Processing protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:41.335858","level":"info","event":"Inserted data for protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:41.336293","level":"info","event":"Processed protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:41.336584","level":"info","event":"Processing protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:43.054031","level":"info","event":"Inserted data for protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:43.054415","level":"info","event":"Processed protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:43.054638","level":"info","event":"Processing protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:44.727063","level":"info","event":"Inserted data for protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:44.727482","level":"info","event":"Processed protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:44.727730","level":"info","event":"Processing protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:46.135935","level":"info","event":"Inserted data for protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:46.151038","level":"info","event":"Processed protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:46.153812","level":"info","event":"Processing protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:47.703433","level":"info","event":"Inserted data for protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:47.703527","level":"info","event":"Processed protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:47.703567","level":"info","event":"Processing protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:49.454848","level":"info","event":"Inserted data for protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:49.455526","level":"info","event":"Processed protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:49.455842","level":"info","event":"Processing protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:51.108300","level":"info","event":"Inserted data for protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:51.108701","level":"info","event":"Processed protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:51.108783","level":"info","event":"Processing protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:52.436298","level":"info","event":"Inserted data for protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:52.436374","level":"info","event":"Processed protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:52.436420","level":"info","event":"Processing protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:54.083915","level":"info","event":"Inserted data for protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:54.084274","level":"info","event":"Processed protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:54.084488","level":"info","event":"Processing protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:55.462462","level":"info","event":"Inserted data for protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:55.462919","level":"info","event":"Processed protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:55.463280","level":"info","event":"Processing protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:56.805356","level":"info","event":"Inserted data for protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:56.805465","level":"info","event":"Processed protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:56.805509","level":"info","event":"Processing protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:58.449908","level":"info","event":"Inserted data for protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:58.450319","level":"info","event":"Processed protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:58.450532","level":"info","event":"Processing protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:59.817602","level":"info","event":"Inserted data for protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:59.818217","level":"info","event":"Processed protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:47:59.818340","level":"info","event":"Processing protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:01.216739","level":"info","event":"Inserted data for protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:01.216909","level":"info","event":"Processed protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:01.217072","level":"info","event":"Processing protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:02.618147","level":"info","event":"Inserted data for protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:02.618498","level":"info","event":"Processed protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:02.618678","level":"info","event":"Processing protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:04.239401","level":"info","event":"Inserted data for protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:04.239839","level":"info","event":"Processed protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:04.240270","level":"info","event":"Processing protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:05.591420","level":"info","event":"Inserted data for protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:05.591731","level":"info","event":"Processed protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:05.591910","level":"info","event":"Processing protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:07.225261","level":"info","event":"Inserted data for protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:07.225470","level":"info","event":"Processed protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:07.225555","level":"info","event":"Processing protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:08.559305","level":"info","event":"Inserted data for protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:08.559568","level":"info","event":"Processed protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:08.559711","level":"info","event":"Processing protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:10.052678","level":"info","event":"Inserted data for protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:10.053346","level":"info","event":"Processed protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:10.053652","level":"info","event":"Processing protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:11.758638","level":"info","event":"Inserted data for protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:11.758792","level":"info","event":"Processed protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:11.758883","level":"info","event":"Processing protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:13.121248","level":"info","event":"Inserted data for protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:13.121557","level":"info","event":"Processed protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:13.121745","level":"info","event":"Processing protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:14.752217","level":"info","event":"Inserted data for protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:14.752370","level":"info","event":"Processed protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:14.752453","level":"info","event":"Processing protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:16.112266","level":"info","event":"Inserted data for protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:16.112630","level":"info","event":"Processed protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:16.112913","level":"info","event":"Processing protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:17.487512","level":"info","event":"Inserted data for protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:17.487825","level":"info","event":"Processed protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:17.488002","level":"info","event":"Processing protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:19.173493","level":"info","event":"Inserted data for protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:19.173785","level":"info","event":"Processed protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:19.173835","level":"info","event":"Processing protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:20.570341","level":"info","event":"Inserted data for protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:20.570581","level":"info","event":"Processed protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:20.570712","level":"info","event":"Processing protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:21.946126","level":"info","event":"Inserted data for protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:21.946362","level":"info","event":"Processed protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:21.946673","level":"info","event":"Processing protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:23.338284","level":"info","event":"Inserted data for protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:23.338433","level":"info","event":"Processed protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:23.338487","level":"info","event":"Processing protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:24.949531","level":"info","event":"Inserted data for protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:24.949769","level":"info","event":"Processed protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:24.949863","level":"info","event":"Processing protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:26.303644","level":"info","event":"Inserted data for protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:26.303948","level":"info","event":"Processed protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:26.304132","level":"info","event":"Processing protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:27.687461","level":"info","event":"Inserted data for protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:27.687638","level":"info","event":"Processed protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:27.687817","level":"info","event":"Processing protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:29.027390","level":"info","event":"Inserted data for protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:29.027848","level":"info","event":"Processed protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:29.028128","level":"info","event":"Processing protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:31.011716","level":"info","event":"Inserted data for protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:31.012785","level":"info","event":"Processed protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:31.013034","level":"info","event":"Processing protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:33.013355","level":"info","event":"Inserted data for protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:33.013658","level":"info","event":"Processed protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:33.013828","level":"info","event":"Processing protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:34.657151","level":"info","event":"Inserted data for protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:34.657530","level":"info","event":"Processed protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:34.657758","level":"info","event":"Processing protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:36.064719","level":"info","event":"Inserted data for protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:36.065102","level":"info","event":"Processed protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:36.065386","level":"info","event":"Processing protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:37.446761","level":"info","event":"Inserted data for protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:37.447240","level":"info","event":"Processed protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:37.447555","level":"info","event":"Processing protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:38.829375","level":"info","event":"Inserted data for protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:38.829476","level":"info","event":"Processed protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:38.829537","level":"info","event":"Processing protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:40.439512","level":"info","event":"Inserted data for protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:40.440280","level":"info","event":"Processed protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:40.440463","level":"info","event":"Processing protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:41.874564","level":"info","event":"Inserted data for protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:41.875149","level":"info","event":"Processed protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:41.875500","level":"info","event":"Processing protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:43.491520","level":"info","event":"Inserted data for protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:43.491810","level":"info","event":"Processed protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:43.492012","level":"info","event":"Processing protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:44.879750","level":"info","event":"Inserted data for protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:44.879832","level":"info","event":"Processed protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:44.879882","level":"info","event":"Processing protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:46.637222","level":"info","event":"Inserted data for protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:46.637594","level":"info","event":"Processed protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:46.637730","level":"info","event":"Processing protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:48.138782","level":"info","event":"Inserted data for protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:48.139421","level":"info","event":"Processed protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:48.139804","level":"info","event":"Processing protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:49.560823","level":"info","event":"Inserted data for protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:49.561264","level":"info","event":"Processed protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:49.561582","level":"info","event":"Processing protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:51.486668","level":"info","event":"Inserted data for protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:51.487215","level":"info","event":"Processed protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:51.487505","level":"info","event":"Processing protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:53.118717","level":"info","event":"Inserted data for protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:53.119111","level":"info","event":"Processed protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:53.119313","level":"info","event":"Processing protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:54.512599","level":"info","event":"Inserted data for protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:54.533358","level":"info","event":"Processed protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:54.534889","level":"info","event":"Processing protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:55.996825","level":"info","event":"Inserted data for protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:55.996975","level":"info","event":"Processed protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:55.997054","level":"info","event":"Processing protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:57.922352","level":"info","event":"Inserted data for protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:57.923012","level":"info","event":"Processed protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:57.923383","level":"info","event":"Processing protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:59.277936","level":"info","event":"Inserted data for protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:59.278321","level":"info","event":"Processed protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:48:59.278621","level":"info","event":"Processing protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:00.651796","level":"info","event":"Inserted data for protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:00.652066","level":"info","event":"Processed protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:00.652177","level":"info","event":"Processing protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:02.519590","level":"info","event":"Inserted data for protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:02.519672","level":"info","event":"Processed protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:02.519712","level":"info","event":"Processing protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:03.954259","level":"info","event":"Inserted data for protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:03.954622","level":"info","event":"Processed protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:03.954881","level":"info","event":"Processing protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:05.814426","level":"info","event":"Inserted data for protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:05.815506","level":"info","event":"Processed protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:05.815840","level":"info","event":"Processing protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:07.215451","level":"info","event":"Inserted data for protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:07.215538","level":"info","event":"Processed protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:07.215588","level":"info","event":"Processing protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:08.550863","level":"info","event":"Inserted data for protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:08.551278","level":"info","event":"Processed protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:08.551515","level":"info","event":"Processing protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:09.987312","level":"info","event":"Inserted data for protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:09.987593","level":"info","event":"Processed protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:09.987757","level":"info","event":"Processing protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:12.082338","level":"info","event":"Inserted data for protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:12.084115","level":"info","event":"Processed protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:12.084454","level":"info","event":"Processing protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:13.704581","level":"info","event":"Inserted data for protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:13.705065","level":"info","event":"Processed protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:13.705331","level":"info","event":"Processing protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:15.378587","level":"info","event":"Inserted data for protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:15.378845","level":"info","event":"Processed protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:15.379056","level":"info","event":"Processing protocol: 1000179","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:16.738689","level":"info","event":"Inserted data for protocol: 1000179","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:16.739059","level":"info","event":"Processed protocol: 1000179","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:16.739256","level":"info","event":"Processing protocol: 1000178","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:18.093333","level":"info","event":"Inserted data for protocol: 1000178","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:18.093418","level":"info","event":"Processed protocol: 1000178","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:18.093460","level":"info","event":"Processing protocol: 1000177","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:19.711304","level":"info","event":"Inserted data for protocol: 1000177","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:19.711546","level":"info","event":"Processed protocol: 1000177","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:19.711601","level":"info","event":"Processing protocol: 1000176","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:21.193920","level":"info","event":"Inserted data for protocol: 1000176","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:21.194284","level":"info","event":"Processed protocol: 1000176","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:21.194484","level":"info","event":"Processing protocol: 1000175","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:22.627109","level":"info","event":"Inserted data for protocol: 1000175","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:22.627297","level":"info","event":"Processed protocol: 1000175","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:22.627396","level":"info","event":"Processing protocol: 1000174","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:25.347498","level":"info","event":"Inserted data for protocol: 1000174","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:25.347929","level":"info","event":"Processed protocol: 1000174","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:25.348239","level":"info","event":"Processing protocol: 1000173","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:26.965811","level":"info","event":"Inserted data for protocol: 1000173","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:26.966277","level":"info","event":"Processed protocol: 1000173","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:26.966440","level":"info","event":"Processing protocol: 1000172","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:28.594664","level":"info","event":"Inserted data for protocol: 1000172","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:28.594751","level":"info","event":"Processed protocol: 1000172","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:28.594814","level":"info","event":"Processing protocol: 1000171","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:30.218600","level":"info","event":"Inserted data for protocol: 1000171","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:30.219016","level":"info","event":"Processed protocol: 1000171","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:30.219233","level":"info","event":"Processing protocol: 1000170","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:31.951219","level":"info","event":"Inserted data for protocol: 1000170","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:31.951573","level":"info","event":"Processed protocol: 1000170","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:31.951800","level":"info","event":"Processing protocol: 1000169","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:33.306362","level":"info","event":"Inserted data for protocol: 1000169","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:33.306755","level":"info","event":"Processed protocol: 1000169","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:33.306975","level":"info","event":"Processing protocol: 1000168","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:34.676607","level":"info","event":"Inserted data for protocol: 1000168","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:34.676713","level":"info","event":"Processed protocol: 1000168","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:34.676763","level":"info","event":"Processing protocol: 1000167","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:36.666364","level":"info","event":"Inserted data for protocol: 1000167","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:36.667462","level":"info","event":"Processed protocol: 1000167","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:36.667894","level":"info","event":"Processing protocol: 1000166","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:38.338429","level":"info","event":"Inserted data for protocol: 1000166","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:38.338859","level":"info","event":"Processed protocol: 1000166","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:38.339162","level":"info","event":"Processing protocol: 1000165","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:39.748118","level":"info","event":"Inserted data for protocol: 1000165","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:39.748503","level":"info","event":"Processed protocol: 1000165","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:39.748661","level":"info","event":"Processing protocol: 1000164","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:41.126035","level":"info","event":"Inserted data for protocol: 1000164","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:41.126547","level":"info","event":"Processed protocol: 1000164","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:41.126916","level":"info","event":"Processing protocol: 1000163","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:42.551688","level":"info","event":"Inserted data for protocol: 1000163","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:42.551815","level":"info","event":"Processed protocol: 1000163","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:42.551871","level":"info","event":"Processing protocol: 1000162","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:43.902851","level":"info","event":"Inserted data for protocol: 1000162","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:43.903275","level":"info","event":"Processed protocol: 1000162","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:43.903542","level":"info","event":"Processing protocol: 1000161","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:45.428301","level":"info","event":"Inserted data for protocol: 1000161","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:45.428852","level":"info","event":"Processed protocol: 1000161","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:45.429062","level":"info","event":"Processing protocol: 1000160","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:46.888685","level":"info","event":"Inserted data for protocol: 1000160","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:46.889148","level":"info","event":"Processed protocol: 1000160","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:46.889359","level":"info","event":"Processing protocol: 1000159","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:48.323279","level":"info","event":"Inserted data for protocol: 1000159","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:48.323556","level":"info","event":"Processed protocol: 1000159","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:48.323767","level":"info","event":"Processing protocol: 1000158","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:49.697899","level":"info","event":"Inserted data for protocol: 1000158","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:49.698168","level":"info","event":"Processed protocol: 1000158","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:49.698330","level":"info","event":"Processing protocol: 1000157","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:51.291762","level":"info","event":"Inserted data for protocol: 1000157","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:51.292511","level":"info","event":"Processed protocol: 1000157","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:51.292862","level":"info","event":"Processing protocol: 1000156","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:52.938598","level":"info","event":"Inserted data for protocol: 1000156","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:52.938901","level":"info","event":"Processed protocol: 1000156","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:52.939277","level":"info","event":"Processing protocol: 1000155","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:54.533473","level":"info","event":"Inserted data for protocol: 1000155","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:54.533833","level":"info","event":"Processed protocol: 1000155","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:54.534075","level":"info","event":"Processing protocol: 1000154","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:55.937233","level":"info","event":"Inserted data for protocol: 1000154","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:55.937657","level":"info","event":"Processed protocol: 1000154","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:55.938101","level":"info","event":"Processing protocol: 1000153","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:57.558688","level":"info","event":"Inserted data for protocol: 1000153","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:57.558877","level":"info","event":"Processed protocol: 1000153","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:57.558929","level":"info","event":"Processing protocol: 1000152","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:59.184057","level":"info","event":"Inserted data for protocol: 1000152","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:59.184286","level":"info","event":"Processed protocol: 1000152","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:49:59.184404","level":"info","event":"Processing protocol: 1000151","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:00.552970","level":"info","event":"Inserted data for protocol: 1000151","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:00.553084","level":"info","event":"Processed protocol: 1000151","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:00.553134","level":"info","event":"Processing protocol: 1000150","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:01.936462","level":"info","event":"Inserted data for protocol: 1000150","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:01.936640","level":"info","event":"Processed protocol: 1000150","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:01.936708","level":"info","event":"Processing protocol: 1000149","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:03.587245","level":"info","event":"Inserted data for protocol: 1000149","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:03.587415","level":"info","event":"Processed protocol: 1000149","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:03.587524","level":"info","event":"Processing protocol: 1000148","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:04.959337","level":"info","event":"Inserted data for protocol: 1000148","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:04.960518","level":"info","event":"Processed protocol: 1000148","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:04.960964","level":"info","event":"Processing protocol: 1000147","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:06.375924","level":"info","event":"Inserted data for protocol: 1000147","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:06.376546","level":"info","event":"Processed protocol: 1000147","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:06.376766","level":"info","event":"Processing protocol: 1000146","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:08.326255","level":"info","event":"Inserted data for protocol: 1000146","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:08.326582","level":"info","event":"Processed protocol: 1000146","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:08.326723","level":"info","event":"Processing protocol: 1000145","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:09.679413","level":"info","event":"Inserted data for protocol: 1000145","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:09.679852","level":"info","event":"Processed protocol: 1000145","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:09.680092","level":"info","event":"Processing protocol: 1000144","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:11.137264","level":"info","event":"Inserted data for protocol: 1000144","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:11.137647","level":"info","event":"Processed protocol: 1000144","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:11.138033","level":"info","event":"Processing protocol: 1000143","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:12.616864","level":"info","event":"Inserted data for protocol: 1000143","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:12.617105","level":"info","event":"Processed protocol: 1000143","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:12.617251","level":"info","event":"Processing protocol: 1000142","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:13.984567","level":"info","event":"Inserted data for protocol: 1000142","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:13.984936","level":"info","event":"Processed protocol: 1000142","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:13.985072","level":"info","event":"Processing protocol: 1000141","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:15.409927","level":"info","event":"Inserted data for protocol: 1000141","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:15.410523","level":"info","event":"Processed protocol: 1000141","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:15.410848","level":"info","event":"Processing protocol: 1000140","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:17.063295","level":"info","event":"Inserted data for protocol: 1000140","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:17.063489","level":"info","event":"Processed protocol: 1000140","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:17.063629","level":"info","event":"Processing protocol: 1000139","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:18.460194","level":"info","event":"Inserted data for protocol: 1000139","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:18.460354","level":"info","event":"Processed protocol: 1000139","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:18.460476","level":"info","event":"Processing protocol: 1000138","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:19.962129","level":"info","event":"Inserted data for protocol: 1000138","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:19.962591","level":"info","event":"Processed protocol: 1000138","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:19.962703","level":"info","event":"Processing protocol: 1000137","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:21.644554","level":"info","event":"Inserted data for protocol: 1000137","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:21.645034","level":"info","event":"Processed protocol: 1000137","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:21.645151","level":"info","event":"Processing protocol: 1000136","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:23.040354","level":"info","event":"Inserted data for protocol: 1000136","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:23.041278","level":"info","event":"Processed protocol: 1000136","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:23.041528","level":"info","event":"Processing protocol: 1000135","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:24.677447","level":"info","event":"Inserted data for protocol: 1000135","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:24.677537","level":"info","event":"Processed protocol: 1000135","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:24.677596","level":"info","event":"Processing protocol: 1000134","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:26.320791","level":"info","event":"Inserted data for protocol: 1000134","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:26.321229","level":"info","event":"Processed protocol: 1000134","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:26.321493","level":"info","event":"Processing protocol: 1000133","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:28.043583","level":"info","event":"Inserted data for protocol: 1000133","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:28.044014","level":"info","event":"Processed protocol: 1000133","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:28.044308","level":"info","event":"Processing protocol: 1000132","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:29.671411","level":"info","event":"Inserted data for protocol: 1000132","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:29.671545","level":"info","event":"Processed protocol: 1000132","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:29.671599","level":"info","event":"Processing protocol: 1000131","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:31.019091","level":"info","event":"Inserted data for protocol: 1000131","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:31.019576","level":"info","event":"Processed protocol: 1000131","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:31.019780","level":"info","event":"Processing protocol: 1000130","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:32.654604","level":"info","event":"Inserted data for protocol: 1000130","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:32.655373","level":"info","event":"Processed protocol: 1000130","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:32.655777","level":"info","event":"Processing protocol: 1000129","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:34.212331","level":"info","event":"Inserted data for protocol: 1000129","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:34.212547","level":"info","event":"Processed protocol: 1000129","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:34.212624","level":"info","event":"Processing protocol: 1000128","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:35.849918","level":"info","event":"Inserted data for protocol: 1000128","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:35.851073","level":"info","event":"Processed protocol: 1000128","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:35.851349","level":"info","event":"Processing protocol: 1000127","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:37.238175","level":"info","event":"Inserted data for protocol: 1000127","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:37.238567","level":"info","event":"Processed protocol: 1000127","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:37.239183","level":"info","event":"Processing protocol: 1000126","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:39.171489","level":"info","event":"Inserted data for protocol: 1000126","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:39.172035","level":"info","event":"Processed protocol: 1000126","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:39.172508","level":"info","event":"Processing protocol: 1000125","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:40.540597","level":"info","event":"Inserted data for protocol: 1000125","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:40.540866","level":"info","event":"Processed protocol: 1000125","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:40.541071","level":"info","event":"Processing protocol: 1000124","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:41.938947","level":"info","event":"Inserted data for protocol: 1000124","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:41.939292","level":"info","event":"Processed protocol: 1000124","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:41.939505","level":"info","event":"Processing protocol: 1000123","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:43.596751","level":"info","event":"Inserted data for protocol: 1000123","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:43.597074","level":"info","event":"Processed protocol: 1000123","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:43.597247","level":"info","event":"Processing protocol: 1000122","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:44.947947","level":"info","event":"Inserted data for protocol: 1000122","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:44.948335","level":"info","event":"Processed protocol: 1000122","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:44.948582","level":"info","event":"Processing protocol: 1000121","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:50:46.298880","level":"error","event":"Process timed out, PID: 71","logger":"airflow.utils.timeout.TimeoutPosix"}
{"timestamp":"2025-09-08T06:50:46.303669","level":"error","event":"Task failed with exception","logger":"task","error_detail":[{"exc_type":"AirflowTaskTimeout","exc_value":"Timeout, PID: 71","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/sdk/execution_time/task_runner.py","lineno":838,"name":"run"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/sdk/execution_time/task_runner.py","lineno":1125,"name":"_execute_task"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/sdk/bases/operator.py","lineno":408,"name":"wrapper"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/providers/standard/operators/python.py","lineno":212,"name":"execute"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/providers/standard/operators/python.py","lineno":235,"name":"execute_callable"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/sdk/execution_time/callback_runner.py","lineno":81,"name":"run"},{"filename":"/opt/airflow/dags/eln_to_mstack_dag.py","lineno":13,"name":"parse_eln_data"},{"filename":"/opt/airflow/dags/eln/eln_parser.py","lineno":459,"name":"get_all_data"},{"filename":"/opt/airflow/dags/eln/eln_parser.py","lineno":439,"name":"insert_data"},{"filename":"/opt/airflow/dags/db/mongo_operations.py","lineno":166,"name":"upsert_data"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/synchronous/collection.py","lineno":1336,"name":"update_one"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/synchronous/collection.py","lineno":1118,"name":"_update_retryable"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/synchronous/mongo_client.py","lineno":2076,"name":"_retryable_write"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/synchronous/mongo_client.py","lineno":1962,"name":"_retry_with_session"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/_csot.py","lineno":125,"name":"csot_wrapper"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/synchronous/mongo_client.py","lineno":2008,"name":"_retry_internal"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/synchronous/mongo_client.py","lineno":2745,"name":"run"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/synchronous/mongo_client.py","lineno":2877,"name":"_write"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/synchronous/collection.py","lineno":1098,"name":"_update"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/synchronous/collection.py","lineno":1049,"name":"_update"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/synchronous/helpers.py","lineno":47,"name":"inner"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/synchronous/pool.py","lineno":441,"name":"command"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/synchronous/pool.py","lineno":413,"name":"command"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/synchronous/network.py","lineno":198,"name":"command"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/network_layer.py","lineno":759,"name":"receive_message"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/network_layer.py","lineno":353,"name":"receive_data"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/network_layer.py","lineno":469,"name":"recv_into"},{"filename":"/usr/local/lib/python3.12/ssl.py","lineno":1251,"name":"recv_into"},{"filename":"/usr/local/lib/python3.12/ssl.py","lineno":1103,"name":"read"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/utils/timeout.py","lineno":69,"name":"handle_timeout"}]}]}
