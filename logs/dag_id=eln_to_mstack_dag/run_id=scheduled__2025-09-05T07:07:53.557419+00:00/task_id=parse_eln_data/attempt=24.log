{"timestamp":"2025-09-05T09:00:39.820788","level":"info","event":"DAG bundles loaded: dags-folder","logger":"airflow.dag_processing.bundles.manager.DagBundlesManager"}
{"timestamp":"2025-09-05T09:00:39.821987","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln_to_mstack_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T09:00:39.836858","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T09:00:39.843757","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T09:00:39.849399","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T09:00:39.966770","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T09:00:39.978590","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T09:00:39.981035","level":"info","event":"Creating or retrieving cached MongoClient for URI ending in ...IdleTimeMS=120000%22","logger":"root"}
{"timestamp":"2025-09-05T09:00:39.984762","level":"warning","event":"maxidletimems must be an integer or float","category":"UserWarning","filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/uri_parser_shared.py","lineno":379,"logger":"py.warnings"}
{"timestamp":"2025-09-05T09:00:39.984872","level":"info","event":"You appear to be connected to a CosmosDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/cosmosdb","logger":"pymongo.client"}
{"timestamp":"2025-09-05T09:00:40.144918","level":"info","event":"You appear to be connected to a CosmosDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/cosmosdb","logger":"pymongo.client"}
{"timestamp":"2025-09-05T09:00:42.498192","level":"info","event":"MongoClient connected and verified successfully.","logger":"root"}
{"timestamp":"2025-09-05T09:00:42.498918","level":"info","event":"MongoOperations initialized for database 'eln' using shared client for ...IdleTimeMS=120000%22","logger":"root"}
{"timestamp":"2025-09-05T09:00:42.498983","level":"info","event":"Getting all protocols from ELN","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:44.441035","level":"info","event":"Total protocols: 129","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:44.441321","level":"info","event":"Processing protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:45.851680","level":"info","event":"Inserted data for protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:45.852087","level":"info","event":"Processed protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:45.852326","level":"info","event":"Processing protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:48.270680","level":"info","event":"Inserted data for protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:48.271224","level":"info","event":"Processed protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:48.271463","level":"info","event":"Processing protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:49.946211","level":"info","event":"Inserted data for protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:49.946496","level":"info","event":"Processed protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:49.946631","level":"info","event":"Processing protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:51.647225","level":"info","event":"Inserted data for protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:51.647385","level":"info","event":"Processed protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:51.647476","level":"info","event":"Processing protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:53.665853","level":"info","event":"Inserted data for protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:53.666147","level":"info","event":"Processed protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:53.666280","level":"info","event":"Processing protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:55.133531","level":"info","event":"Inserted data for protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:55.133912","level":"info","event":"Processed protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:55.134271","level":"info","event":"Processing protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:56.482431","level":"info","event":"Inserted data for protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:56.482800","level":"info","event":"Processed protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:56.482987","level":"info","event":"Processing protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:57.875137","level":"info","event":"Inserted data for protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:57.875233","level":"info","event":"Processed protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:57.875273","level":"info","event":"Processing protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:59.661382","level":"info","event":"Inserted data for protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:59.661821","level":"info","event":"Processed protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:00:59.661950","level":"info","event":"Processing protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:01.093768","level":"info","event":"Inserted data for protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:01.094150","level":"info","event":"Processed protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:01.094311","level":"info","event":"Processing protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:02.680686","level":"info","event":"Inserted data for protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:02.680783","level":"info","event":"Processed protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:02.680844","level":"info","event":"Processing protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:03.982231","level":"info","event":"Inserted data for protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:03.982316","level":"info","event":"Processed protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:03.982365","level":"info","event":"Processing protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:05.719855","level":"info","event":"Inserted data for protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:05.720273","level":"info","event":"Processed protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:05.720497","level":"info","event":"Processing protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:07.140650","level":"info","event":"Inserted data for protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:07.140752","level":"info","event":"Processed protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:07.140807","level":"info","event":"Processing protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:08.958266","level":"info","event":"Inserted data for protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:08.958654","level":"info","event":"Processed protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:08.958902","level":"info","event":"Processing protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:10.566773","level":"info","event":"Inserted data for protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:10.567267","level":"info","event":"Processed protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:10.567538","level":"info","event":"Processing protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:12.192749","level":"info","event":"Inserted data for protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:12.193413","level":"info","event":"Processed protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:12.193813","level":"info","event":"Processing protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:13.869321","level":"info","event":"Inserted data for protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:13.869491","level":"info","event":"Processed protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:13.869585","level":"info","event":"Processing protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:15.531471","level":"info","event":"Inserted data for protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:15.531698","level":"info","event":"Processed protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:15.531849","level":"info","event":"Processing protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:17.140241","level":"info","event":"Inserted data for protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:17.140733","level":"info","event":"Processed protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:17.141168","level":"info","event":"Processing protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:18.745759","level":"info","event":"Inserted data for protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:18.746087","level":"info","event":"Processed protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:18.746301","level":"info","event":"Processing protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:20.378848","level":"info","event":"Inserted data for protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:20.379263","level":"info","event":"Processed protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:20.379545","level":"info","event":"Processing protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:21.724054","level":"info","event":"Inserted data for protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:21.724227","level":"info","event":"Processed protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:21.724365","level":"info","event":"Processing protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:23.345647","level":"info","event":"Inserted data for protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:23.346003","level":"info","event":"Processed protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:23.346284","level":"info","event":"Processing protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:24.956993","level":"info","event":"Inserted data for protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:24.957337","level":"info","event":"Processed protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:24.957463","level":"info","event":"Processing protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:26.573070","level":"info","event":"Inserted data for protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:26.573589","level":"info","event":"Processed protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:26.573891","level":"info","event":"Processing protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:27.916843","level":"info","event":"Inserted data for protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:27.917060","level":"info","event":"Processed protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:27.917208","level":"info","event":"Processing protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:29.243983","level":"info","event":"Inserted data for protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:29.244323","level":"info","event":"Processed protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:29.244541","level":"info","event":"Processing protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:30.610334","level":"info","event":"Inserted data for protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:30.610578","level":"info","event":"Processed protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:30.610725","level":"info","event":"Processing protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:31.992018","level":"info","event":"Inserted data for protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:31.992426","level":"info","event":"Processed protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:31.992641","level":"info","event":"Processing protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:33.610692","level":"info","event":"Inserted data for protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:33.611079","level":"info","event":"Processed protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:33.611393","level":"info","event":"Processing protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:34.960977","level":"info","event":"Inserted data for protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:34.961056","level":"info","event":"Processed protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:34.961135","level":"info","event":"Processing protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:36.295405","level":"info","event":"Inserted data for protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:36.295854","level":"info","event":"Processed protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:36.296194","level":"info","event":"Processing protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:37.629790","level":"info","event":"Inserted data for protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:37.629864","level":"info","event":"Processed protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:37.629913","level":"info","event":"Processing protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:39.526415","level":"info","event":"Inserted data for protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:39.526597","level":"info","event":"Processed protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:39.526658","level":"info","event":"Processing protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:41.276687","level":"info","event":"Inserted data for protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:41.277294","level":"info","event":"Processed protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:41.277656","level":"info","event":"Processing protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:43.001433","level":"info","event":"Inserted data for protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:43.001623","level":"info","event":"Processed protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:43.001727","level":"info","event":"Processing protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:44.381390","level":"info","event":"Inserted data for protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:44.381729","level":"info","event":"Processed protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:44.381975","level":"info","event":"Processing protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:45.762408","level":"info","event":"Inserted data for protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:45.762760","level":"info","event":"Processed protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:45.763016","level":"info","event":"Processing protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:47.426621","level":"info","event":"Inserted data for protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:47.426981","level":"info","event":"Processed protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:47.427273","level":"info","event":"Processing protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:48.797722","level":"info","event":"Inserted data for protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:48.798678","level":"info","event":"Processed protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:48.799807","level":"info","event":"Processing protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:50.373986","level":"info","event":"Inserted data for protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:50.374307","level":"info","event":"Processed protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:50.375010","level":"info","event":"Processing protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:51.800976","level":"info","event":"Inserted data for protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:51.801660","level":"info","event":"Processed protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:51.802062","level":"info","event":"Processing protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:53.192287","level":"info","event":"Inserted data for protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:53.192389","level":"info","event":"Processed protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:53.192440","level":"info","event":"Processing protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:54.514939","level":"info","event":"Inserted data for protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:54.515330","level":"info","event":"Processed protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:54.515575","level":"info","event":"Processing protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:55.868250","level":"info","event":"Inserted data for protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:55.868563","level":"info","event":"Processed protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:55.868724","level":"info","event":"Processing protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:57.723698","level":"info","event":"Inserted data for protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:57.724301","level":"info","event":"Processed protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:57.724640","level":"info","event":"Processing protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:59.333083","level":"info","event":"Inserted data for protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:59.333436","level":"info","event":"Processed protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:01:59.333636","level":"info","event":"Processing protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:00.701822","level":"info","event":"Inserted data for protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:00.701922","level":"info","event":"Processed protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:00.701966","level":"info","event":"Processing protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:02.111840","level":"info","event":"Inserted data for protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:02.111973","level":"info","event":"Processed protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:02.112053","level":"info","event":"Processing protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:03.482675","level":"info","event":"Inserted data for protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:03.482807","level":"info","event":"Processed protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:03.482891","level":"info","event":"Processing protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:05.072823","level":"info","event":"Inserted data for protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:05.073082","level":"info","event":"Processed protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:05.073380","level":"info","event":"Processing protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:06.405418","level":"info","event":"Inserted data for protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:06.405483","level":"info","event":"Processed protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:06.405522","level":"info","event":"Processing protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:08.143163","level":"info","event":"Inserted data for protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:08.143263","level":"info","event":"Processed protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:08.143369","level":"info","event":"Processing protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:09.453058","level":"info","event":"Inserted data for protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:09.453155","level":"info","event":"Processed protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:09.453213","level":"info","event":"Processing protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:10.805540","level":"info","event":"Inserted data for protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:10.805892","level":"info","event":"Processed protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:10.806565","level":"info","event":"Processing protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:12.390220","level":"info","event":"Inserted data for protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:12.390835","level":"info","event":"Processed protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:12.391171","level":"info","event":"Processing protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:13.726739","level":"info","event":"Inserted data for protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:13.727222","level":"info","event":"Processed protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:13.727586","level":"info","event":"Processing protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:15.128184","level":"info","event":"Inserted data for protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:15.128760","level":"info","event":"Processed protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:15.129149","level":"info","event":"Processing protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:16.444718","level":"info","event":"Inserted data for protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:16.444799","level":"info","event":"Processed protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:16.444855","level":"info","event":"Processing protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:18.037726","level":"info","event":"Inserted data for protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:18.038054","level":"info","event":"Processed protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:18.038307","level":"info","event":"Processing protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:19.372449","level":"info","event":"Inserted data for protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:19.372560","level":"info","event":"Processed protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:19.372606","level":"info","event":"Processing protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:20.678785","level":"info","event":"Inserted data for protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:20.679084","level":"info","event":"Processed protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:20.679289","level":"info","event":"Processing protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:22.296906","level":"info","event":"Inserted data for protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:22.296986","level":"info","event":"Processed protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:22.297050","level":"info","event":"Processing protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:23.631766","level":"info","event":"Inserted data for protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:23.632249","level":"info","event":"Processed protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:23.632614","level":"info","event":"Processing protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:24.982044","level":"info","event":"Inserted data for protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:24.982599","level":"info","event":"Processed protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:24.982773","level":"info","event":"Processing protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:26.557230","level":"info","event":"Inserted data for protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:26.557523","level":"info","event":"Processed protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:26.557721","level":"info","event":"Processing protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:28.238388","level":"info","event":"Inserted data for protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:28.238787","level":"info","event":"Processed protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:28.239078","level":"info","event":"Processing protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:30.008297","level":"info","event":"Inserted data for protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:30.008659","level":"info","event":"Processed protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:30.008926","level":"info","event":"Processing protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:31.606634","level":"info","event":"Inserted data for protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:31.607079","level":"info","event":"Processed protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:31.607384","level":"info","event":"Processing protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:32.941254","level":"info","event":"Inserted data for protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:32.941675","level":"info","event":"Processed protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:32.941937","level":"info","event":"Processing protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:34.277616","level":"info","event":"Inserted data for protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:34.278169","level":"info","event":"Processed protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:34.278391","level":"info","event":"Processing protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:36.008345","level":"info","event":"Inserted data for protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:36.008815","level":"info","event":"Processed protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:36.009185","level":"info","event":"Processing protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:37.586619","level":"info","event":"Inserted data for protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:37.586854","level":"info","event":"Processed protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:37.587038","level":"info","event":"Processing protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:38.881973","level":"info","event":"Inserted data for protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:38.882047","level":"info","event":"Processed protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:38.882123","level":"info","event":"Processing protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:40.436057","level":"info","event":"Inserted data for protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:40.436313","level":"info","event":"Processed protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:40.436429","level":"info","event":"Processing protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:41.788036","level":"info","event":"Inserted data for protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:41.788544","level":"info","event":"Processed protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:41.788744","level":"info","event":"Processing protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:43.105606","level":"info","event":"Inserted data for protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:43.106358","level":"info","event":"Processed protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:43.106708","level":"info","event":"Processing protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:44.708236","level":"info","event":"Inserted data for protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:44.708927","level":"info","event":"Processed protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:44.709311","level":"info","event":"Processing protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:46.071054","level":"info","event":"Inserted data for protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:46.071552","level":"info","event":"Processed protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:46.071866","level":"info","event":"Processing protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:47.422294","level":"info","event":"Inserted data for protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:47.422557","level":"info","event":"Processed protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:47.422781","level":"info","event":"Processing protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:48.763708","level":"info","event":"Inserted data for protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:48.764068","level":"info","event":"Processed protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:48.764367","level":"info","event":"Processing protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:50.328946","level":"info","event":"Inserted data for protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:50.329093","level":"info","event":"Processed protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:50.329165","level":"info","event":"Processing protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:51.618687","level":"info","event":"Inserted data for protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:51.618840","level":"info","event":"Processed protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:51.618926","level":"info","event":"Processing protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:53.223158","level":"info","event":"Inserted data for protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:53.223253","level":"info","event":"Processed protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:53.223296","level":"info","event":"Processing protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:54.503888","level":"info","event":"Inserted data for protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:54.504317","level":"info","event":"Processed protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:54.504762","level":"info","event":"Processing protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:55.853945","level":"info","event":"Inserted data for protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:55.854066","level":"info","event":"Processed protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:55.854144","level":"info","event":"Processing protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:57.496242","level":"info","event":"Inserted data for protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:57.496628","level":"info","event":"Processed protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:57.496864","level":"info","event":"Processing protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:58.915422","level":"info","event":"Inserted data for protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:58.915676","level":"info","event":"Processed protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:02:58.915804","level":"info","event":"Processing protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:00.731289","level":"info","event":"Inserted data for protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:00.731619","level":"info","event":"Processed protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:00.731850","level":"info","event":"Processing protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:02.587913","level":"info","event":"Inserted data for protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:02.587983","level":"info","event":"Processed protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:02.588036","level":"info","event":"Processing protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:04.333080","level":"info","event":"Inserted data for protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:04.333420","level":"info","event":"Processed protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:04.333540","level":"info","event":"Processing protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:06.558003","level":"info","event":"Inserted data for protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:06.558518","level":"info","event":"Processed protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:06.558663","level":"info","event":"Processing protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:08.464459","level":"info","event":"Inserted data for protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:08.464598","level":"info","event":"Processed protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:08.464660","level":"info","event":"Processing protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:10.747977","level":"info","event":"Inserted data for protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:10.748055","level":"info","event":"Processed protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:10.748084","level":"info","event":"Processing protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:12.155460","level":"info","event":"Inserted data for protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:12.155700","level":"info","event":"Processed protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:12.155834","level":"info","event":"Processing protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:13.777873","level":"info","event":"Inserted data for protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:13.778247","level":"info","event":"Processed protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:13.778891","level":"info","event":"Processing protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:15.165598","level":"info","event":"Inserted data for protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:15.166418","level":"info","event":"Processed protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:15.166806","level":"info","event":"Processing protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:16.556794","level":"info","event":"Inserted data for protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:16.558737","level":"info","event":"Processed protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:16.558920","level":"info","event":"Processing protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:17.875081","level":"info","event":"Inserted data for protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:17.875272","level":"info","event":"Processed protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:17.875367","level":"info","event":"Processing protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:19.747963","level":"info","event":"Inserted data for protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:19.748092","level":"info","event":"Processed protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:19.748200","level":"info","event":"Processing protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:21.573568","level":"info","event":"Inserted data for protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:21.573725","level":"info","event":"Processed protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:21.573803","level":"info","event":"Processing protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:23.128172","level":"info","event":"Inserted data for protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:23.128472","level":"info","event":"Processed protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:23.128690","level":"info","event":"Processing protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:24.445517","level":"info","event":"Inserted data for protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:24.445704","level":"info","event":"Processed protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:24.445778","level":"info","event":"Processing protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:25.807232","level":"info","event":"Inserted data for protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:25.807525","level":"info","event":"Processed protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:25.807713","level":"info","event":"Processing protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:27.113998","level":"info","event":"Inserted data for protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:27.114398","level":"info","event":"Processed protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:27.114738","level":"info","event":"Processing protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:28.622915","level":"info","event":"Inserted data for protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:28.623013","level":"info","event":"Processed protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:28.623081","level":"info","event":"Processing protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:30.016573","level":"info","event":"Inserted data for protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:30.016937","level":"info","event":"Processed protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:30.017274","level":"info","event":"Processing protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:31.616591","level":"info","event":"Inserted data for protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:31.616928","level":"info","event":"Processed protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:31.617197","level":"info","event":"Processing protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:32.941859","level":"info","event":"Inserted data for protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:32.941948","level":"info","event":"Processed protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:32.942183","level":"info","event":"Processing protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:34.594019","level":"info","event":"Inserted data for protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:34.594278","level":"info","event":"Processed protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:34.594427","level":"info","event":"Processing protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:36.052053","level":"info","event":"Inserted data for protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:36.052423","level":"info","event":"Processed protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:36.052699","level":"info","event":"Processing protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:37.567313","level":"info","event":"Inserted data for protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:37.567660","level":"info","event":"Processed protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:37.567836","level":"info","event":"Processing protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:39.172913","level":"info","event":"Inserted data for protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:39.172994","level":"info","event":"Processed protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:39.173034","level":"info","event":"Processing protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:40.737257","level":"info","event":"Inserted data for protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:40.737748","level":"info","event":"Processed protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:40.738062","level":"info","event":"Processing protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:42.064948","level":"info","event":"Inserted data for protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:42.065255","level":"info","event":"Processed protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:42.065388","level":"info","event":"Processing protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:43.395594","level":"info","event":"Inserted data for protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:43.395778","level":"info","event":"Processed protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:43.395859","level":"info","event":"Processing protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:45.200454","level":"info","event":"Inserted data for protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:45.200859","level":"info","event":"Processed protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:03:45.201088","level":"info","event":"Processing protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:41.791535","level":"info","event":"Inserted data for protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:41.792021","level":"info","event":"Processed protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:41.792093","level":"info","event":"Processing protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:43.699180","level":"info","event":"Inserted data for protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:43.699767","level":"info","event":"Processed protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:43.700057","level":"info","event":"Processing protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:45.631333","level":"info","event":"Inserted data for protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:45.631511","level":"info","event":"Processed protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:45.631603","level":"info","event":"Processing protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:46.928428","level":"info","event":"Inserted data for protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:46.928574","level":"info","event":"Processed protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:46.928639","level":"info","event":"Processing protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:48.780603","level":"info","event":"Inserted data for protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:48.781022","level":"info","event":"Processed protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:48.781358","level":"info","event":"Processing protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:50.144391","level":"info","event":"Inserted data for protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:50.144711","level":"info","event":"Processed protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:50.144915","level":"info","event":"Processing protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:51.511204","level":"info","event":"Inserted data for protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:51.511592","level":"info","event":"Processed protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:51.511867","level":"info","event":"Processing protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:52.908511","level":"info","event":"Inserted data for protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:52.908924","level":"info","event":"Processed protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:52.909248","level":"info","event":"Processing protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:54.490773","level":"info","event":"Inserted data for protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:54.491351","level":"info","event":"Processed protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:54.491735","level":"info","event":"Processing protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:56.058585","level":"info","event":"Inserted data for protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:56.058822","level":"info","event":"Processed protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:56.058899","level":"info","event":"Processing protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:57.675508","level":"info","event":"Inserted data for protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:57.675911","level":"info","event":"Processed protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T09:04:57.698429","level":"info","event":"Parsed ELN data","logger":"root"}
{"timestamp":"2025-09-05T09:04:57.712370","level":"info","event":"Done. Returned value was: None","logger":"airflow.task.operators.airflow.providers.standard.operators.python.PythonOperator"}
