{"timestamp":"2025-09-05T07:26:06.988038","level":"info","event":"DAG bundles loaded: dags-folder","logger":"airflow.dag_processing.bundles.manager.DagBundlesManager"}
{"timestamp":"2025-09-05T07:26:06.988582","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln_to_mstack_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T07:26:06.995309","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T07:26:07.008618","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T07:26:07.016771","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T07:26:07.103107","level":"error","event":"Task failed with exception","logger":"task","error_detail":[{"exc_type":"TypeError","exc_value":"Variable.get() got an unexpected keyword argument 'default_var'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/sdk/execution_time/task_runner.py","lineno":838,"name":"run"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/sdk/execution_time/task_runner.py","lineno":1125,"name":"_execute_task"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/sdk/bases/operator.py","lineno":408,"name":"wrapper"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/providers/standard/operators/python.py","lineno":212,"name":"execute"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/providers/standard/operators/python.py","lineno":235,"name":"execute_callable"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/sdk/execution_time/callback_runner.py","lineno":81,"name":"run"},{"filename":"/opt/airflow/dags/eln_to_mstack_dag.py","lineno":12,"name":"parse_eln_data"},{"filename":"/opt/airflow/dags/eln/eln_parser.py","lineno":16,"name":"__init__"},{"filename":"/opt/airflow/dags/config/db_config.py","lineno":73,"name":"get_chem_stack_mongo_params"}]}]}
