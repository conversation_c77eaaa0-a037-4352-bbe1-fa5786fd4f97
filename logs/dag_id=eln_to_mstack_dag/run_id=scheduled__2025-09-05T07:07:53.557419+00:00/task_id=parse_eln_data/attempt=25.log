{"timestamp":"2025-09-05T12:13:50.067907","level":"info","event":"DAG bundles loaded: dags-folder","logger":"airflow.dag_processing.bundles.manager.DagBundlesManager"}
{"timestamp":"2025-09-05T12:13:50.068827","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln_to_mstack_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-05T12:13:50.078161","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T12:13:50.087218","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T12:13:50.090319","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T12:13:50.185661","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T12:13:50.199133","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-05T12:13:50.200949","level":"info","event":"Creating or retrieving cached MongoClient for URI ending in ...IdleTimeMS=120000%22","logger":"root"}
{"timestamp":"2025-09-05T12:13:50.204440","level":"warning","event":"maxidletimems must be an integer or float","category":"UserWarning","filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/uri_parser_shared.py","lineno":379,"logger":"py.warnings"}
{"timestamp":"2025-09-05T12:13:50.204534","level":"info","event":"You appear to be connected to a CosmosDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/cosmosdb","logger":"pymongo.client"}
{"timestamp":"2025-09-05T12:13:50.357908","level":"info","event":"You appear to be connected to a CosmosDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/cosmosdb","logger":"pymongo.client"}
{"timestamp":"2025-09-05T12:13:52.741236","level":"info","event":"MongoClient connected and verified successfully.","logger":"root"}
{"timestamp":"2025-09-05T12:13:52.741551","level":"info","event":"MongoOperations initialized for database 'eln' using shared client for ...IdleTimeMS=120000%22","logger":"root"}
{"timestamp":"2025-09-05T12:13:52.741649","level":"info","event":"Getting all protocols from ELN","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:13:54.551428","level":"info","event":"Total protocols: 129","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:13:54.552077","level":"info","event":"Processing protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:13:55.996121","level":"info","event":"Inserted data for protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:13:55.996414","level":"info","event":"Processed protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:13:55.996586","level":"info","event":"Processing protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:13:57.836244","level":"info","event":"Inserted data for protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:13:57.836611","level":"info","event":"Processed protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:13:57.836815","level":"info","event":"Processing protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:13:59.354587","level":"info","event":"Inserted data for protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:13:59.355026","level":"info","event":"Processed protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:13:59.355222","level":"info","event":"Processing protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:00.884110","level":"info","event":"Inserted data for protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:00.884717","level":"info","event":"Processed protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:00.885002","level":"info","event":"Processing protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:02.753379","level":"info","event":"Inserted data for protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:02.753514","level":"info","event":"Processed protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:02.753565","level":"info","event":"Processing protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:04.028573","level":"info","event":"Inserted data for protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:04.029055","level":"info","event":"Processed protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:04.029268","level":"info","event":"Processing protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:05.376713","level":"info","event":"Inserted data for protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:05.377137","level":"info","event":"Processed protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:05.377403","level":"info","event":"Processing protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:06.712485","level":"info","event":"Inserted data for protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:06.713006","level":"info","event":"Processed protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:06.713181","level":"info","event":"Processing protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:08.297822","level":"info","event":"Inserted data for protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:08.297902","level":"info","event":"Processed protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:08.297932","level":"info","event":"Processing protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:09.671825","level":"info","event":"Inserted data for protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:09.671907","level":"info","event":"Processed protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:09.671938","level":"info","event":"Processing protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:11.459113","level":"info","event":"Inserted data for protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:11.459240","level":"info","event":"Processed protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:11.459306","level":"info","event":"Processing protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:12.896095","level":"info","event":"Inserted data for protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:12.896396","level":"info","event":"Processed protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:12.896489","level":"info","event":"Processing protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:14.494928","level":"info","event":"Inserted data for protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:14.495596","level":"info","event":"Processed protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:14.495817","level":"info","event":"Processing protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:15.802231","level":"info","event":"Inserted data for protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:15.804066","level":"info","event":"Processed protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:15.804468","level":"info","event":"Processing protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:17.415489","level":"info","event":"Inserted data for protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:17.415937","level":"info","event":"Processed protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:17.416075","level":"info","event":"Processing protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:18.950810","level":"info","event":"Inserted data for protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:18.950970","level":"info","event":"Processed protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:18.951046","level":"info","event":"Processing protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:20.512995","level":"info","event":"Inserted data for protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:20.513145","level":"info","event":"Processed protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:20.513232","level":"info","event":"Processing protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:22.040762","level":"info","event":"Inserted data for protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:22.040854","level":"info","event":"Processed protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:22.040917","level":"info","event":"Processing protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:23.630332","level":"info","event":"Inserted data for protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:23.630962","level":"info","event":"Processed protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:23.631210","level":"info","event":"Processing protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:25.216378","level":"info","event":"Inserted data for protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:25.216959","level":"info","event":"Processed protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:25.219634","level":"info","event":"Processing protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:26.913405","level":"info","event":"Inserted data for protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:26.914070","level":"info","event":"Processed protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:26.914445","level":"info","event":"Processing protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:28.503535","level":"info","event":"Inserted data for protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:28.503936","level":"info","event":"Processed protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:28.504149","level":"info","event":"Processing protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:29.840043","level":"info","event":"Inserted data for protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:29.840431","level":"info","event":"Processed protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:29.840880","level":"info","event":"Processing protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:31.438958","level":"info","event":"Inserted data for protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:31.439174","level":"info","event":"Processed protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:31.439236","level":"info","event":"Processing protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:33.026986","level":"info","event":"Inserted data for protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:33.027112","level":"info","event":"Processed protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:33.027182","level":"info","event":"Processing protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:34.770868","level":"info","event":"Inserted data for protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:34.771182","level":"info","event":"Processed protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:34.771315","level":"info","event":"Processing protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:36.065034","level":"info","event":"Inserted data for protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:36.065175","level":"info","event":"Processed protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:36.065225","level":"info","event":"Processing protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:37.384498","level":"info","event":"Inserted data for protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:37.384982","level":"info","event":"Processed protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:37.385233","level":"info","event":"Processing protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:38.730908","level":"info","event":"Inserted data for protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:38.731441","level":"info","event":"Processed protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:38.731747","level":"info","event":"Processing protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:40.133499","level":"info","event":"Inserted data for protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:40.133564","level":"info","event":"Processed protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:40.133607","level":"info","event":"Processing protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:41.769972","level":"info","event":"Inserted data for protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:41.770174","level":"info","event":"Processed protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:41.770279","level":"info","event":"Processing protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:43.082592","level":"info","event":"Inserted data for protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:43.082781","level":"info","event":"Processed protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:43.082900","level":"info","event":"Processing protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:44.419847","level":"info","event":"Inserted data for protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:44.419974","level":"info","event":"Processed protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:44.420046","level":"info","event":"Processing protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:45.737322","level":"info","event":"Inserted data for protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:45.737580","level":"info","event":"Processed protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:45.737730","level":"info","event":"Processing protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:47.563644","level":"info","event":"Inserted data for protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:47.563916","level":"info","event":"Processed protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:47.563995","level":"info","event":"Processing protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:49.206242","level":"info","event":"Inserted data for protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:49.206400","level":"info","event":"Processed protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:49.206444","level":"info","event":"Processing protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:50.914295","level":"info","event":"Inserted data for protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:50.914527","level":"info","event":"Processed protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:50.914644","level":"info","event":"Processing protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:52.233431","level":"info","event":"Inserted data for protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:52.233598","level":"info","event":"Processed protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:52.233714","level":"info","event":"Processing protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:53.532373","level":"info","event":"Inserted data for protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:53.532621","level":"info","event":"Processed protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:53.532812","level":"info","event":"Processing protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:55.058695","level":"info","event":"Inserted data for protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:55.058830","level":"info","event":"Processed protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:55.058883","level":"info","event":"Processing protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:56.348033","level":"info","event":"Inserted data for protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:56.348321","level":"info","event":"Processed protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:56.349762","level":"info","event":"Processing protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:58.054879","level":"info","event":"Inserted data for protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:58.055062","level":"info","event":"Processed protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:58.055169","level":"info","event":"Processing protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:59.389860","level":"info","event":"Inserted data for protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:59.390205","level":"info","event":"Processed protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:14:59.390385","level":"info","event":"Processing protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:00.658198","level":"info","event":"Inserted data for protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:00.658433","level":"info","event":"Processed protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:00.658528","level":"info","event":"Processing protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:01.986087","level":"info","event":"Inserted data for protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:01.986408","level":"info","event":"Processed protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:01.986818","level":"info","event":"Processing protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:03.396860","level":"info","event":"Inserted data for protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:03.397238","level":"info","event":"Processed protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:03.397447","level":"info","event":"Processing protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:05.257779","level":"info","event":"Inserted data for protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:05.257887","level":"info","event":"Processed protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:05.257953","level":"info","event":"Processing protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:06.746342","level":"info","event":"Inserted data for protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:06.746490","level":"info","event":"Processed protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:06.746557","level":"info","event":"Processing protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:08.116487","level":"info","event":"Inserted data for protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:08.117284","level":"info","event":"Processed protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:08.117429","level":"info","event":"Processing protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:09.458322","level":"info","event":"Inserted data for protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:09.458629","level":"info","event":"Processed protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:09.458870","level":"info","event":"Processing protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:10.861773","level":"info","event":"Inserted data for protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:10.862219","level":"info","event":"Processed protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:10.862401","level":"info","event":"Processing protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:12.419514","level":"info","event":"Inserted data for protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:12.419590","level":"info","event":"Processed protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:12.419636","level":"info","event":"Processing protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:13.911232","level":"info","event":"Inserted data for protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:13.911434","level":"info","event":"Processed protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:13.911527","level":"info","event":"Processing protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:15.468313","level":"info","event":"Inserted data for protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:15.468668","level":"info","event":"Processed protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:15.469012","level":"info","event":"Processing protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:16.818970","level":"info","event":"Inserted data for protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:16.819330","level":"info","event":"Processed protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:16.819593","level":"info","event":"Processing protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:18.169167","level":"info","event":"Inserted data for protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:18.169274","level":"info","event":"Processed protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:18.169336","level":"info","event":"Processing protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:19.755621","level":"info","event":"Inserted data for protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:19.756172","level":"info","event":"Processed protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:19.756463","level":"info","event":"Processing protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:21.103068","level":"info","event":"Inserted data for protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:21.103524","level":"info","event":"Processed protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:21.103973","level":"info","event":"Processing protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:22.441433","level":"info","event":"Inserted data for protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:22.442026","level":"info","event":"Processed protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:22.442154","level":"info","event":"Processing protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:23.762916","level":"info","event":"Inserted data for protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:23.763115","level":"info","event":"Processed protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:23.763232","level":"info","event":"Processing protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:25.287756","level":"info","event":"Inserted data for protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:25.288112","level":"info","event":"Processed protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:25.288481","level":"info","event":"Processing protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:26.726858","level":"info","event":"Inserted data for protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:26.727137","level":"info","event":"Processed protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:26.727313","level":"info","event":"Processing protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:28.018331","level":"info","event":"Inserted data for protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:28.018425","level":"info","event":"Processed protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:28.018478","level":"info","event":"Processing protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:29.592719","level":"info","event":"Inserted data for protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:29.593159","level":"info","event":"Processed protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:29.593483","level":"info","event":"Processing protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:31.024964","level":"info","event":"Inserted data for protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:31.025304","level":"info","event":"Processed protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:31.025554","level":"info","event":"Processing protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:32.331310","level":"info","event":"Inserted data for protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:32.331589","level":"info","event":"Processed protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:32.331808","level":"info","event":"Processing protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:33.634975","level":"info","event":"Inserted data for protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:33.635313","level":"info","event":"Processed protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:33.635631","level":"info","event":"Processing protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:35.369980","level":"info","event":"Inserted data for protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:35.370180","level":"info","event":"Processed protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:35.370249","level":"info","event":"Processing protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:36.918375","level":"info","event":"Inserted data for protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:36.918666","level":"info","event":"Processed protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:36.920821","level":"info","event":"Processing protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:38.515592","level":"info","event":"Inserted data for protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:38.515935","level":"info","event":"Processed protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:38.516152","level":"info","event":"Processing protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:39.846952","level":"info","event":"Inserted data for protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:39.847287","level":"info","event":"Processed protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:39.847423","level":"info","event":"Processing protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:41.174149","level":"info","event":"Inserted data for protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:41.174339","level":"info","event":"Processed protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:41.174422","level":"info","event":"Processing protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:42.711851","level":"info","event":"Inserted data for protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:42.712202","level":"info","event":"Processed protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:42.712377","level":"info","event":"Processing protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:44.268515","level":"info","event":"Inserted data for protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:44.268732","level":"info","event":"Processed protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:44.268839","level":"info","event":"Processing protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:45.536311","level":"info","event":"Inserted data for protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:45.536402","level":"info","event":"Processed protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:45.536455","level":"info","event":"Processing protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:47.034164","level":"info","event":"Inserted data for protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:47.034422","level":"info","event":"Processed protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:47.034590","level":"info","event":"Processing protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:48.341426","level":"info","event":"Inserted data for protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:48.341688","level":"info","event":"Processed protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:48.341766","level":"info","event":"Processing protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:49.639043","level":"info","event":"Inserted data for protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:49.639776","level":"info","event":"Processed protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:49.640163","level":"info","event":"Processing protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:51.182947","level":"info","event":"Inserted data for protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:51.183176","level":"info","event":"Processed protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:51.183327","level":"info","event":"Processing protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:52.490097","level":"info","event":"Inserted data for protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:52.490466","level":"info","event":"Processed protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:52.490729","level":"info","event":"Processing protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:53.858368","level":"info","event":"Inserted data for protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:53.858766","level":"info","event":"Processed protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:53.858966","level":"info","event":"Processing protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:55.145900","level":"info","event":"Inserted data for protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:55.146008","level":"info","event":"Processed protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:55.146084","level":"info","event":"Processing protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:56.671467","level":"info","event":"Inserted data for protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:56.671821","level":"info","event":"Processed protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:56.671989","level":"info","event":"Processing protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:57.962219","level":"info","event":"Inserted data for protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:57.962367","level":"info","event":"Processed protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:57.962462","level":"info","event":"Processing protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:59.545192","level":"info","event":"Inserted data for protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:59.545326","level":"info","event":"Processed protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:15:59.545441","level":"info","event":"Processing protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:00.806596","level":"info","event":"Inserted data for protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:00.807009","level":"info","event":"Processed protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:00.807178","level":"info","event":"Processing protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:02.095015","level":"info","event":"Inserted data for protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:02.095359","level":"info","event":"Processed protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:02.095572","level":"info","event":"Processing protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:03.676595","level":"info","event":"Inserted data for protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:03.676745","level":"info","event":"Processed protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:03.676817","level":"info","event":"Processing protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:05.014754","level":"info","event":"Inserted data for protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:05.015084","level":"info","event":"Processed protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:05.015331","level":"info","event":"Processing protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:06.583469","level":"info","event":"Inserted data for protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:06.583849","level":"info","event":"Processed protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:06.583988","level":"info","event":"Processing protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:07.919492","level":"info","event":"Inserted data for protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:07.919916","level":"info","event":"Processed protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:07.920161","level":"info","event":"Processing protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:09.302284","level":"info","event":"Inserted data for protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:09.302746","level":"info","event":"Processed protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:09.302928","level":"info","event":"Processing protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:10.848939","level":"info","event":"Inserted data for protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:10.849083","level":"info","event":"Processed protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:10.849172","level":"info","event":"Processing protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:12.172292","level":"info","event":"Inserted data for protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:12.172360","level":"info","event":"Processed protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:12.172383","level":"info","event":"Processing protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:13.456240","level":"info","event":"Inserted data for protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:13.456579","level":"info","event":"Processed protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:13.456760","level":"info","event":"Processing protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:14.762162","level":"info","event":"Inserted data for protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:14.762455","level":"info","event":"Processed protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:14.762582","level":"info","event":"Processing protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:16.301406","level":"info","event":"Inserted data for protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:16.301725","level":"info","event":"Processed protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:16.301891","level":"info","event":"Processing protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:17.576344","level":"info","event":"Inserted data for protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:17.576416","level":"info","event":"Processed protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:17.576438","level":"info","event":"Processing protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:18.848772","level":"info","event":"Inserted data for protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:18.849104","level":"info","event":"Processed protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:18.849312","level":"info","event":"Processing protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:20.165792","level":"info","event":"Inserted data for protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:20.166217","level":"info","event":"Processed protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:20.166448","level":"info","event":"Processing protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:21.957389","level":"info","event":"Inserted data for protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:21.957758","level":"info","event":"Processed protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:21.958019","level":"info","event":"Processing protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:23.815484","level":"info","event":"Inserted data for protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:23.815843","level":"info","event":"Processed protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:23.816041","level":"info","event":"Processing protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:25.413808","level":"info","event":"Inserted data for protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:25.414152","level":"info","event":"Processed protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:25.414337","level":"info","event":"Processing protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:26.720243","level":"info","event":"Inserted data for protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:26.720643","level":"info","event":"Processed protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:26.720877","level":"info","event":"Processing protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:28.021956","level":"info","event":"Inserted data for protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:28.022206","level":"info","event":"Processed protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:28.022338","level":"info","event":"Processing protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:29.356348","level":"info","event":"Inserted data for protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:29.356601","level":"info","event":"Processed protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:29.356841","level":"info","event":"Processing protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:30.699460","level":"info","event":"Inserted data for protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:30.699870","level":"info","event":"Processed protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:30.700046","level":"info","event":"Processing protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:32.008074","level":"info","event":"Inserted data for protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:32.008381","level":"info","event":"Processed protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:32.008549","level":"info","event":"Processing protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:33.601885","level":"info","event":"Inserted data for protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:33.602212","level":"info","event":"Processed protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:33.602415","level":"info","event":"Processing protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:34.970118","level":"info","event":"Inserted data for protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:34.970273","level":"info","event":"Processed protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:34.970519","level":"info","event":"Processing protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:36.663928","level":"info","event":"Inserted data for protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:36.664220","level":"info","event":"Processed protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:36.664297","level":"info","event":"Processing protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:37.991032","level":"info","event":"Inserted data for protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:37.991321","level":"info","event":"Processed protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:37.991482","level":"info","event":"Processing protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:39.326558","level":"info","event":"Inserted data for protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:39.326970","level":"info","event":"Processed protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:39.327113","level":"info","event":"Processing protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:40.965944","level":"info","event":"Inserted data for protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:40.966528","level":"info","event":"Processed protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:40.967344","level":"info","event":"Processing protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:42.551037","level":"info","event":"Inserted data for protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:42.551243","level":"info","event":"Processed protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:42.551354","level":"info","event":"Processing protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:43.911291","level":"info","event":"Inserted data for protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:43.911589","level":"info","event":"Processed protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:43.911727","level":"info","event":"Processing protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:45.251109","level":"info","event":"Inserted data for protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:45.251504","level":"info","event":"Processed protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:45.251600","level":"info","event":"Processing protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:47.033168","level":"info","event":"Inserted data for protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:47.033516","level":"info","event":"Processed protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:47.033782","level":"info","event":"Processing protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:48.366311","level":"info","event":"Inserted data for protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:48.366742","level":"info","event":"Processed protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:48.366965","level":"info","event":"Processing protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:49.668045","level":"info","event":"Inserted data for protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:49.668144","level":"info","event":"Processed protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:49.668189","level":"info","event":"Processing protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:51.483387","level":"info","event":"Inserted data for protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:51.483841","level":"info","event":"Processed protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:51.484118","level":"info","event":"Processing protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:52.804415","level":"info","event":"Inserted data for protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:52.804573","level":"info","event":"Processed protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:52.804641","level":"info","event":"Processing protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:54.580117","level":"info","event":"Inserted data for protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:54.580466","level":"info","event":"Processed protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:54.580740","level":"info","event":"Processing protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:55.961498","level":"info","event":"Inserted data for protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:55.962065","level":"info","event":"Processed protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:55.962435","level":"info","event":"Processing protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:57.258322","level":"info","event":"Inserted data for protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:57.258796","level":"info","event":"Processed protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:57.259041","level":"info","event":"Processing protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:58.573455","level":"info","event":"Inserted data for protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:58.573724","level":"info","event":"Processed protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:16:58.573915","level":"info","event":"Processing protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:17:00.320508","level":"info","event":"Inserted data for protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:17:00.320946","level":"info","event":"Processed protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:17:00.321200","level":"info","event":"Processing protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:17:01.854190","level":"info","event":"Inserted data for protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:17:01.854551","level":"info","event":"Processed protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:17:01.854716","level":"info","event":"Processing protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:17:03.401332","level":"info","event":"Inserted data for protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:17:03.401647","level":"info","event":"Processed protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-05T12:17:03.421799","level":"info","event":"Parsed ELN data","logger":"root"}
{"timestamp":"2025-09-05T12:17:03.430760","level":"info","event":"Done. Returned value was: None","logger":"airflow.task.operators.airflow.providers.standard.operators.python.PythonOperator"}
