{"timestamp":"2025-09-08T06:58:17.001504","level":"info","event":"DAG bundles loaded: dags-folder","logger":"airflow.dag_processing.bundles.manager.DagBundlesManager"}
{"timestamp":"2025-09-08T06:58:17.003958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/eln_to_mstack_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-09-08T06:58:17.015353","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:58:17.029143","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:58:17.032750","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:58:17.124701","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:58:17.160401","level":"info","event":"Secrets backends loaded for worker","count":1,"backend_classes":["EnvironmentVariablesBackend"],"logger":"supervisor"}
{"timestamp":"2025-09-08T06:58:17.165228","level":"info","event":"Creating or retrieving cached MongoClient for URI ending in ...IdleTimeMS=120000%22","logger":"root"}
{"timestamp":"2025-09-08T06:58:17.168750","level":"warning","event":"maxidletimems must be an integer or float","category":"UserWarning","filename":"/home/<USER>/.local/lib/python3.12/site-packages/pymongo/uri_parser_shared.py","lineno":379,"logger":"py.warnings"}
{"timestamp":"2025-09-08T06:58:17.168888","level":"info","event":"You appear to be connected to a CosmosDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/cosmosdb","logger":"pymongo.client"}
{"timestamp":"2025-09-08T06:58:17.425295","level":"info","event":"You appear to be connected to a CosmosDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/cosmosdb","logger":"pymongo.client"}
{"timestamp":"2025-09-08T06:58:20.441540","level":"info","event":"MongoClient connected and verified successfully.","logger":"root"}
{"timestamp":"2025-09-08T06:58:20.442453","level":"info","event":"MongoOperations initialized for database 'eln' using shared client for ...IdleTimeMS=120000%22","logger":"root"}
{"timestamp":"2025-09-08T06:58:20.442660","level":"info","event":"Getting all protocols from ELN","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:23.155906","level":"info","event":"Total protocols: 309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:23.156512","level":"info","event":"Processing protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:24.609818","level":"info","event":"Inserted data for protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:24.610225","level":"info","event":"Processed protocol: 1000340","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:24.610453","level":"info","event":"Processing protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:26.588021","level":"info","event":"Inserted data for protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:26.588109","level":"info","event":"Processed protocol: 1000339","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:26.588146","level":"info","event":"Processing protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:28.205036","level":"info","event":"Inserted data for protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:28.205291","level":"info","event":"Processed protocol: 1000338","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:28.205389","level":"info","event":"Processing protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:29.855174","level":"info","event":"Inserted data for protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:29.855594","level":"info","event":"Processed protocol: 1000337","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:29.855680","level":"info","event":"Processing protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:32.072184","level":"info","event":"Inserted data for protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:32.072517","level":"info","event":"Processed protocol: 1000336","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:32.072724","level":"info","event":"Processing protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:34.015119","level":"info","event":"Inserted data for protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:34.015501","level":"info","event":"Processed protocol: 1000335","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:34.015675","level":"info","event":"Processing protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:35.654140","level":"info","event":"Inserted data for protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:35.654414","level":"info","event":"Processed protocol: 1000334","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:35.654688","level":"info","event":"Processing protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:37.026928","level":"info","event":"Inserted data for protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:37.027037","level":"info","event":"Processed protocol: 1000333","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:37.027077","level":"info","event":"Processing protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:38.630748","level":"info","event":"Inserted data for protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:38.631266","level":"info","event":"Processed protocol: 1000332","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:38.631515","level":"info","event":"Processing protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:40.083037","level":"info","event":"Inserted data for protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:40.083114","level":"info","event":"Processed protocol: 1000331","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:40.083152","level":"info","event":"Processing protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:41.738571","level":"info","event":"Inserted data for protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:41.738960","level":"info","event":"Processed protocol: 1000330","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:41.739200","level":"info","event":"Processing protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:43.125971","level":"info","event":"Inserted data for protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:43.126354","level":"info","event":"Processed protocol: 1000329","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:43.126490","level":"info","event":"Processing protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:44.810027","level":"info","event":"Inserted data for protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:44.810571","level":"info","event":"Processed protocol: 1000328","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:44.810842","level":"info","event":"Processing protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:46.232089","level":"info","event":"Inserted data for protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:46.232566","level":"info","event":"Processed protocol: 1000327","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:46.232802","level":"info","event":"Processing protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:48.097318","level":"info","event":"Inserted data for protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:48.097397","level":"info","event":"Processed protocol: 1000326","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:48.097419","level":"info","event":"Processing protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:49.771072","level":"info","event":"Inserted data for protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:49.771423","level":"info","event":"Processed protocol: 1000325","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:49.771638","level":"info","event":"Processing protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:51.710460","level":"info","event":"Inserted data for protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:51.712106","level":"info","event":"Processed protocol: 1000324","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:51.712231","level":"info","event":"Processing protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:53.361528","level":"info","event":"Inserted data for protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:53.361935","level":"info","event":"Processed protocol: 1000323","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:53.362187","level":"info","event":"Processing protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:54.967682","level":"info","event":"Inserted data for protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:54.967776","level":"info","event":"Processed protocol: 1000322","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:54.967833","level":"info","event":"Processing protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:56.767973","level":"info","event":"Inserted data for protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:56.768123","level":"info","event":"Processed protocol: 1000321","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:56.768218","level":"info","event":"Processing protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:58.583073","level":"info","event":"Inserted data for protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:58.583163","level":"info","event":"Processed protocol: 1000320","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:58:58.583213","level":"info","event":"Processing protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:00.189754","level":"info","event":"Inserted data for protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:00.190289","level":"info","event":"Processed protocol: 1000319","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:00.190555","level":"info","event":"Processing protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:01.578057","level":"info","event":"Inserted data for protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:01.578406","level":"info","event":"Processed protocol: 1000318","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:01.578618","level":"info","event":"Processing protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:03.173303","level":"info","event":"Inserted data for protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:03.173425","level":"info","event":"Processed protocol: 1000317","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:03.173509","level":"info","event":"Processing protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:04.960314","level":"info","event":"Inserted data for protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:04.960708","level":"info","event":"Processed protocol: 1000316","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:04.960865","level":"info","event":"Processing protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:06.988580","level":"info","event":"Inserted data for protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:06.988657","level":"info","event":"Processed protocol: 1000315","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:06.988702","level":"info","event":"Processing protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:08.509179","level":"info","event":"Inserted data for protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:08.509555","level":"info","event":"Processed protocol: 1000314","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:08.509833","level":"info","event":"Processing protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:09.964500","level":"info","event":"Inserted data for protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:09.964678","level":"info","event":"Processed protocol: 1000313","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:09.964787","level":"info","event":"Processing protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:11.453121","level":"info","event":"Inserted data for protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:11.453199","level":"info","event":"Processed protocol: 1000312","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:11.453234","level":"info","event":"Processing protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:12.842929","level":"info","event":"Inserted data for protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:12.843380","level":"info","event":"Processed protocol: 1000311","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:12.843563","level":"info","event":"Processing protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:14.517420","level":"info","event":"Inserted data for protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:14.517713","level":"info","event":"Processed protocol: 1000310","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:14.517924","level":"info","event":"Processing protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:15.872871","level":"info","event":"Inserted data for protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:15.875394","level":"info","event":"Processed protocol: 1000309","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:15.875818","level":"info","event":"Processing protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:17.291508","level":"info","event":"Inserted data for protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:17.291605","level":"info","event":"Processed protocol: 1000308","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:17.291791","level":"info","event":"Processing protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:18.688264","level":"info","event":"Inserted data for protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:18.688594","level":"info","event":"Processed protocol: 1000307","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:18.688806","level":"info","event":"Processing protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:20.981285","level":"info","event":"Inserted data for protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:20.981588","level":"info","event":"Processed protocol: 1000306","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:20.981847","level":"info","event":"Processing protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:22.668665","level":"info","event":"Inserted data for protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:22.669031","level":"info","event":"Processed protocol: 1000305","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:22.669267","level":"info","event":"Processing protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:24.308942","level":"info","event":"Inserted data for protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:24.309423","level":"info","event":"Processed protocol: 1000304","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:24.309673","level":"info","event":"Processing protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:25.762501","level":"info","event":"Inserted data for protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:25.762933","level":"info","event":"Processed protocol: 1000303","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:25.763106","level":"info","event":"Processing protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:27.403379","level":"info","event":"Inserted data for protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:27.403453","level":"info","event":"Processed protocol: 1000302","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:27.403511","level":"info","event":"Processing protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:29.242179","level":"info","event":"Inserted data for protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:29.243166","level":"info","event":"Processed protocol: 1000301","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:29.243443","level":"info","event":"Processing protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:30.596625","level":"info","event":"Inserted data for protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:30.596848","level":"info","event":"Processed protocol: 1000300","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:30.596935","level":"info","event":"Processing protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:32.476045","level":"info","event":"Inserted data for protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:32.476250","level":"info","event":"Processed protocol: 1000299","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:32.476512","level":"info","event":"Processing protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:33.861288","level":"info","event":"Inserted data for protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:33.861466","level":"info","event":"Processed protocol: 1000298","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:33.861561","level":"info","event":"Processing protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:35.305927","level":"info","event":"Inserted data for protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:35.306417","level":"info","event":"Processed protocol: 1000297","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:35.306775","level":"info","event":"Processing protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:36.679227","level":"info","event":"Inserted data for protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:36.679392","level":"info","event":"Processed protocol: 1000296","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:36.679477","level":"info","event":"Processing protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:38.058135","level":"info","event":"Inserted data for protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:38.058361","level":"info","event":"Processed protocol: 1000295","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:38.058487","level":"info","event":"Processing protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:40.016802","level":"info","event":"Inserted data for protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:40.018673","level":"info","event":"Processed protocol: 1000294","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:40.019320","level":"info","event":"Processing protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:41.824423","level":"info","event":"Inserted data for protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:41.824772","level":"info","event":"Processed protocol: 1000293","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:41.824972","level":"info","event":"Processing protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:43.203566","level":"info","event":"Inserted data for protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:43.203636","level":"info","event":"Processed protocol: 1000292","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:43.203682","level":"info","event":"Processing protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:44.554597","level":"info","event":"Inserted data for protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:44.554674","level":"info","event":"Processed protocol: 1000291","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:44.554754","level":"info","event":"Processing protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:45.928487","level":"info","event":"Inserted data for protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:45.928714","level":"info","event":"Processed protocol: 1000290","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:45.928830","level":"info","event":"Processing protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:47.996635","level":"info","event":"Inserted data for protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:47.996753","level":"info","event":"Processed protocol: 1000289","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:47.996813","level":"info","event":"Processing protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:50.001076","level":"info","event":"Inserted data for protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:50.001584","level":"info","event":"Processed protocol: 1000288","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:50.001788","level":"info","event":"Processing protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:51.641443","level":"info","event":"Inserted data for protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:51.641748","level":"info","event":"Processed protocol: 1000287","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:51.641964","level":"info","event":"Processing protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:53.013882","level":"info","event":"Inserted data for protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:53.014118","level":"info","event":"Processed protocol: 1000286","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:53.014223","level":"info","event":"Processing protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:54.483301","level":"info","event":"Inserted data for protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:54.485149","level":"info","event":"Processed protocol: 1000285","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:54.490365","level":"info","event":"Processing protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:56.516496","level":"info","event":"Inserted data for protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:56.517232","level":"info","event":"Processed protocol: 1000284","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:56.517583","level":"info","event":"Processing protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:57.970138","level":"info","event":"Inserted data for protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:57.970231","level":"info","event":"Processed protocol: 1000283","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:57.970279","level":"info","event":"Processing protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:59.331580","level":"info","event":"Inserted data for protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:59.331945","level":"info","event":"Processed protocol: 1000282","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T06:59:59.332337","level":"info","event":"Processing protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:00.737505","level":"info","event":"Inserted data for protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:00.737661","level":"info","event":"Processed protocol: 1000281","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:00.737720","level":"info","event":"Processing protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:02.443178","level":"info","event":"Inserted data for protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:02.443420","level":"info","event":"Processed protocol: 1000280","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:02.443551","level":"info","event":"Processing protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:03.828490","level":"info","event":"Inserted data for protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:03.828678","level":"info","event":"Processed protocol: 1000279","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:03.828809","level":"info","event":"Processing protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:05.426732","level":"info","event":"Inserted data for protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:05.428428","level":"info","event":"Processed protocol: 1000278","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:05.428892","level":"info","event":"Processing protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:07.163513","level":"info","event":"Inserted data for protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:07.163878","level":"info","event":"Processed protocol: 1000277","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:07.164136","level":"info","event":"Processing protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:08.679145","level":"info","event":"Inserted data for protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:08.679268","level":"info","event":"Processed protocol: 1000276","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:08.679372","level":"info","event":"Processing protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:10.050143","level":"info","event":"Inserted data for protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:10.050724","level":"info","event":"Processed protocol: 1000275","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:10.051044","level":"info","event":"Processing protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:11.481920","level":"info","event":"Inserted data for protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:11.482181","level":"info","event":"Processed protocol: 1000274","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:11.482289","level":"info","event":"Processing protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:13.119522","level":"info","event":"Inserted data for protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:13.119706","level":"info","event":"Processed protocol: 1000273","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:13.119775","level":"info","event":"Processing protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:14.824589","level":"info","event":"Inserted data for protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:14.824684","level":"info","event":"Processed protocol: 1000272","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:14.824823","level":"info","event":"Processing protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:16.413831","level":"info","event":"Inserted data for protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:16.414080","level":"info","event":"Processed protocol: 1000271","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:16.414148","level":"info","event":"Processing protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:17.816025","level":"info","event":"Inserted data for protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:17.816271","level":"info","event":"Processed protocol: 1000270","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:17.816456","level":"info","event":"Processing protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:19.204060","level":"info","event":"Inserted data for protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:19.204147","level":"info","event":"Processed protocol: 1000269","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:19.204186","level":"info","event":"Processing protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:20.874159","level":"info","event":"Inserted data for protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:20.874421","level":"info","event":"Processed protocol: 1000268","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:20.874614","level":"info","event":"Processing protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:23.074214","level":"info","event":"Inserted data for protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:23.074612","level":"info","event":"Processed protocol: 1000267","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:23.074794","level":"info","event":"Processing protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:24.752395","level":"info","event":"Inserted data for protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:24.753112","level":"info","event":"Processed protocol: 1000266","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:24.753361","level":"info","event":"Processing protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:27.163880","level":"info","event":"Inserted data for protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:27.164306","level":"info","event":"Processed protocol: 1000265","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:27.164580","level":"info","event":"Processing protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:28.823461","level":"info","event":"Inserted data for protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:28.824342","level":"info","event":"Processed protocol: 1000264","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:28.824733","level":"info","event":"Processing protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:30.226637","level":"info","event":"Inserted data for protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:30.227160","level":"info","event":"Processed protocol: 1000263","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:30.229795","level":"info","event":"Processing protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:31.863374","level":"info","event":"Inserted data for protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:31.863452","level":"info","event":"Processed protocol: 1000262","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:31.863482","level":"info","event":"Processing protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:33.280426","level":"info","event":"Inserted data for protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:33.280861","level":"info","event":"Processed protocol: 1000261","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:33.280916","level":"info","event":"Processing protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:34.651441","level":"info","event":"Inserted data for protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:34.651788","level":"info","event":"Processed protocol: 1000260","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:34.651947","level":"info","event":"Processing protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:36.085380","level":"info","event":"Inserted data for protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:36.085731","level":"info","event":"Processed protocol: 1000259","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:36.085975","level":"info","event":"Processing protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:37.853078","level":"info","event":"Inserted data for protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:37.853158","level":"info","event":"Processed protocol: 1000258","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:37.853204","level":"info","event":"Processing protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:39.221608","level":"info","event":"Inserted data for protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:39.221692","level":"info","event":"Processed protocol: 1000257","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:39.221733","level":"info","event":"Processing protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:41.038593","level":"info","event":"Inserted data for protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:41.038807","level":"info","event":"Processed protocol: 1000256","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:41.038914","level":"info","event":"Processing protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:42.390487","level":"info","event":"Inserted data for protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:42.390867","level":"info","event":"Processed protocol: 1000255","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:42.391169","level":"info","event":"Processing protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:43.795482","level":"info","event":"Inserted data for protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:43.795771","level":"info","event":"Processed protocol: 1000254","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:43.795960","level":"info","event":"Processing protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:45.400887","level":"info","event":"Inserted data for protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:45.401316","level":"info","event":"Processed protocol: 1000253","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:45.401599","level":"info","event":"Processing protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:46.803822","level":"info","event":"Inserted data for protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:46.803888","level":"info","event":"Processed protocol: 1000252","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:46.803924","level":"info","event":"Processing protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:48.414497","level":"info","event":"Inserted data for protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:48.414580","level":"info","event":"Processed protocol: 1000251","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:48.414633","level":"info","event":"Processing protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:49.828169","level":"info","event":"Inserted data for protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:49.828268","level":"info","event":"Processed protocol: 1000250","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:49.828324","level":"info","event":"Processing protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:51.239958","level":"info","event":"Inserted data for protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:51.240343","level":"info","event":"Processed protocol: 1000249","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:51.240534","level":"info","event":"Processing protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:52.895352","level":"info","event":"Inserted data for protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:52.895441","level":"info","event":"Processed protocol: 1000248","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:52.895491","level":"info","event":"Processing protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:54.266350","level":"info","event":"Inserted data for protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:54.266910","level":"info","event":"Processed protocol: 1000247","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:54.267128","level":"info","event":"Processing protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:55.655257","level":"info","event":"Inserted data for protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:55.655473","level":"info","event":"Processed protocol: 1000246","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:55.655696","level":"info","event":"Processing protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:57.026079","level":"info","event":"Inserted data for protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:57.026152","level":"info","event":"Processed protocol: 1000245","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:57.026234","level":"info","event":"Processing protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:58.666652","level":"info","event":"Inserted data for protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:58.666942","level":"info","event":"Processed protocol: 1000244","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:00:58.668815","level":"info","event":"Processing protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:00.091315","level":"info","event":"Inserted data for protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:00.091414","level":"info","event":"Processed protocol: 1000243","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:00.091463","level":"info","event":"Processing protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:01.655632","level":"info","event":"Inserted data for protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:01.655843","level":"info","event":"Processed protocol: 1000242","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:01.655968","level":"info","event":"Processing protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:03.031299","level":"info","event":"Inserted data for protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:03.031620","level":"info","event":"Processed protocol: 1000241","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:03.031757","level":"info","event":"Processing protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:04.897668","level":"info","event":"Inserted data for protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:04.897917","level":"info","event":"Processed protocol: 1000240","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:04.898124","level":"info","event":"Processing protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:06.802309","level":"info","event":"Inserted data for protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:06.802789","level":"info","event":"Processed protocol: 1000239","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:06.803104","level":"info","event":"Processing protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:08.490485","level":"info","event":"Inserted data for protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:08.490828","level":"info","event":"Processed protocol: 1000238","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:08.491058","level":"info","event":"Processing protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:09.864715","level":"info","event":"Inserted data for protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:09.864786","level":"info","event":"Processed protocol: 1000237","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:09.864828","level":"info","event":"Processing protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:11.271467","level":"info","event":"Inserted data for protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:11.272222","level":"info","event":"Processed protocol: 1000236","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:11.272517","level":"info","event":"Processing protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:12.663718","level":"info","event":"Inserted data for protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:12.663879","level":"info","event":"Processed protocol: 1000235","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:12.663974","level":"info","event":"Processing protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:14.028560","level":"info","event":"Inserted data for protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:14.028884","level":"info","event":"Processed protocol: 1000234","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:14.029134","level":"info","event":"Processing protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:15.402380","level":"info","event":"Inserted data for protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:15.402647","level":"info","event":"Processed protocol: 1000233","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:15.402842","level":"info","event":"Processing protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:17.193906","level":"info","event":"Inserted data for protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:17.193999","level":"info","event":"Processed protocol: 1000232","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:17.194051","level":"info","event":"Processing protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:18.611911","level":"info","event":"Inserted data for protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:18.612273","level":"info","event":"Processed protocol: 1000231","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:18.612490","level":"info","event":"Processing protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:20.225193","level":"info","event":"Inserted data for protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:20.225327","level":"info","event":"Processed protocol: 1000230","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:20.225660","level":"info","event":"Processing protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:21.646274","level":"info","event":"Inserted data for protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:21.647180","level":"info","event":"Processed protocol: 1000229","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:21.647429","level":"info","event":"Processing protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:23.029178","level":"info","event":"Inserted data for protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:23.029394","level":"info","event":"Processed protocol: 1000228","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:23.029473","level":"info","event":"Processing protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:24.688760","level":"info","event":"Inserted data for protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:24.689044","level":"info","event":"Processed protocol: 1000227","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:24.689181","level":"info","event":"Processing protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:26.474256","level":"info","event":"Inserted data for protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:26.474362","level":"info","event":"Processed protocol: 1000226","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:26.474430","level":"info","event":"Processing protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:27.823230","level":"info","event":"Inserted data for protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:27.823314","level":"info","event":"Processed protocol: 1000225","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:27.823406","level":"info","event":"Processing protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:29.206800","level":"info","event":"Inserted data for protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:29.207434","level":"info","event":"Processed protocol: 1000224","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:29.207816","level":"info","event":"Processing protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:31.001563","level":"info","event":"Inserted data for protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:31.001747","level":"info","event":"Processed protocol: 1000223","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:31.001807","level":"info","event":"Processing protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:32.440482","level":"info","event":"Inserted data for protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:32.440695","level":"info","event":"Processed protocol: 1000222","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:32.442477","level":"info","event":"Processing protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:33.792626","level":"info","event":"Inserted data for protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:33.792885","level":"info","event":"Processed protocol: 1000221","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:33.793097","level":"info","event":"Processing protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:35.760269","level":"info","event":"Inserted data for protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:35.760480","level":"info","event":"Processed protocol: 1000220","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:35.760693","level":"info","event":"Processing protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:37.115655","level":"info","event":"Inserted data for protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:37.115737","level":"info","event":"Processed protocol: 1000219","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:37.115764","level":"info","event":"Processing protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:38.961585","level":"info","event":"Inserted data for protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:38.961751","level":"info","event":"Processed protocol: 1000218","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:38.961863","level":"info","event":"Processing protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:40.337915","level":"info","event":"Inserted data for protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:40.338248","level":"info","event":"Processed protocol: 1000217","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:40.338508","level":"info","event":"Processing protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:41.870620","level":"info","event":"Inserted data for protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:41.870715","level":"info","event":"Processed protocol: 1000216","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:41.870758","level":"info","event":"Processing protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:43.265828","level":"info","event":"Inserted data for protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:43.266255","level":"info","event":"Processed protocol: 1000183","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:43.266452","level":"info","event":"Processing protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:45.005783","level":"info","event":"Inserted data for protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:45.006163","level":"info","event":"Processed protocol: 1000182","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:45.006413","level":"info","event":"Processing protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:46.718619","level":"info","event":"Inserted data for protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:46.718970","level":"info","event":"Processed protocol: 1000181","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:46.719213","level":"info","event":"Processing protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:48.334070","level":"info","event":"Inserted data for protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:48.334248","level":"info","event":"Processed protocol: 1000180","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:48.334388","level":"info","event":"Processing protocol: 1000179","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:49.662542","level":"info","event":"Inserted data for protocol: 1000179","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:49.662916","level":"info","event":"Processed protocol: 1000179","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:49.663084","level":"info","event":"Processing protocol: 1000178","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:51.039537","level":"info","event":"Inserted data for protocol: 1000178","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:51.039609","level":"info","event":"Processed protocol: 1000178","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:51.039660","level":"info","event":"Processing protocol: 1000177","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:52.644596","level":"info","event":"Inserted data for protocol: 1000177","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:52.644933","level":"info","event":"Processed protocol: 1000177","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:52.645207","level":"info","event":"Processing protocol: 1000176","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:54.037259","level":"info","event":"Inserted data for protocol: 1000176","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:54.038331","level":"info","event":"Processed protocol: 1000176","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:54.038634","level":"info","event":"Processing protocol: 1000175","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:55.460407","level":"info","event":"Inserted data for protocol: 1000175","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:55.460852","level":"info","event":"Processed protocol: 1000175","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:55.461114","level":"info","event":"Processing protocol: 1000174","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:57.342636","level":"info","event":"Inserted data for protocol: 1000174","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:57.342952","level":"info","event":"Processed protocol: 1000174","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:57.343216","level":"info","event":"Processing protocol: 1000173","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:58.959854","level":"info","event":"Inserted data for protocol: 1000173","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:58.960189","level":"info","event":"Processed protocol: 1000173","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:01:58.960339","level":"info","event":"Processing protocol: 1000172","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:00.572706","level":"info","event":"Inserted data for protocol: 1000172","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:00.573117","level":"info","event":"Processed protocol: 1000172","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:00.573360","level":"info","event":"Processing protocol: 1000171","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:02.195668","level":"info","event":"Inserted data for protocol: 1000171","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:02.196054","level":"info","event":"Processed protocol: 1000171","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:02.196309","level":"info","event":"Processing protocol: 1000170","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:03.992748","level":"info","event":"Inserted data for protocol: 1000170","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:03.994241","level":"info","event":"Processed protocol: 1000170","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:03.994326","level":"info","event":"Processing protocol: 1000169","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:05.370355","level":"info","event":"Inserted data for protocol: 1000169","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:05.370552","level":"info","event":"Processed protocol: 1000169","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:05.370655","level":"info","event":"Processing protocol: 1000168","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:06.711825","level":"info","event":"Inserted data for protocol: 1000168","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:06.712277","level":"info","event":"Processed protocol: 1000168","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:06.712433","level":"info","event":"Processing protocol: 1000167","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:08.067454","level":"info","event":"Inserted data for protocol: 1000167","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:08.068174","level":"info","event":"Processed protocol: 1000167","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:08.068576","level":"info","event":"Processing protocol: 1000166","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:09.736068","level":"info","event":"Inserted data for protocol: 1000166","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:09.736462","level":"info","event":"Processed protocol: 1000166","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:09.737170","level":"info","event":"Processing protocol: 1000165","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:11.133058","level":"info","event":"Inserted data for protocol: 1000165","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:11.133150","level":"info","event":"Processed protocol: 1000165","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:11.133204","level":"info","event":"Processing protocol: 1000164","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:12.488462","level":"info","event":"Inserted data for protocol: 1000164","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:12.488589","level":"info","event":"Processed protocol: 1000164","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:12.488653","level":"info","event":"Processing protocol: 1000163","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:13.878525","level":"info","event":"Inserted data for protocol: 1000163","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:13.878604","level":"info","event":"Processed protocol: 1000163","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:13.878669","level":"info","event":"Processing protocol: 1000162","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:15.370908","level":"info","event":"Inserted data for protocol: 1000162","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:15.371219","level":"info","event":"Processed protocol: 1000162","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:15.371378","level":"info","event":"Processing protocol: 1000161","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:16.765098","level":"info","event":"Inserted data for protocol: 1000161","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:16.765426","level":"info","event":"Processed protocol: 1000161","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:16.765705","level":"info","event":"Processing protocol: 1000160","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:18.127718","level":"info","event":"Inserted data for protocol: 1000160","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:18.128159","level":"info","event":"Processed protocol: 1000160","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:18.128580","level":"info","event":"Processing protocol: 1000159","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:20.141955","level":"info","event":"Inserted data for protocol: 1000159","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:20.142664","level":"info","event":"Processed protocol: 1000159","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:20.143062","level":"info","event":"Processing protocol: 1000158","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:21.501538","level":"info","event":"Inserted data for protocol: 1000158","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:21.501597","level":"info","event":"Processed protocol: 1000158","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:21.501638","level":"info","event":"Processing protocol: 1000157","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:23.103090","level":"info","event":"Inserted data for protocol: 1000157","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:23.103156","level":"info","event":"Processed protocol: 1000157","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:23.103194","level":"info","event":"Processing protocol: 1000156","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:24.705962","level":"info","event":"Inserted data for protocol: 1000156","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:24.706150","level":"info","event":"Processed protocol: 1000156","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:24.706258","level":"info","event":"Processing protocol: 1000155","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:26.082728","level":"info","event":"Inserted data for protocol: 1000155","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:26.083148","level":"info","event":"Processed protocol: 1000155","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:26.083371","level":"info","event":"Processing protocol: 1000154","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:27.450438","level":"info","event":"Inserted data for protocol: 1000154","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:27.450806","level":"info","event":"Processed protocol: 1000154","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:27.450967","level":"info","event":"Processing protocol: 1000153","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:29.065757","level":"info","event":"Inserted data for protocol: 1000153","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:29.066152","level":"info","event":"Processed protocol: 1000153","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:29.066364","level":"info","event":"Processing protocol: 1000152","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:30.683171","level":"info","event":"Inserted data for protocol: 1000152","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:30.683610","level":"info","event":"Processed protocol: 1000152","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:30.683913","level":"info","event":"Processing protocol: 1000151","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:32.060757","level":"info","event":"Inserted data for protocol: 1000151","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:32.060832","level":"info","event":"Processed protocol: 1000151","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:32.060887","level":"info","event":"Processing protocol: 1000150","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:33.448801","level":"info","event":"Inserted data for protocol: 1000150","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:33.449244","level":"info","event":"Processed protocol: 1000150","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:33.449420","level":"info","event":"Processing protocol: 1000149","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:35.170772","level":"info","event":"Inserted data for protocol: 1000149","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:35.170849","level":"info","event":"Processed protocol: 1000149","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:35.170895","level":"info","event":"Processing protocol: 1000148","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:36.566123","level":"info","event":"Inserted data for protocol: 1000148","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:36.566497","level":"info","event":"Processed protocol: 1000148","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:36.566702","level":"info","event":"Processing protocol: 1000147","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:37.970243","level":"info","event":"Inserted data for protocol: 1000147","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:37.970720","level":"info","event":"Processed protocol: 1000147","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:37.971039","level":"info","event":"Processing protocol: 1000146","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:39.920310","level":"info","event":"Inserted data for protocol: 1000146","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:39.920671","level":"info","event":"Processed protocol: 1000146","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:39.920886","level":"info","event":"Processing protocol: 1000145","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:41.293824","level":"info","event":"Inserted data for protocol: 1000145","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:41.294171","level":"info","event":"Processed protocol: 1000145","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:41.294369","level":"info","event":"Processing protocol: 1000144","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:42.649582","level":"info","event":"Inserted data for protocol: 1000144","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:42.649814","level":"info","event":"Processed protocol: 1000144","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:42.649963","level":"info","event":"Processing protocol: 1000143","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:44.041553","level":"info","event":"Inserted data for protocol: 1000143","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:44.041626","level":"info","event":"Processed protocol: 1000143","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:44.041669","level":"info","event":"Processing protocol: 1000142","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:45.406942","level":"info","event":"Inserted data for protocol: 1000142","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:45.407051","level":"info","event":"Processed protocol: 1000142","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:45.407143","level":"info","event":"Processing protocol: 1000141","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:46.743425","level":"info","event":"Inserted data for protocol: 1000141","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:46.743750","level":"info","event":"Processed protocol: 1000141","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:46.744066","level":"info","event":"Processing protocol: 1000140","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:48.390414","level":"info","event":"Inserted data for protocol: 1000140","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:48.390747","level":"info","event":"Processed protocol: 1000140","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:48.390963","level":"info","event":"Processing protocol: 1000139","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:49.785152","level":"info","event":"Inserted data for protocol: 1000139","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:49.785244","level":"info","event":"Processed protocol: 1000139","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:49.785289","level":"info","event":"Processing protocol: 1000138","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:51.159018","level":"info","event":"Inserted data for protocol: 1000138","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:51.159899","level":"info","event":"Processed protocol: 1000138","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:51.160293","level":"info","event":"Processing protocol: 1000137","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:52.764134","level":"info","event":"Inserted data for protocol: 1000137","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:52.764212","level":"info","event":"Processed protocol: 1000137","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:52.764277","level":"info","event":"Processing protocol: 1000136","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:54.137187","level":"info","event":"Inserted data for protocol: 1000136","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:54.137511","level":"info","event":"Processed protocol: 1000136","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:54.137718","level":"info","event":"Processing protocol: 1000135","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:55.800441","level":"info","event":"Inserted data for protocol: 1000135","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:55.800548","level":"info","event":"Processed protocol: 1000135","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:55.800610","level":"info","event":"Processing protocol: 1000134","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:57.405277","level":"info","event":"Inserted data for protocol: 1000134","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:57.405617","level":"info","event":"Processed protocol: 1000134","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:57.405874","level":"info","event":"Processing protocol: 1000133","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:59.014351","level":"info","event":"Inserted data for protocol: 1000133","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:59.014761","level":"info","event":"Processed protocol: 1000133","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:02:59.014969","level":"info","event":"Processing protocol: 1000132","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:00.639849","level":"info","event":"Inserted data for protocol: 1000132","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:00.640239","level":"info","event":"Processed protocol: 1000132","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:00.640485","level":"info","event":"Processing protocol: 1000131","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:01.998689","level":"info","event":"Inserted data for protocol: 1000131","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:01.999361","level":"info","event":"Processed protocol: 1000131","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:01.999757","level":"info","event":"Processing protocol: 1000130","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:03.627215","level":"info","event":"Inserted data for protocol: 1000130","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:03.627296","level":"info","event":"Processed protocol: 1000130","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:03.627364","level":"info","event":"Processing protocol: 1000129","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:05.197580","level":"info","event":"Inserted data for protocol: 1000129","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:05.197818","level":"info","event":"Processed protocol: 1000129","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:05.198089","level":"info","event":"Processing protocol: 1000128","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:06.813328","level":"info","event":"Inserted data for protocol: 1000128","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:06.813388","level":"info","event":"Processed protocol: 1000128","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:06.813436","level":"info","event":"Processing protocol: 1000127","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:08.175108","level":"info","event":"Inserted data for protocol: 1000127","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:08.175478","level":"info","event":"Processed protocol: 1000127","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:08.176143","level":"info","event":"Processing protocol: 1000126","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:10.037114","level":"info","event":"Inserted data for protocol: 1000126","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:10.037333","level":"info","event":"Processed protocol: 1000126","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:10.037466","level":"info","event":"Processing protocol: 1000125","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:11.405167","level":"info","event":"Inserted data for protocol: 1000125","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:11.405460","level":"info","event":"Processed protocol: 1000125","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:11.405648","level":"info","event":"Processing protocol: 1000124","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:12.745145","level":"info","event":"Inserted data for protocol: 1000124","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:12.745500","level":"info","event":"Processed protocol: 1000124","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:12.745693","level":"info","event":"Processing protocol: 1000123","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:14.417615","level":"info","event":"Inserted data for protocol: 1000123","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:14.417776","level":"info","event":"Processed protocol: 1000123","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:14.417948","level":"info","event":"Processing protocol: 1000122","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:15.781923","level":"info","event":"Inserted data for protocol: 1000122","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:15.782789","level":"info","event":"Processed protocol: 1000122","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:15.783296","level":"info","event":"Processing protocol: 1000121","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:17.158263","level":"info","event":"Inserted data for protocol: 1000121","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:17.158364","level":"info","event":"Processed protocol: 1000121","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:17.158877","level":"info","event":"Processing protocol: 1000120","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:18.558746","level":"info","event":"Inserted data for protocol: 1000120","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:18.559181","level":"info","event":"Processed protocol: 1000120","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:18.559390","level":"info","event":"Processing protocol: 1000119","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:19.929845","level":"info","event":"Inserted data for protocol: 1000119","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:19.930250","level":"info","event":"Processed protocol: 1000119","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:19.930450","level":"info","event":"Processing protocol: 1000118","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:21.295837","level":"info","event":"Inserted data for protocol: 1000118","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:21.296482","level":"info","event":"Processed protocol: 1000118","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:21.296762","level":"info","event":"Processing protocol: 1000117","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:22.765678","level":"info","event":"Inserted data for protocol: 1000117","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:22.765763","level":"info","event":"Processed protocol: 1000117","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:22.765812","level":"info","event":"Processing protocol: 1000116","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:24.183318","level":"info","event":"Inserted data for protocol: 1000116","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:24.183736","level":"info","event":"Processed protocol: 1000116","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:24.184031","level":"info","event":"Processing protocol: 1000115","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:25.864841","level":"info","event":"Inserted data for protocol: 1000115","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:25.865210","level":"info","event":"Processed protocol: 1000115","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:25.865438","level":"info","event":"Processing protocol: 1000114","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:27.522168","level":"info","event":"Inserted data for protocol: 1000114","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:27.522268","level":"info","event":"Processed protocol: 1000114","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:27.522336","level":"info","event":"Processing protocol: 1000113","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:28.886421","level":"info","event":"Inserted data for protocol: 1000113","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:28.887176","level":"info","event":"Processed protocol: 1000113","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:28.887583","level":"info","event":"Processing protocol: 1000112","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:30.274313","level":"info","event":"Inserted data for protocol: 1000112","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:30.274683","level":"info","event":"Processed protocol: 1000112","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:30.274889","level":"info","event":"Processing protocol: 1000111","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:31.631517","level":"info","event":"Inserted data for protocol: 1000111","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:31.631845","level":"info","event":"Processed protocol: 1000111","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:31.632119","level":"info","event":"Processing protocol: 1000110","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:33.224888","level":"info","event":"Inserted data for protocol: 1000110","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:33.225309","level":"info","event":"Processed protocol: 1000110","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:33.225521","level":"info","event":"Processing protocol: 1000109","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:34.599966","level":"info","event":"Inserted data for protocol: 1000109","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:34.600395","level":"info","event":"Processed protocol: 1000109","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:34.600608","level":"info","event":"Processing protocol: 1000108","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:35.995926","level":"info","event":"Inserted data for protocol: 1000108","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:35.996168","level":"info","event":"Processed protocol: 1000108","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:35.996288","level":"info","event":"Processing protocol: 1000107","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:37.392740","level":"info","event":"Inserted data for protocol: 1000107","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:37.393192","level":"info","event":"Processed protocol: 1000107","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:37.393499","level":"info","event":"Processing protocol: 1000106","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:39.070608","level":"info","event":"Inserted data for protocol: 1000106","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:39.070695","level":"info","event":"Processed protocol: 1000106","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:39.070735","level":"info","event":"Processing protocol: 1000105","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:40.922681","level":"info","event":"Inserted data for protocol: 1000105","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:40.923109","level":"info","event":"Processed protocol: 1000105","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:40.923352","level":"info","event":"Processing protocol: 1000104","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:42.315143","level":"info","event":"Inserted data for protocol: 1000104","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:42.315557","level":"info","event":"Processed protocol: 1000104","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:42.315844","level":"info","event":"Processing protocol: 1000103","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:43.702406","level":"info","event":"Inserted data for protocol: 1000103","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:43.702790","level":"info","event":"Processed protocol: 1000103","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:43.703007","level":"info","event":"Processing protocol: 1000102","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:45.590171","level":"info","event":"Inserted data for protocol: 1000102","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:45.590813","level":"info","event":"Processed protocol: 1000102","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:45.591185","level":"info","event":"Processing protocol: 1000101","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:47.252564","level":"info","event":"Inserted data for protocol: 1000101","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:47.252671","level":"info","event":"Processed protocol: 1000101","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:47.252722","level":"info","event":"Processing protocol: 1000100","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:48.892640","level":"info","event":"Inserted data for protocol: 1000100","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:48.892947","level":"info","event":"Processed protocol: 1000100","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:48.893245","level":"info","event":"Processing protocol: 1000099","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:50.580239","level":"info","event":"Inserted data for protocol: 1000099","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:50.580460","level":"info","event":"Processed protocol: 1000099","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:50.580591","level":"info","event":"Processing protocol: 1000098","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:52.038765","level":"info","event":"Inserted data for protocol: 1000098","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:52.041229","level":"info","event":"Processed protocol: 1000098","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:52.041464","level":"info","event":"Processing protocol: 1000097","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:53.464443","level":"info","event":"Inserted data for protocol: 1000097","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:53.464762","level":"info","event":"Processed protocol: 1000097","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:53.464949","level":"info","event":"Processing protocol: 1000096","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:54.863493","level":"info","event":"Inserted data for protocol: 1000096","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:54.863944","level":"info","event":"Processed protocol: 1000096","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:54.864182","level":"info","event":"Processing protocol: 1000095","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:56.508110","level":"info","event":"Inserted data for protocol: 1000095","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:56.510121","level":"info","event":"Processed protocol: 1000095","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:56.511960","level":"info","event":"Processing protocol: 1000094","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:58.207508","level":"info","event":"Inserted data for protocol: 1000094","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:58.207909","level":"info","event":"Processed protocol: 1000094","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:58.208201","level":"info","event":"Processing protocol: 1000093","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:59.538514","level":"info","event":"Inserted data for protocol: 1000093","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:59.538579","level":"info","event":"Processed protocol: 1000093","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:03:59.538607","level":"info","event":"Processing protocol: 1000092","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:00.889850","level":"info","event":"Inserted data for protocol: 1000092","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:00.890180","level":"info","event":"Processed protocol: 1000092","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:00.890334","level":"info","event":"Processing protocol: 1000091","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:02.500446","level":"info","event":"Inserted data for protocol: 1000091","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:02.501201","level":"info","event":"Processed protocol: 1000091","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:02.501594","level":"info","event":"Processing protocol: 1000090","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:04.167250","level":"info","event":"Inserted data for protocol: 1000090","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:04.167431","level":"info","event":"Processed protocol: 1000090","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:04.168202","level":"info","event":"Processing protocol: 1000089","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:05.559662","level":"info","event":"Inserted data for protocol: 1000089","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:05.559908","level":"info","event":"Processed protocol: 1000089","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:05.560050","level":"info","event":"Processing protocol: 1000088","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:07.418905","level":"info","event":"Inserted data for protocol: 1000088","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:07.419102","level":"info","event":"Processed protocol: 1000088","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:07.419197","level":"info","event":"Processing protocol: 1000087","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:09.354336","level":"info","event":"Inserted data for protocol: 1000087","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:09.354648","level":"info","event":"Processed protocol: 1000087","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:09.354845","level":"info","event":"Processing protocol: 1000086","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:10.714341","level":"info","event":"Inserted data for protocol: 1000086","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:10.714561","level":"info","event":"Processed protocol: 1000086","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:10.714671","level":"info","event":"Processing protocol: 1000085","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:12.065263","level":"info","event":"Inserted data for protocol: 1000085","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:12.065696","level":"info","event":"Processed protocol: 1000085","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:12.065954","level":"info","event":"Processing protocol: 1000084","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:13.703856","level":"info","event":"Inserted data for protocol: 1000084","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:13.704221","level":"info","event":"Processed protocol: 1000084","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:13.704447","level":"info","event":"Processing protocol: 1000083","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:15.097392","level":"info","event":"Inserted data for protocol: 1000083","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:15.097641","level":"info","event":"Processed protocol: 1000083","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:15.097775","level":"info","event":"Processing protocol: 1000082","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:16.742829","level":"info","event":"Inserted data for protocol: 1000082","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:16.743249","level":"info","event":"Processed protocol: 1000082","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:16.743469","level":"info","event":"Processing protocol: 1000081","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:18.296394","level":"info","event":"Inserted data for protocol: 1000081","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:18.296904","level":"info","event":"Processed protocol: 1000081","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:18.297237","level":"info","event":"Processing protocol: 1000080","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:19.655375","level":"info","event":"Inserted data for protocol: 1000080","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:19.655695","level":"info","event":"Processed protocol: 1000080","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:19.655907","level":"info","event":"Processing protocol: 1000079","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:21.250763","level":"info","event":"Inserted data for protocol: 1000079","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:21.251137","level":"info","event":"Processed protocol: 1000079","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:21.251387","level":"info","event":"Processing protocol: 1000078","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:22.624784","level":"info","event":"Inserted data for protocol: 1000078","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:22.625179","level":"info","event":"Processed protocol: 1000078","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:22.625468","level":"info","event":"Processing protocol: 1000077","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:24.043693","level":"info","event":"Inserted data for protocol: 1000077","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:24.044117","level":"info","event":"Processed protocol: 1000077","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:24.044365","level":"info","event":"Processing protocol: 1000076","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:25.454406","level":"info","event":"Inserted data for protocol: 1000076","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:25.454484","level":"info","event":"Processed protocol: 1000076","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:25.454513","level":"info","event":"Processing protocol: 1000075","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:26.932637","level":"info","event":"Inserted data for protocol: 1000075","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:26.932940","level":"info","event":"Processed protocol: 1000075","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:26.933214","level":"info","event":"Processing protocol: 1000074","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:28.576795","level":"info","event":"Inserted data for protocol: 1000074","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:28.577217","level":"info","event":"Processed protocol: 1000074","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:28.577558","level":"info","event":"Processing protocol: 1000073","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:30.244722","level":"info","event":"Inserted data for protocol: 1000073","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:30.245107","level":"info","event":"Processed protocol: 1000073","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:30.245374","level":"info","event":"Processing protocol: 1000072","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:31.610278","level":"info","event":"Inserted data for protocol: 1000072","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:31.610345","level":"info","event":"Processed protocol: 1000072","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:31.610396","level":"info","event":"Processing protocol: 1000071","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:33.061511","level":"info","event":"Inserted data for protocol: 1000071","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:33.061848","level":"info","event":"Processed protocol: 1000071","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:33.062129","level":"info","event":"Processing protocol: 1000070","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:34.429025","level":"info","event":"Inserted data for protocol: 1000070","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:34.429087","level":"info","event":"Processed protocol: 1000070","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:34.429116","level":"info","event":"Processing protocol: 1000069","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:36.070155","level":"info","event":"Inserted data for protocol: 1000069","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:36.070446","level":"info","event":"Processed protocol: 1000069","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:36.070628","level":"info","event":"Processing protocol: 1000068","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:37.462790","level":"info","event":"Inserted data for protocol: 1000068","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:37.462875","level":"info","event":"Processed protocol: 1000068","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:37.462900","level":"info","event":"Processing protocol: 1000067","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:38.860592","level":"info","event":"Inserted data for protocol: 1000067","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:38.860670","level":"info","event":"Processed protocol: 1000067","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:38.860712","level":"info","event":"Processing protocol: 1000066","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:40.265440","level":"info","event":"Inserted data for protocol: 1000066","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:40.265759","level":"info","event":"Processed protocol: 1000066","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:40.266057","level":"info","event":"Processing protocol: 1000065","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:41.881110","level":"info","event":"Inserted data for protocol: 1000065","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:41.881412","level":"info","event":"Processed protocol: 1000065","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:41.881602","level":"info","event":"Processing protocol: 1000064","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:43.349662","level":"info","event":"Inserted data for protocol: 1000064","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:43.350124","level":"info","event":"Processed protocol: 1000064","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:43.350303","level":"info","event":"Processing protocol: 1000063","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:45.220393","level":"info","event":"Inserted data for protocol: 1000063","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:45.220968","level":"info","event":"Processed protocol: 1000063","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:45.221309","level":"info","event":"Processing protocol: 1000062","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:46.887166","level":"info","event":"Inserted data for protocol: 1000062","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:46.887462","level":"info","event":"Processed protocol: 1000062","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:46.887591","level":"info","event":"Processing protocol: 1000061","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:48.580444","level":"info","event":"Inserted data for protocol: 1000061","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:48.580829","level":"info","event":"Processed protocol: 1000061","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:48.581102","level":"info","event":"Processing protocol: 1000060","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:50.232970","level":"info","event":"Inserted data for protocol: 1000060","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:50.233385","level":"info","event":"Processed protocol: 1000060","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:50.233532","level":"info","event":"Processing protocol: 1000059","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:51.896104","level":"info","event":"Inserted data for protocol: 1000059","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:51.897596","level":"info","event":"Processed protocol: 1000059","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:51.897856","level":"info","event":"Processing protocol: 1000058","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:53.261442","level":"info","event":"Inserted data for protocol: 1000058","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:53.261639","level":"info","event":"Processed protocol: 1000058","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:53.261725","level":"info","event":"Processing protocol: 1000057","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:54.599658","level":"info","event":"Inserted data for protocol: 1000057","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:54.599806","level":"info","event":"Processed protocol: 1000057","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:54.599878","level":"info","event":"Processing protocol: 1000056","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:55.996917","level":"info","event":"Inserted data for protocol: 1000056","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:55.997304","level":"info","event":"Processed protocol: 1000056","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:55.997591","level":"info","event":"Processing protocol: 1000055","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:57.662341","level":"info","event":"Inserted data for protocol: 1000055","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:57.662726","level":"info","event":"Processed protocol: 1000055","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:57.663082","level":"info","event":"Processing protocol: 1000054","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:59.321543","level":"info","event":"Inserted data for protocol: 1000054","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:59.321931","level":"info","event":"Processed protocol: 1000054","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:04:59.322386","level":"info","event":"Processing protocol: 1000053","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:00.967727","level":"info","event":"Inserted data for protocol: 1000053","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:00.968499","level":"info","event":"Processed protocol: 1000053","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:00.968872","level":"info","event":"Processing protocol: 1000052","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:02.390849","level":"info","event":"Inserted data for protocol: 1000052","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:02.391319","level":"info","event":"Processed protocol: 1000052","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:02.391526","level":"info","event":"Processing protocol: 1000051","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:03.732192","level":"info","event":"Inserted data for protocol: 1000051","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:03.732271","level":"info","event":"Processed protocol: 1000051","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:03.732326","level":"info","event":"Processing protocol: 1000050","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:05.074947","level":"info","event":"Inserted data for protocol: 1000050","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:05.075073","level":"info","event":"Processed protocol: 1000050","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:05.075152","level":"info","event":"Processing protocol: 1000049","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:06.697597","level":"info","event":"Inserted data for protocol: 1000049","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:06.697851","level":"info","event":"Processed protocol: 1000049","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:06.698091","level":"info","event":"Processing protocol: 1000048","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:08.077947","level":"info","event":"Inserted data for protocol: 1000048","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:08.078337","level":"info","event":"Processed protocol: 1000048","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:08.078483","level":"info","event":"Processing protocol: 1000047","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:09.438069","level":"info","event":"Inserted data for protocol: 1000047","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:09.438408","level":"info","event":"Processed protocol: 1000047","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:09.438533","level":"info","event":"Processing protocol: 1000046","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:10.876920","level":"info","event":"Inserted data for protocol: 1000046","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:10.877333","level":"info","event":"Processed protocol: 1000046","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:10.877587","level":"info","event":"Processing protocol: 1000045","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:12.280181","level":"info","event":"Inserted data for protocol: 1000045","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:12.280430","level":"info","event":"Processed protocol: 1000045","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:12.280555","level":"info","event":"Processing protocol: 1000044","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:13.895970","level":"info","event":"Inserted data for protocol: 1000044","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:13.896166","level":"info","event":"Processed protocol: 1000044","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:13.896223","level":"info","event":"Processing protocol: 1000043","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:15.280205","level":"info","event":"Inserted data for protocol: 1000043","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:15.280575","level":"info","event":"Processed protocol: 1000043","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:15.280800","level":"info","event":"Processing protocol: 1000042","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:17.031308","level":"info","event":"Inserted data for protocol: 1000042","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:17.032075","level":"info","event":"Processed protocol: 1000042","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:17.032480","level":"info","event":"Processing protocol: 1000041","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:18.775591","level":"info","event":"Inserted data for protocol: 1000041","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:18.775956","level":"info","event":"Processed protocol: 1000041","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:18.776052","level":"info","event":"Processing protocol: 1000040","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:20.303822","level":"info","event":"Inserted data for protocol: 1000040","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:20.304642","level":"info","event":"Processed protocol: 1000040","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:20.304932","level":"info","event":"Processing protocol: 1000039","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:21.766846","level":"info","event":"Inserted data for protocol: 1000039","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:21.767073","level":"info","event":"Processed protocol: 1000039","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:21.767160","level":"info","event":"Processing protocol: 1000038","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:23.151159","level":"info","event":"Inserted data for protocol: 1000038","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:23.151343","level":"info","event":"Processed protocol: 1000038","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:05:23.151445","level":"info","event":"Processing protocol: 1000037","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:22.560808","level":"info","event":"Inserted data for protocol: 1000037","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:22.562746","level":"info","event":"Processed protocol: 1000037","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:22.563053","level":"info","event":"Processing protocol: 1000036","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:24.089234","level":"info","event":"Inserted data for protocol: 1000036","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:24.089707","level":"info","event":"Processed protocol: 1000036","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:24.090065","level":"info","event":"Processing protocol: 1000035","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:25.507423","level":"info","event":"Inserted data for protocol: 1000035","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:25.507946","level":"info","event":"Processed protocol: 1000035","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:25.508293","level":"info","event":"Processing protocol: 1000034","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:27.173429","level":"info","event":"Inserted data for protocol: 1000034","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:27.174276","level":"info","event":"Processed protocol: 1000034","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:27.174355","level":"info","event":"Processing protocol: 1000033","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:28.615665","level":"info","event":"Inserted data for protocol: 1000033","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:28.616029","level":"info","event":"Processed protocol: 1000033","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:28.616180","level":"info","event":"Processing protocol: 1000032","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:30.501638","level":"info","event":"Inserted data for protocol: 1000032","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:30.502288","level":"info","event":"Processed protocol: 1000032","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:30.502674","level":"info","event":"Processing protocol: 1000031","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:32.131914","level":"info","event":"Inserted data for protocol: 1000031","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:32.132310","level":"info","event":"Processed protocol: 1000031","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:32.132400","level":"info","event":"Processing protocol: 1000030","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:33.779531","level":"info","event":"Inserted data for protocol: 1000030","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:33.779769","level":"info","event":"Processed protocol: 1000030","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:33.779836","level":"info","event":"Processing protocol: 1000029","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:35.140737","level":"info","event":"Inserted data for protocol: 1000029","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:35.141104","level":"info","event":"Processed protocol: 1000029","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:35.141321","level":"info","event":"Processing protocol: 1000028","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:36.771642","level":"info","event":"Inserted data for protocol: 1000028","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:36.772210","level":"info","event":"Processed protocol: 1000028","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:36.772434","level":"info","event":"Processing protocol: 1000027","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:38.425476","level":"info","event":"Inserted data for protocol: 1000027","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:38.425830","level":"info","event":"Processed protocol: 1000027","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:38.425975","level":"info","event":"Processing protocol: 1000026","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:40.125103","level":"info","event":"Inserted data for protocol: 1000026","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:40.125440","level":"info","event":"Processed protocol: 1000026","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:40.125810","level":"info","event":"Processing protocol: 1000025","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:41.487656","level":"info","event":"Inserted data for protocol: 1000025","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:41.487901","level":"info","event":"Processed protocol: 1000025","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:41.488136","level":"info","event":"Processing protocol: 1000024","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:42.868661","level":"info","event":"Inserted data for protocol: 1000024","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:42.869130","level":"info","event":"Processed protocol: 1000024","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:42.869360","level":"info","event":"Processing protocol: 1000023","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:44.233437","level":"info","event":"Inserted data for protocol: 1000023","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:44.234494","level":"info","event":"Processed protocol: 1000023","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:44.235474","level":"info","event":"Processing protocol: 1000022","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:45.603054","level":"info","event":"Inserted data for protocol: 1000022","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:45.603303","level":"info","event":"Processed protocol: 1000022","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:45.603355","level":"info","event":"Processing protocol: 1000021","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:47.126349","level":"info","event":"Inserted data for protocol: 1000021","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:47.126807","level":"info","event":"Processed protocol: 1000021","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:47.127131","level":"info","event":"Processing protocol: 1000020","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:48.755525","level":"info","event":"Inserted data for protocol: 1000020","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:48.755965","level":"info","event":"Processed protocol: 1000020","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:48.756364","level":"info","event":"Processing protocol: 1000019","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:50.170410","level":"info","event":"Inserted data for protocol: 1000019","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:50.170521","level":"info","event":"Processed protocol: 1000019","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:50.170557","level":"info","event":"Processing protocol: 1000018","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:51.584606","level":"info","event":"Inserted data for protocol: 1000018","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:51.585046","level":"info","event":"Processed protocol: 1000018","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:51.585330","level":"info","event":"Processing protocol: 1000017","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:52.971507","level":"info","event":"Inserted data for protocol: 1000017","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:52.971884","level":"info","event":"Processed protocol: 1000017","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:52.973471","level":"info","event":"Processing protocol: 1000016","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:54.586815","level":"info","event":"Inserted data for protocol: 1000016","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:54.587623","level":"info","event":"Processed protocol: 1000016","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:54.587855","level":"info","event":"Processing protocol: 1000015","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:56.001733","level":"info","event":"Inserted data for protocol: 1000015","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:56.001975","level":"info","event":"Processed protocol: 1000015","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:56.002108","level":"info","event":"Processing protocol: 1000014","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:57.394975","level":"info","event":"Inserted data for protocol: 1000014","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:57.395071","level":"info","event":"Processed protocol: 1000014","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:57.395106","level":"info","event":"Processing protocol: 1000013","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:58.797409","level":"info","event":"Inserted data for protocol: 1000013","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:58.797489","level":"info","event":"Processed protocol: 1000013","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:07:58.797544","level":"info","event":"Processing protocol: 1000012","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:00.426094","level":"info","event":"Inserted data for protocol: 1000012","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:00.426369","level":"info","event":"Processed protocol: 1000012","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:00.426566","level":"info","event":"Processing protocol: 1000011","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:01.784920","level":"info","event":"Inserted data for protocol: 1000011","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:01.785382","level":"info","event":"Processed protocol: 1000011","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:01.786931","level":"info","event":"Processing protocol: 1000010","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:03.256512","level":"info","event":"Inserted data for protocol: 1000010","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:03.257317","level":"info","event":"Processed protocol: 1000010","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:03.257688","level":"info","event":"Processing protocol: 1000009","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:04.603442","level":"info","event":"Inserted data for protocol: 1000009","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:04.603543","level":"info","event":"Processed protocol: 1000009","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:04.604394","level":"info","event":"Processing protocol: 1000008","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:05.948521","level":"info","event":"Inserted data for protocol: 1000008","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:05.948819","level":"info","event":"Processed protocol: 1000008","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:05.948944","level":"info","event":"Processing protocol: 1000007","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:07.301470","level":"info","event":"Inserted data for protocol: 1000007","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:07.301842","level":"info","event":"Processed protocol: 1000007","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:07.302117","level":"info","event":"Processing protocol: 1000006","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:08.650852","level":"info","event":"Inserted data for protocol: 1000006","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:08.651250","level":"info","event":"Processed protocol: 1000006","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:08.651499","level":"info","event":"Processing protocol: 1000005","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:10.025587","level":"info","event":"Inserted data for protocol: 1000005","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:10.025722","level":"info","event":"Processed protocol: 1000005","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:10.025798","level":"info","event":"Processing protocol: 1000004","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:11.417118","level":"info","event":"Inserted data for protocol: 1000004","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:11.417556","level":"info","event":"Processed protocol: 1000004","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:11.417846","level":"info","event":"Processing protocol: 1000003","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:12.767592","level":"info","event":"Inserted data for protocol: 1000003","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:12.767925","level":"info","event":"Processed protocol: 1000003","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:12.768239","level":"info","event":"Processing protocol: 1000002","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:14.152427","level":"info","event":"Inserted data for protocol: 1000002","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:14.152508","level":"info","event":"Processed protocol: 1000002","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:14.152556","level":"info","event":"Processing protocol: 1000001","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:15.492411","level":"info","event":"Inserted data for protocol: 1000001","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:15.492662","level":"info","event":"Processed protocol: 1000001","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:15.492908","level":"info","event":"Processing protocol: 1000000","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:16.816468","level":"info","event":"Inserted data for protocol: 1000000","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:16.816707","level":"info","event":"Processed protocol: 1000000","logger":"eln.eln_parser"}
{"timestamp":"2025-09-08T07:08:16.894956","level":"info","event":"Parsed ELN data","logger":"root"}
{"timestamp":"2025-09-08T07:08:16.915448","level":"info","event":"Done. Returned value was: None","logger":"airflow.task.operators.airflow.providers.standard.operators.python.PythonOperator"}
