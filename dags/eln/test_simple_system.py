"""
Test Script for Simple ELN System

This script tests all the components of the simplified ELN system to make sure
everything works together properly.
"""

import logging
import sys
import os

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_data_transformer():
    """Test the data transformer."""
    print("=" * 50)
    print("TESTING DATA TRANSFORMER")
    print("=" * 50)
    
    try:
        from data_transformer import DataTransformer
        
        transformer = DataTransformer()
        
        # Test experiment normalization
        test_cases = [
            "MS25012-010-R1-Step-1",
            "MS25013-005-R2-Step-2", 
            "MS25018-013-R1-S1",
            "MS25007-R1-010",
            "MS25005",
            "INVALID-123"
        ]
        
        print("Testing experiment number normalization:")
        for exp in test_cases:
            normalized = transformer.normalize_experiment_number(exp)
            is_valid, message = transformer.validate_experiment_number(exp)
            
            status = "✓" if is_valid else "✗"
            print(f"  {status} {exp:<25} -> {normalized or 'FAILED'}")
            if not is_valid:
                print(f"    └─ {message}")
        
        print("\n✅ Data transformer test completed")
        return True
        
    except Exception as e:
        print(f"❌ Data transformer test failed: {e}")
        return False


def test_db_operations():
    """Test database operations."""
    print("=" * 50)
    print("TESTING DATABASE OPERATIONS")
    print("=" * 50)
    
    try:
        from db_operations import DatabaseOperations
        
        db_ops = DatabaseOperations()
        
        # Test health check
        health = db_ops.health_check()
        print(f"Database health check: {'✓ PASSED' if health else '✗ FAILED'}")
        
        if health:
            # Test getting stats
            stats = db_ops.get_stats()
            print(f"Database stats: {stats}")
            
            # Test getting last sync date
            last_sync = db_ops.get_last_sync_date()
            print(f"Last sync date: {last_sync}")
            
            # Test getting run logs
            logs = db_ops.get_run_logs(3)
            print(f"Recent logs: {len(logs)} entries")
            
        else:
            print("⚠️  Database not available - this is OK if MongoDB is not running")
        
        db_ops.close_connection()
        print("\n✅ Database operations test completed")
        return True
        
    except Exception as e:
        print(f"❌ Database operations test failed: {e}")
        return False


def test_mapping_lookup():
    """Test mapping lookup."""
    print("=" * 50)
    print("TESTING MAPPING LOOKUP")
    print("=" * 50)
    
    try:
        from data_transformer import DataTransformer
        
        transformer = DataTransformer()
        
        # Test mapping lookup
        test_experiments = [
            "MS25012-007-R1-S1",
            "MS25018-013-R1-S1",
            "MS25013",
            "UNKNOWN-001"
        ]
        
        print("Testing mapping lookup:")
        for exp in test_experiments:
            mapping = transformer.get_best_mapping(exp)
            
            if mapping:
                print(f"  ✓ {exp}")
                print(f"    Project ID: {mapping.get('project_id', 'N/A')[:20]}...")
                print(f"    Route ID:   {mapping.get('route_id', 'N/A')[:20]}...")
                print(f"    Step ID:    {mapping.get('step_id', 'N/A')[:20]}...")
            else:
                print(f"  ✗ {exp} - No mapping found")
        
        print("\n✅ Mapping lookup test completed")
        return True
        
    except Exception as e:
        print(f"❌ Mapping lookup test failed: {e}")
        return False


def test_simple_eln_sync():
    """Test the simple ELN sync system."""
    print("=" * 50)
    print("TESTING SIMPLE ELN SYNC")
    print("=" * 50)
    
    try:
        from simple_eln_sync import SimpleELNSync
        
        sync = SimpleELNSync()
        
        # Test getting sync status
        status = sync.get_sync_status()
        
        if "error" in status:
            print(f"⚠️  Sync status error: {status['error']}")
        else:
            print("Sync status:")
            print(f"  Last sync: {status.get('last_sync_date', 'Never')}")
            print(f"  Database health: {'✓' if status.get('database_health') else '✗'}")
            
            db_stats = status.get('database_stats', {})
            if db_stats:
                print("  Database collections:")
                for collection, stats in db_stats.items():
                    print(f"    {collection}: {stats.get('count', 0)} records")
        
        # Test experiment validation
        test_experiments = [
            "MS25012-010-R1-Step-1",
            "MS25018-013-R1-S1"
        ]
        
        validation_results = sync.validate_experiments(test_experiments)
        print(f"\nExperiment validation results:")
        for exp, result in validation_results.items():
            status_icon = "✓" if result['valid'] else "✗"
            print(f"  {status_icon} {exp} -> {result.get('normalized', 'FAILED')}")
        
        sync.cleanup()
        print("\n✅ Simple ELN sync test completed")
        return True
        
    except Exception as e:
        print(f"❌ Simple ELN sync test failed: {e}")
        return False


def test_imports():
    """Test that all imports work."""
    print("=" * 50)
    print("TESTING IMPORTS")
    print("=" * 50)
    
    imports_to_test = [
        ("data_transformer", "DataTransformer"),
        ("db_operations", "DatabaseOperations"),
        ("simple_eln_sync", "SimpleELNSync"),
        ("eln_mapper", "ELN_MAPPING"),
        ("eln_constants", "CHEMSTACK_HEADERS")
    ]
    
    all_passed = True
    
    for module_name, class_name in imports_to_test:
        try:
            module = __import__(module_name)
            if hasattr(module, class_name):
                print(f"  ✓ {module_name}.{class_name}")
            else:
                print(f"  ✗ {module_name}.{class_name} - not found")
                all_passed = False
        except ImportError as e:
            print(f"  ✗ {module_name} - import failed: {e}")
            all_passed = False
        except Exception as e:
            print(f"  ✗ {module_name} - error: {e}")
            all_passed = False
    
    if all_passed:
        print("\n✅ All imports test completed")
    else:
        print("\n❌ Some imports failed")
    
    return all_passed


def main():
    """Run all tests."""
    print("🧪 TESTING SIMPLE ELN SYNCHRONIZATION SYSTEM")
    print("=" * 60)
    print()
    
    tests = [
        ("Imports", test_imports),
        ("Data Transformer", test_data_transformer),
        ("Database Operations", test_db_operations),
        ("Mapping Lookup", test_mapping_lookup),
        ("Simple ELN Sync", test_simple_eln_sync)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
            print()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
            print()
    
    # Summary
    print("=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name:<20} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Your simplified ELN system is working correctly.")
        print("\nNext steps:")
        print("1. Configure your MongoDB connection in db_operations.py")
        print("2. Update missing mappings in eln_mapper.py")
        print("3. Test with actual ELN data")
        print("4. Run: python simple_usage_example.py")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please check the errors above.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
