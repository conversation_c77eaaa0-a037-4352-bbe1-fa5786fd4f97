import logging
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import requests

# Add paths for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, '/opt/airflow/dag_dependencies')

from db_operations import DatabaseOperations
from data_transformer import DataTransformer
from eln_parser import <PERSON><PERSON>NPars<PERSON>
from eln_constants import CHEMSTACK_HEADERS, CHEMSTACK_CREATE_EXPERIMENT_URL, CHEMSTACK_SEARCH_EXPERIMENT_URL

logger = logging.getLogger(__name__)


class ELNSync:
    """
    Simple ELN synchronization system.
    
    Features:
    - Clean separation of DB operations
    - Improved experiment number normalization
    - Simple transformation pipeline
    - Easy to understand and maintain
    """
    
    def __init__(self):
        """Initialize the sync system."""
        self.db_ops = DatabaseOperations()
        self.transformer = DataTransformer()
        self.eln_parser = ELNParser()
    
    def sync_all_protocols(self, force_full_sync: bool = False) -> Dict[str, Any]:
        """
        Sync all protocols from ELN to ChemStack.
        
        Args:
            force_full_sync: If True, sync all data regardless of last sync date
            
        Returns:
            Dict with sync results
        """
        results = {
            "total_protocols": 0,
            "processed": 0,
            "successful": 0,
            "failed": 0,
            "errors": []
        }
        
        try:
            if force_full_sync:
                last_sync_date = datetime.now() - timedelta(days=365)
                logger.info("Force full sync requested")
            else:
                last_sync_date = self.db_ops.get_last_sync_date()
                logger.info(f"Last sync date: {last_sync_date}")
            
            protocols = self._fetch_protocols(last_sync_date)
            results["total_protocols"] = len(protocols)
            logger.info(f"Fetched {len(protocols)} protocols from ELN")
            
            for protocol in protocols:
                protocol_id = protocol.get("protocolordercode")
                try:
                    success = self._process_single_protocol(protocol_id)
                    results["processed"] += 1
                    
                    if success:
                        results["successful"] += 1
                    else:
                        results["failed"] += 1
                        
                except Exception as e:
                    results["failed"] += 1
                    results["errors"].append(f"{protocol_id}: {str(e)}")
                    logger.error(f"Error processing protocol {protocol_id}: {e}")
            
            error_summary = "; ".join(results["errors"][:5]) if results["errors"] else ""
            self.db_ops.insert_run_log(datetime.now(), error_summary)
            
            logger.info(f"Sync completed: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Sync failed: {e}")
            self.db_ops.insert_run_log(datetime.now(), str(e))
            results["errors"].append(f"SYSTEM: {str(e)}")
            return results
    
    def sync_single_protocol(self, protocol_id: str) -> bool:
        """
        Sync a single protocol by ID.
        
        Args:
            protocol_id: Protocol ID to sync
            
        Returns:
            True if successful, False otherwise
        """
        try:
            return self._process_single_protocol(protocol_id)
        except Exception as e:
            logger.error(f"Error syncing protocol {protocol_id}: {e}")
            return False
    
    def _fetch_protocols(self, last_sync_date: datetime) -> List[Dict[str, Any]]:
        """Fetch protocols from ELN API."""
        try:
            protocols_response = self.eln_parser.get_all_protocols(last_sync_date)
            return protocols_response.get("protocols", [])
        except Exception as e:
            logger.error(f"Error fetching protocols: {e}")
            return []
    
    def _process_single_protocol(self, protocol_id: str) -> bool:
        """
        Process a single protocol.
        
        Args:
            protocol_id: Protocol ID to process
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Processing protocol: {protocol_id}")
            
            protocol_data = self.eln_parser.get_protocol_by_id(protocol_id)
            if not protocol_data:
                logger.error(f"Could not fetch protocol details for {protocol_id}")
                return False
            
            parsed_data = self._parse_protocol(protocol_data)
            if not parsed_data:
                logger.error(f"Could not parse protocol data for {protocol_id}")
                return False
            
            parsed_data["protocol_id"] = protocol_id
            self.db_ops.upsert_eln_data(protocol_id, parsed_data)
            
            experiment_number = '-'.join(parsed_data.get("experiment_overview", {}).get("experiment_number").split('-')[:-1])
            return self._sync_to_chemstack(parsed_data, experiment_number)
            
        except Exception as e:
            logger.error(f"Error processing protocol {protocol_id}: {e}")
            return False
    
    def _parse_protocol(self, protocol_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Parse protocol data using ELN parser."""
        try:
            self.eln_parser.raw = protocol_data
            self.eln_parser.structured = self.eln_parser._initialize_structure()
            self.eln_parser.content = self.eln_parser._parse_content()
            return self.eln_parser.parse()
        except Exception as e:
            logger.error(f"Error parsing protocol: {e}")
            return None
    
    def _get_experiment_metadata(self, experiment_number: str) -> Optional[Dict[str, Any]]:
        """Get experiment metadata from ChemStack."""
        try:
            url = CHEMSTACK_SEARCH_EXPERIMENT_URL.format(experiment_number=experiment_number)
            response = requests.get(url, headers=CHEMSTACK_HEADERS)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error fetching experiment metadata: {e}")
            return {}
    
    def _sync_to_chemstack(self, parsed_data: Dict[str, Any], experiment_number: str) -> bool:
        """
        Sync parsed data to ChemStack.
        
        Args:
            parsed_data: Parsed ELN data
            experiment_number: Experiment number
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Transform data
            transformed_payload = self.transformer.transform_eln_to_chemstack(parsed_data)
            if not transformed_payload:
                logger.error(f"Failed to transform data for experiment: {experiment_number}")
                return False
            
            # Get mapping information
            mapping = {}
            metadata = self._get_experiment_metadata(experiment_number)
            if not metadata:
                return False
            mapping['step_id'] = metadata.get('id')
            mapping['project_id'] = metadata.get('project_id')
            mapping['route_id'] = metadata.get('route_id')
            
            if not mapping or not all(mapping.get(key) for key in ['project_id', 'route_id', 'step_id']):
                logger.error(f"Incomplete mapping for experiment: {experiment_number}")
                return False
            
            # Check if reaction already exists
            existing_reaction_id = self.db_ops.get_reaction_id(experiment_number)
            
            # Create or update reaction
            if existing_reaction_id:
                success = self._update_chemstack_reaction(
                    mapping, existing_reaction_id, transformed_payload
                )
                operation = "updated"
            else:
                reaction_id = self._create_chemstack_reaction(mapping, transformed_payload)
                if reaction_id:
                    self.db_ops.upsert_reaction_experiment(experiment_number, reaction_id)
                    success = True
                    operation = "created"
                else:
                    success = False
                    operation = "create"
            
            if success:
                logger.info(f"Successfully {operation} reaction for experiment: {experiment_number}")
            else:
                logger.error(f"Failed to {operation} reaction for experiment: {experiment_number}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error syncing to ChemStack for experiment {experiment_number}: {e}")
            return False
    
    def _create_chemstack_reaction(self, mapping: Dict[str, Any], payload: Dict[str, Any]) -> Optional[str]:
        """Create a new reaction in ChemStack."""
        try:
            url = CHEMSTACK_CREATE_EXPERIMENT_URL.format(
                project_id=mapping['project_id'],
                route_id=mapping['route_id'],
                step_id=mapping['step_id']
            )
            
            response = requests.post(url, json=payload, headers=CHEMSTACK_HEADERS)
            response.raise_for_status()
            
            result = response.json()
            return result.get('id')
            
        except Exception as e:
            logger.error(f"Error creating ChemStack reaction: {e}")
            return None
    
    def _update_chemstack_reaction(self, mapping: Dict[str, Any], reaction_id: str,
                                  payload: Dict[str, Any]) -> bool:
        """Update an existing reaction in ChemStack."""
        try:
            base_url = CHEMSTACK_CREATE_EXPERIMENT_URL.format(
                project_id=mapping['project_id'],
                route_id=mapping['route_id'],
                step_id=mapping['step_id']
            )
            url = f"{base_url}/{reaction_id}"

            response = requests.put(url, json=payload, headers=CHEMSTACK_HEADERS)
            response.raise_for_status()

            return True

        except Exception as e:
            logger.error(f"Error updating ChemStack reaction {reaction_id}: {e}")
            return False
    
    def get_sync_status(self) -> Dict[str, Any]:
        """Get current synchronization status."""
        try:
            return {
                "last_sync_date": self.db_ops.get_last_sync_date().isoformat(),
                "recent_logs": self.db_ops.get_run_logs(5),
                "database_stats": self.db_ops.get_stats(),
                "database_health": self.db_ops.health_check()
            }
        except Exception as e:
            logger.error(f"Error getting sync status: {e}")
            return {"error": str(e)}
    
    def validate_experiments(self, experiment_numbers: List[str]) -> Dict[str, Any]:
        """Validate a list of experiment numbers."""
        results = {}
        for exp_num in experiment_numbers:
            is_valid, message = self.transformer.validate_experiment_number(exp_num)
            normalized = self.transformer.normalize_experiment_number(exp_num)
            results[exp_num] = {
                "valid": is_valid,
                "message": message,
                "normalized": normalized
            }
        return results
    
    def cleanup(self):
        """Clean up resources."""
        try:
            self.db_ops.close_connection()
            logger.info("Resources cleaned up successfully")
        except Exception as e:
            logger.error(f"Error cleaning up resources: {e}")


# Example usage
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    sync = ELNSync()
    
    try:
        # Test experiment validation
        test_experiments = [
            "MS25012-010-R1-Step-1",
            "MS25013-005-R2-Step-2",
            "MS25018-013-R1-S1"
        ]
        
        validation_results = sync.validate_experiments(test_experiments)
        print("Validation Results:")
        for exp, result in validation_results.items():
            print(f"  {exp}: {result}")
        
        # Get sync status
        status = sync.get_sync_status()
        print(f"\nSync Status: {status}")
        
        # Uncomment to run actual sync
        # results = sync.sync_all_protocols()
        # print(f"Sync Results: {results}")
        
    finally:
        sync.cleanup()
