# from airflow.sdk import Variable
import json

# USERNAME = Variable.get('eln_username', default='')
# PASSWORD = Variable.get('eln_password', default='')
# GET_ALL_PROTOCALS = 'https://logilabelnserviceprod.azurewebsites.net/protocol/GetAllprotocols'

# ELN_HEADERS = {
#     'X-TenantID': Variable.get('eln_tenant_id', default=''),
#   'Content-Type': 'application/json'
# }

USERNAME = '<EMAIL>'
PASSWORD = 'Hemu@789'
GET_ALL_PROTOCALS = 'https://logilabelnserviceprod.azurewebsites.net/protocol/GetAllprotocols'

ELN_HEADERS = {
    'X-TenantID': 'mstack2269',
  'Content-Type': 'application/json'
}
GET_PROTOCOLS_PAYLOAD = {
    "sUsername": USERNAME,
    "sPassword": PASSWORD,
    "fromdate" : "2025-08-01",
    "todate" : "2025-08-15"
}

GET_PROTOCOL_BY_ID = 'https://logilabelnserviceprod.azurewebsites.net/protocol/GetAllprotocolscontent?protocolordercode={protocol_id}'



import requests
import json

CHEMSTACK_CLONE_URL = "https://api-stg.chemstack.ai/chemstack/api/v1/projects/{project_id}/routes/clone_from_retro_route/{route_id}"

# payload = json.dumps({})
# headers = {
#   'accept': 'application/json',
#   'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8',
#   'authorization': 'Bearer ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
#   'content-type': 'application/json',
#   'origin': 'https://stg.chemstack.ai',
#   'priority': 'u=1, i',
#   'referer': 'https://stg.chemstack.ai/',
#   'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
#   'sec-ch-ua-mobile': '?0',
#   'sec-ch-ua-platform': '"macOS"',
#   'sec-fetch-dest': 'empty',
#   'sec-fetch-mode': 'cors',
#   'sec-fetch-site': 'same-site',
#   'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
#   'x-tenant-id': '',
#   'x-user-id': ''
# }

CHEMSTACK_CLONE_HEADERS = {
  'accept': 'application/json',
}
CHEMSTACK_CLONE_PAYLOAD = json.dumps({})



import requests

CHEMSTACK_GET_REACTION_BY_EXPERIMENT_NUMBER = "https://api-stg.chemstack.ai/chemstack/api/v1/reactions?experiment_number=MS61"


CHEMSTACK_CREATE_EXPERIMENT_NUMBER = "https://api-stg.chemstack.ai/chemstack/api/v1/projects/{project_id}/routes/{route_id}/steps/{step_id}/reactions"
