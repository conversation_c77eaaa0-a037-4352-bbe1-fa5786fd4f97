import os
from airflow.sdk import Variable
from dotenv import load_dotenv
load_dotenv()
# ELN API Configuration
USERNAME = Variable.get('ELN_USERNAME', os.getenv('ELN_USERNAME'))
PASSWORD = Variable.get('ELN_PASSWORD', os.getenv('ELN_PASSWORD'))
ELN_TENANT_ID = Variable.get('ELN_TENANT_ID', os.getenv('ELN_TENANT_ID'))

# ELN API Endpoints
GET_ALL_PROTOCALS = 'https://logilabelnserviceprod.azurewebsites.net/protocol/GetAllprotocols'
GET_PROTOCOL_BY_ID = 'https://logilabelnserviceprod.azurewebsites.net/protocol/GetAllprotocolscontent?protocolordercode={protocol_id}'

AZURE_BLOB_CONTAINER_NAME = 'chemstack-files'
# ELN Headers
ELN_HEADERS = {
    'X-TenantID': ELN_TENANT_ID,
    'Content-Type': 'application/json'
}

# Default payload template for getting protocols
GET_PROTOCOLS_PAYLOAD = {
    "sUsername": USERNAME,
    "sPassword": PASSWORD,
    "fromdate": "2025-08-08",
    "todate": "2025-08-12"
}

# ChemStack Configuration
CHEMSTACK_BASE_URL = Variable.get('CHEMSTACK_BASE_URL', os.getenv('CHEMSTACK_BASE_URL'))
CHEMSTACK_TENANT_ID = Variable.get('CHEMSTACK_TENANT_ID', os.getenv('CHEMSTACK_TENANT_ID'))
CHEMSTACK_USER_ID = Variable.get('CHEMSTACK_USER_ID', os.getenv('CHEMSTACK_USER_ID'))
CHEMSTACK_AUTH_TOKEN = Variable.get('CHEMSTACK_AUTH_TOKEN', '')
# ChemStack API Endpoints
CHEMSTACK_CLONE_URL = f"{CHEMSTACK_BASE_URL}/chemstack/api/v1/projects/{{project_id}}/routes/clone_from_retro_route/{{route_id}}"
CHEMSTACK_GET_REACTION_BY_EXPERIMENT_NUMBER = f"{CHEMSTACK_BASE_URL}/chemstack/api/v1/reactions?experiment_number={{experiment_number}}"
CHEMSTACK_CREATE_EXPERIMENT_URL = f"{CHEMSTACK_BASE_URL}/chemstack/api/v1/projects/{{project_id}}/routes/{{route_id}}/steps/{{step_id}}/reactions"
CHEMSTACK_SEARCH_EXPERIMENT_URL = f"{CHEMSTACK_BASE_URL}/chemstack/api/v1/step/search?unique_id={{experiment_number}}"

# ChemStack Headers
CHEMSTACK_HEADERS = {
    'accept': 'application/json',
    'accept-language': 'en-US,en;q=0.9',
    'authorization': f'Bearer {CHEMSTACK_AUTH_TOKEN}',
    'content-type': 'application/json',
    'origin': CHEMSTACK_BASE_URL,
    'referer': CHEMSTACK_BASE_URL,
    'x-tenant-id': CHEMSTACK_TENANT_ID,
    'x-user-id': CHEMSTACK_USER_ID
}

# Default payloads
CHEMSTACK_CLONE_HEADERS = {
    'accept': 'application/json',
}
CHEMSTACK_CLONE_PAYLOAD = {}

# Sync Configuration
DEFAULT_SYNC_DAYS = int(os.getenv('DEFAULT_SYNC_DAYS', '90'))
