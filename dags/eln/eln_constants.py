"""
ELN Constants Configuration

Clean configuration file for ELN synchronization system.
Contains API endpoints, headers, and other constants.
"""

import os

# ELN API Configuration
USERNAME = os.getenv('ELN_USERNAME', '<EMAIL>')
PASSWORD = os.getenv('<PERSON>LN_PASSWORD', '<PERSON><PERSON>@789')
ELN_TENANT_ID = os.getenv('ELN_TENANT_ID', 'mstack2269')

# ELN API Endpoints
GET_ALL_PROTOCALS = 'https://logilabelnserviceprod.azurewebsites.net/protocol/GetAllprotocols'
GET_PROTOCOL_BY_ID = 'https://logilabelnserviceprod.azurewebsites.net/protocol/GetAllprotocolscontent?protocolordercode={protocol_id}'

# ELN Headers
ELN_HEADERS = {
    'X-TenantID': ELN_TENANT_ID,
    'Content-Type': 'application/json'
}

# Default payload template for getting protocols
GET_PROTOCOLS_PAYLOAD = {
    "sUsername": USERNAME,
    "sPassword": PASSWORD,
    "fromdate": "2025-08-01",
    "todate": "2025-08-15"
}

# ChemStack Configuration
CHEMSTACK_BASE_URL = os.getenv('CHEMSTACK_BASE_URL', 'https://api-stg.chemstack.ai')
CHEMSTACK_TENANT_ID = os.getenv('CHEMSTACK_TENANT_ID', '6cd50e19-6a16-45ac-9c4e-cb8c908eb72e')
CHEMSTACK_USER_ID = os.getenv('CHEMSTACK_USER_ID', '09b3ec46-0c97-46b0-9488-7af04e1d27f2')
CHEMSTACK_AUTH_TOKEN = os.getenv('CHEMSTACK_AUTH_TOKEN', '******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************')

# ChemStack API Endpoints
CHEMSTACK_CLONE_URL = f"{CHEMSTACK_BASE_URL}/chemstack/api/v1/projects/{{project_id}}/routes/clone_from_retro_route/{{route_id}}"
CHEMSTACK_GET_REACTION_BY_EXPERIMENT_NUMBER = f"{CHEMSTACK_BASE_URL}/chemstack/api/v1/reactions?experiment_number={{experiment_number}}"
CHEMSTACK_CREATE_EXPERIMENT_URL = f"{CHEMSTACK_BASE_URL}/chemstack/api/v1/projects/{{project_id}}/routes/{{route_id}}/steps/{{step_id}}/reactions"

# ChemStack Headers
CHEMSTACK_HEADERS = {
    'accept': 'application/json',
    'accept-language': 'en-US,en;q=0.9',
    'authorization': f'Bearer {CHEMSTACK_AUTH_TOKEN}',
    'content-type': 'application/json',
    'origin': 'https://stg.chemstack.ai',
    'referer': 'https://stg.chemstack.ai/',
    'x-tenant-id': CHEMSTACK_TENANT_ID,
    'x-user-id': CHEMSTACK_USER_ID
}

# Default payloads
CHEMSTACK_CLONE_HEADERS = {
    'accept': 'application/json',
}
CHEMSTACK_CLONE_PAYLOAD = {}

# Sync Configuration
DEFAULT_SYNC_DAYS = int(os.getenv('DEFAULT_SYNC_DAYS', '90'))
