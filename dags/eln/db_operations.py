"""
Simple Database Operations Module

This module handles all MongoDB operations for the ELN data synchronization system.
Provides a clean interface for CRUD operations on ELN data, reaction experiments,
and run logs.
"""

import logging
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

# Add paths for imports
sys.path.insert(0, '/opt/airflow/dag_dependencies')
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from config.db_config import DatabaseConfig
except ImportError:
    # Fallback if config not available
    class DatabaseConfig:
        @staticmethod
        def get_chem_stack_mongo_params():
            return {
                'connection_string': os.getenv('CHEM_STACK_MONGO_CONNECTION_STRING', 'mongodb://localhost:27017'),
                'database_name': os.getenv('CHEM_STACK_MONGO_DB_NAME', 'chemstack_eln')
            }

logger = logging.getLogger(__name__)

# Collection names
ELN_DATA_COLLECTION = "eln_data"
ELN_RUN_LOG_COLLECTION = "eln_run_log"
REACTION_EXPERIMENT_DATA_COLLECTION = "reaction_experiment_data"
DEFAULT_SYNC_DAYS = 90


class DatabaseOperations:
    """
    Simple database operations for ELN data synchronization.

    Handles:
    - ELN data storage and retrieval
    - Reaction experiment tracking
    - Run log management
    """

    def __init__(self, connection_string: str = None, database_name: str = None):
        """
        Initialize database connection.

        Args:
            connection_string: MongoDB connection string
            database_name: Database name to use
        """
        # Get config from DatabaseConfig if not provided
        if not connection_string or not database_name:
            try:
                config = DatabaseConfig.get_chem_stack_mongo_params()
                self.connection_string = connection_string or config.get('connection_string')
                self.database_name = database_name or config.get('database_name')
            except Exception as e:
                logger.warning(f"Could not get database config: {e}")
                self.connection_string = connection_string or 'mongodb://localhost:27017'
                self.database_name = database_name or 'chemstack_eln'
        else:
            self.connection_string = connection_string
            self.database_name = database_name

        self._client = None
        self._database = None

        # Try to import pymongo
        try:
            from pymongo import MongoClient
            self.MongoClient = MongoClient
        except ImportError:
            logger.error("pymongo not installed. Install with: pip install pymongo")
            self.MongoClient = None
    
    def _get_client(self):
        """Get or create MongoDB client."""
        if not self.MongoClient:
            raise Exception("pymongo not available")
            
        if self._client is None:
            self._client = self.MongoClient(self.connection_string)
        return self._client
    
    def _get_database(self):
        """Get or create database instance."""
        if self._database is None:
            self._database = self._get_client()[self.database_name]
        return self._database
    
    def _get_collection(self, collection_name: str):
        """Get collection instance."""
        return self._get_database()[collection_name]
    
    def close_connection(self):
        """Close database connection."""
        if self._client:
            self._client.close()
            self._client = None
            self._database = None
    
    # ELN Data Operations
    def upsert_eln_data(self, protocol_id: str, data: Dict[str, Any]) -> bool:
        """
        Insert or update ELN data for a protocol.
        
        Args:
            protocol_id: Protocol ID to upsert
            data: ELN data to store
            
        Returns:
            bool: True if operation successful
        """
        try:
            collection = self._get_collection(ELN_DATA_COLLECTION)
            query = {"protocol_id": protocol_id}
            
            # Add timestamps
            now = datetime.now()
            data.update({
                "protocol_id": protocol_id,
                "updated_at": now
            })
            
            # Add created_at only for new documents
            if not collection.find_one(query):
                data["created_at"] = now
            
            collection.update_one(
                query,
                {"$set": data},
                upsert=True
            )

            logger.info(f"Upserted ELN data for protocol: {protocol_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error upserting ELN data for protocol {protocol_id}: {e}")
            return False
    
    def get_eln_data(self, protocol_id: str) -> Optional[Dict[str, Any]]:
        """
        Get ELN data for a specific protocol.
        
        Args:
            protocol_id: Protocol ID to retrieve
            
        Returns:
            Dict containing ELN data or None if not found
        """
        try:
            collection = self._get_collection(ELN_DATA_COLLECTION)
            return collection.find_one({"protocol_id": protocol_id})
        except Exception as e:
            logger.error(f"Error getting ELN data for protocol {protocol_id}: {e}")
            return None
    
    # Reaction Experiment Operations
    def upsert_reaction_experiment(self, experiment_number: str, reaction_id: str) -> bool:
        """
        Insert or update reaction experiment mapping.
        
        Args:
            experiment_number: Experiment number
            reaction_id: ChemStack reaction ID
            
        Returns:
            bool: True if operation successful
        """
        try:
            collection = self._get_collection(REACTION_EXPERIMENT_DATA_COLLECTION)
            query = {"experiment_number": experiment_number}
            
            now = datetime.now()
            data = {
                "experiment_number": experiment_number,
                "reaction_id": reaction_id,
                "updated_at": now
            }
            
            # Add created_at only for new documents
            if not collection.find_one(query):
                data["created_at"] = now
            
            collection.update_one(
                query,
                {"$set": data},
                upsert=True
            )
            
            logger.info(f"Upserted reaction experiment: {experiment_number} -> {reaction_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error upserting reaction experiment {experiment_number}: {e}")
            return False
    
    def get_reaction_id(self, experiment_number: str) -> Optional[str]:
        """
        Get reaction ID for an experiment number.
        
        Args:
            experiment_number: Experiment number to look up
            
        Returns:
            Reaction ID or None if not found
        """
        try:
            collection = self._get_collection(REACTION_EXPERIMENT_DATA_COLLECTION)
            result = collection.find_one({"experiment_number": experiment_number})
            return result.get("reaction_id") if result else None
        except Exception as e:
            logger.error(f"Error getting reaction ID for experiment {experiment_number}: {e}")
            return None
    
    # Run Log Operations
    def insert_run_log(self, last_modified_date: datetime, error: str = "") -> bool:
        """
        Insert a new run log entry.
        
        Args:
            last_modified_date: Last modified date from the sync
            error: Error message if any
            
        Returns:
            bool: True if operation successful
        """
        try:
            collection = self._get_collection(ELN_RUN_LOG_COLLECTION)
            now = datetime.now()
            
            data = {
                "created_at": now,
                "updated_at": now,
                "last_modified_date": last_modified_date,
                "error": error,
                "status": "error" if error else "success"
            }
            
            collection.insert_one(data)
            logger.info(f"Inserted run log with status: {'error' if error else 'success'}")
            return True
            
        except Exception as e:
            logger.error(f"Error inserting run log: {e}")
            return False
    
    def get_last_sync_date(self) -> datetime:
        """
        Get the last successful sync date.
        
        Returns:
            datetime: Last sync date or default date if no previous sync
        """
        try:
            collection = self._get_collection(ELN_RUN_LOG_COLLECTION)
            result = collection.find_one(
                {"status": "success"},
                sort=[("created_at", -1)]
            )
            
            if result and result.get("last_modified_date"):
                return result["last_modified_date"]
            else:
                # Default to 90 days ago if no previous sync
                return datetime.now() - timedelta(days=DEFAULT_SYNC_DAYS)
                
        except Exception as e:
            logger.error(f"Error getting last sync date: {e}")
            return datetime.now() - timedelta(days=DEFAULT_SYNC_DAYS)
    
    def get_run_logs(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get recent run logs.
        
        Args:
            limit: Maximum number of logs to return
            
        Returns:
            List of run log documents
        """
        try:
            collection = self._get_collection(ELN_RUN_LOG_COLLECTION)
            return list(collection.find().sort("created_at", -1).limit(limit))
        except Exception as e:
            logger.error(f"Error getting run logs: {e}")
            return []
    
    # Utility Methods
    def health_check(self) -> bool:
        """
        Check database connectivity.
        
        Returns:
            bool: True if database is accessible
        """
        try:
            self._get_client().admin.command('ping')
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get database statistics.
        
        Returns:
            Dict containing collection statistics
        """
        try:
            db = self._get_database()
            stats = {}
            
            collections = [ELN_DATA_COLLECTION, ELN_RUN_LOG_COLLECTION, REACTION_EXPERIMENT_DATA_COLLECTION]
            for collection_name in collections:
                try:
                    collection = db[collection_name]
                    count = collection.count_documents({})
                    stats[collection_name] = {
                        "count": count,
                        "size": 0  # Simplified - don't get size to avoid errors
                    }
                except Exception as e:
                    logger.warning(f"Could not get stats for {collection_name}: {e}")
                    stats[collection_name] = {"count": 0, "size": 0}
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting database stats: {e}")
            return {}


# Example usage
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    db_ops = DatabaseOperations()
    
    try:
        # Test database connection
        health = db_ops.health_check()
        print(f"Database health: {health}")
        
        if health:
            # Get statistics
            stats = db_ops.get_stats()
            print(f"Database stats: {stats}")
            
            # Get last sync date
            last_sync = db_ops.get_last_sync_date()
            print(f"Last sync date: {last_sync}")
        
    finally:
        db_ops.close_connection()
