"""
Database Operations Module for ELN Synchronization

This module provides a wrapper around your existing mongo_operations.py
to handle ELN-specific database operations.
"""

import logging
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

# Add paths for imports
sys.path.insert(0, '/opt/airflow/dag_dependencies')
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

logger = logging.getLogger(__name__)

try:
    from db.mongo_operations import MongoOperations
    from config.db_config import DatabaseConfig
except ImportError as e:
    logger.error(f"Could not import existing mongo operations: {e}")
    raise

# Collection names
ELN_DATA_COLLECTION = "eln_data"
ELN_RUN_LOG_COLLECTION = "eln_run_log"
REACTION_EXPERIMENT_DATA_COLLECTION = "reaction_experiment_data"
DEFAULT_SYNC_DAYS = 90


class DatabaseOperations:
    """
    ELN-specific database operations using your existing MongoOperations.

    This is a wrapper around your existing mongo_operations.py that provides
    ELN-specific methods for data synchronization.
    """

    def __init__(self):
        """Initialize database operations using existing config."""
        try:
            # Get database config using your existing configuration
            config = DatabaseConfig.get_chem_stack_mongo_params()

            # Initialize using your existing MongoOperations
            self.mongo_ops = MongoOperations(
                connection_string=config['connection_string'],
                database_name=config['database_name']
            )

            logger.info(f"DatabaseOperations initialized with database: {config['database_name']}")

        except Exception as e:
            logger.error(f"Failed to initialize DatabaseOperations: {e}")
            raise

    def close_connection(self):
        """Close database connection."""
        try:
            self.mongo_ops.close()
        except Exception as e:
            logger.warning(f"Error closing connection: {e}")

    # ELN Data Operations
    def upsert_eln_data(self, protocol_id: str, data: Dict[str, Any]) -> bool:
        """
        Insert or update ELN data for a protocol.

        Args:
            protocol_id: Protocol ID to upsert
            data: ELN data to store

        Returns:
            bool: True if operation successful
        """
        try:
            query = {"protocol_id": protocol_id}

            # Add timestamps
            now = datetime.now()
            data.update({
                "protocol_id": protocol_id,
                "updated_at": now
            })

            # Check if document exists to add created_at
            existing = self.mongo_ops.read_data(ELN_DATA_COLLECTION, query)
            if not existing:
                data["created_at"] = now

            # Use your existing upsert method
            self.mongo_ops.upsert_data(
                ELN_DATA_COLLECTION,
                query,
                {"$set": data}
            )

            logger.info(f"Upserted ELN data for protocol: {protocol_id}")
            return True

        except Exception as e:
            logger.error(f"Error upserting ELN data for protocol {protocol_id}: {e}")
            return False

    def get_eln_data(self, protocol_id: str) -> Optional[Dict[str, Any]]:
        """
        Get ELN data for a specific protocol.

        Args:
            protocol_id: Protocol ID to retrieve

        Returns:
            Dict containing ELN data or None if not found
        """
        try:
            results = self.mongo_ops.read_data(ELN_DATA_COLLECTION, {"protocol_id": protocol_id})
            return results[0] if results else None
        except Exception as e:
            logger.error(f"Error getting ELN data for protocol {protocol_id}: {e}")
            return None

    # Reaction Experiment Operations
    def upsert_reaction_experiment(self, experiment_number: str, reaction_id: str) -> bool:
        """
        Insert or update reaction experiment mapping.

        Args:
            experiment_number: Experiment number
            reaction_id: ChemStack reaction ID

        Returns:
            bool: True if operation successful
        """
        try:
            query = {"experiment_number": experiment_number}

            now = datetime.now()
            data = {
                "experiment_number": experiment_number,
                "reaction_id": reaction_id,
                "updated_at": now
            }

            # Check if document exists to add created_at
            existing = self.mongo_ops.read_data(REACTION_EXPERIMENT_DATA_COLLECTION, query)
            if not existing:
                data["created_at"] = now

            # Use your existing upsert method
            self.mongo_ops.upsert_data(
                REACTION_EXPERIMENT_DATA_COLLECTION,
                query,
                {"$set": data}
            )

            logger.info(f"Upserted reaction experiment: {experiment_number} -> {reaction_id}")
            return True

        except Exception as e:
            logger.error(f"Error upserting reaction experiment {experiment_number}: {e}")
            return False

    def get_reaction_id(self, experiment_number: str) -> Optional[str]:
        """
        Get reaction ID for an experiment number.

        Args:
            experiment_number: Experiment number to look up

        Returns:
            Reaction ID or None if not found
        """
        try:
            results = self.mongo_ops.read_data(REACTION_EXPERIMENT_DATA_COLLECTION, {"experiment_number": experiment_number})
            return results[0].get("reaction_id") if results else None
        except Exception as e:
            logger.error(f"Error getting reaction ID for experiment {experiment_number}: {e}")
            return None

    # Run Log Operations
    def insert_run_log(self, last_modified_date: datetime, error: str = "") -> bool:
        """
        Insert a new run log entry.

        Args:
            last_modified_date: Last modified date from the sync
            error: Error message if any

        Returns:
            bool: True if operation successful
        """
        try:
            now = datetime.now()

            data = {
                "created_at": now,
                "updated_at": now,
                "last_modified_date": last_modified_date,
                "error": error,
                "status": "error" if error else "success"
            }

            # Use your existing write_data method
            self.mongo_ops.write_data(ELN_RUN_LOG_COLLECTION, data)
            logger.info(f"Inserted run log with status: {'error' if error else 'success'}")
            return True

        except Exception as e:
            logger.error(f"Error inserting run log: {e}")
            return False

    def get_last_sync_date(self) -> datetime:
        """
        Get the last successful sync date.

        Returns:
            datetime: Last sync date or default date if no previous sync
        """
        try:
            # Use your existing read_data method with sorting
            results = self.mongo_ops.read_data(
                ELN_RUN_LOG_COLLECTION,
                query={"status": "success"},
                sort=[("created_at", -1)]
            )

            if results and results[0].get("last_modified_date"):
                return results[0]["last_modified_date"]
            else:
                # Default to 90 days ago if no previous sync
                return datetime.now() - timedelta(days=DEFAULT_SYNC_DAYS)

        except Exception as e:
            logger.error(f"Error getting last sync date: {e}")
            return datetime.now() - timedelta(days=DEFAULT_SYNC_DAYS)

    def get_run_logs(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get recent run logs.

        Args:
            limit: Maximum number of logs to return

        Returns:
            List of run log documents
        """
        try:
            # Use your existing read_data_paginated method
            results, _ = self.mongo_ops.read_data_paginated(
                ELN_RUN_LOG_COLLECTION,
                page=1,
                page_size=limit,
                sort=[("created_at", -1)]
            )
            return results
        except Exception as e:
            logger.error(f"Error getting run logs: {e}")
            return []

    # Utility Methods
    def health_check(self) -> bool:
        """
        Check database connectivity.

        Returns:
            bool: True if database is accessible
        """
        try:
            # Use your existing mongo operations to test connectivity
            # Try to read from any collection (even if empty)
            self.mongo_ops.read_data("test_collection", {})
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False

    def get_stats(self) -> Dict[str, Any]:
        """
        Get database statistics.

        Returns:
            Dict containing collection statistics
        """
        try:
            stats = {}

            collections = [ELN_DATA_COLLECTION, ELN_RUN_LOG_COLLECTION, REACTION_EXPERIMENT_DATA_COLLECTION]
            for collection_name in collections:
                try:
                    # Use your existing read_data_paginated to get count
                    _, count = self.mongo_ops.read_data_paginated(
                        collection_name,
                        page=1,
                        page_size=1
                    )
                    stats[collection_name] = {
                        "count": count,
                        "size": 0  # Simplified - don't get size to avoid errors
                    }
                except Exception as e:
                    logger.warning(f"Could not get stats for {collection_name}: {e}")
                    stats[collection_name] = {"count": 0, "size": 0}

            return stats

        except Exception as e:
            logger.error(f"Error getting database stats: {e}")
            return {}


# Example usage
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    db_ops = DatabaseOperations()
    
    try:
        # Test database connection
        health = db_ops.health_check()
        print(f"Database health: {health}")
        
        if health:
            # Get statistics
            stats = db_ops.get_stats()
            print(f"Database stats: {stats}")
            
            # Get last sync date
            last_sync = db_ops.get_last_sync_date()
            print(f"Last sync date: {last_sync}")
        
    finally:
        db_ops.close_connection()
