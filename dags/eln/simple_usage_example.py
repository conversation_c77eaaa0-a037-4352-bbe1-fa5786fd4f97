"""
Simple Usage Example for ELN Synchronization System

This example shows how to use the simplified ELN sync system with:
- Separate DB operations
- Improved transformation with regex normalization
- Clean, easy-to-understand code
"""

import logging
from simple_eln_sync import SimpleELNSync
from data_transformer import DataTransformer
from db_operations import DatabaseOperations

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def example_experiment_normalization():
    """Example of experiment number normalization."""
    print("=" * 60)
    print("EXPERIMENT NUMBER NORMALIZATION EXAMPLE")
    print("=" * 60)
    
    transformer = DataTransformer()
    
    # Test various experiment number formats
    test_experiments = [
        "MS25012-010-R1-Step-1",  # Your original format
        "MS25012-012-R1-Step-1",  # Another variation
        "MS25013-005-R2-Step-2",  # Different project
        "MS25018-013-R1-S1",      # Different format
        "MS25007-R1-010",         # Route first format
        "MS25005",                # Just project code
        "INVALID-123"             # Invalid format
    ]
    
    print("Testing experiment number normalization:")
    for exp in test_experiments:
        normalized = transformer.normalize_experiment_number(exp)
        is_valid, message = transformer.validate_experiment_number(exp)
        
        print(f"  {exp:<25} -> {normalized or 'FAILED':<20} (Valid: {is_valid})")
        if not is_valid:
            print(f"    └─ {message}")
    
    print()


def example_mapping_lookup():
    """Example of mapping lookup."""
    print("=" * 60)
    print("MAPPING LOOKUP EXAMPLE")
    print("=" * 60)
    
    transformer = DataTransformer()
    
    # Test mapping lookup for different experiments
    test_experiments = [
        "MS25012-007-R1-S1",
        "MS25018-013-R1-S1", 
        "MS25013-005-R2-S2"
    ]
    
    print("Testing mapping lookup:")
    for exp in test_experiments:
        mapping = transformer.get_best_mapping(exp)
        
        print(f"  {exp}:")
        if mapping:
            print(f"    Project ID: {mapping.get('project_id', 'N/A')}")
            print(f"    Route ID:   {mapping.get('route_id', 'N/A')}")
            print(f"    Step ID:    {mapping.get('step_id', 'N/A')}")
            print(f"    Experiment: {mapping.get('experiment_number', 'N/A')}")
        else:
            print("    └─ No mapping found")
        print()


def example_database_operations():
    """Example of database operations."""
    print("=" * 60)
    print("DATABASE OPERATIONS EXAMPLE")
    print("=" * 60)
    
    db_ops = DatabaseOperations()
    
    try:
        # Check database health
        health = db_ops.health_check()
        print(f"Database health: {'✓ OK' if health else '✗ FAILED'}")
        
        if not health:
            print("Database is not accessible - check your MongoDB connection")
            return
        
        # Get database statistics
        stats = db_ops.get_stats()
        print("\nDatabase statistics:")
        for collection, info in stats.items():
            print(f"  {collection}: {info.get('count', 0)} records")
        
        # Get last sync date
        last_sync = db_ops.get_last_sync_date()
        print(f"\nLast sync date: {last_sync}")
        
        # Get recent run logs
        logs = db_ops.get_run_logs(3)
        print(f"\nRecent run logs ({len(logs)} entries):")
        for log in logs:
            status = log.get('status', 'unknown')
            created = log.get('created_at', 'unknown')
            error = log.get('error', 'No error')
            print(f"  {created}: {status} - {error}")
        
    except Exception as e:
        print(f"Database operations failed: {e}")
    finally:
        db_ops.close_connection()
    
    print()


def example_sync_status():
    """Example of getting sync status."""
    print("=" * 60)
    print("SYNC STATUS EXAMPLE")
    print("=" * 60)
    
    sync = SimpleELNSync()
    
    try:
        status = sync.get_sync_status()
        
        if "error" in status:
            print(f"Error getting sync status: {status['error']}")
            return
        
        print("Current sync status:")
        print(f"  Last sync: {status.get('last_sync_date', 'Never')}")
        print(f"  Database health: {'✓ OK' if status.get('database_health') else '✗ FAILED'}")
        
        db_stats = status.get('database_stats', {})
        print(f"  Database records:")
        for collection, stats in db_stats.items():
            print(f"    {collection}: {stats.get('count', 0)} records")
        
        recent_logs = status.get('recent_logs', [])
        print(f"  Recent logs: {len(recent_logs)} entries")
        
    except Exception as e:
        print(f"Error getting sync status: {e}")
    finally:
        sync.cleanup()
    
    print()


def example_validate_your_experiments():
    """Example of validating your 8 molecules."""
    print("=" * 60)
    print("YOUR 8 MOLECULES VALIDATION EXAMPLE")
    print("=" * 60)
    
    sync = SimpleELNSync()
    
    # Your 8 molecules with various experiment formats
    your_experiments = [
        "MS25012-010-R1-Step-1",  # Should normalize to MS25012-007-R1-S1
        "MS25013-005-R2-Step-2",  # Should normalize to MS25013-007-R2-S2
        "MS25005-003-R1-Step-1",  # Should normalize to MS25005-007-R1-S1
        "MS25007-008-R1-Step-1",  # Should normalize to MS25007-007-R1-S1
        "MS25016-001-R1-Step-1",  # Should normalize to MS25016-007-R1-S1
        "MS25010-002-R1-Step-1",  # Should normalize to MS25010-007-R1-S1
        "MS25017-004-R1-Step-1",  # Should normalize to MS25017-007-R1-S1
        "MS25018-013-R1-S1"       # Should normalize to MS25018-007-R1-S1
    ]
    
    try:
        validation_results = sync.validate_experiments(your_experiments)
        
        print("Validation results for your 8 molecules:")
        for original, result in validation_results.items():
            status = "✓" if result['valid'] else "✗"
            normalized = result.get('normalized', 'FAILED')
            
            print(f"  {status} {original:<25} -> {normalized}")
            if not result['valid']:
                print(f"    └─ {result['message']}")
        
        # Summary
        valid_count = sum(1 for r in validation_results.values() if r['valid'])
        total_count = len(validation_results)
        print(f"\nSummary: {valid_count}/{total_count} experiments are valid and supported")
        
    except Exception as e:
        print(f"Error validating experiments: {e}")
    finally:
        sync.cleanup()
    
    print()


def example_single_protocol_sync():
    """Example of syncing a single protocol (commented out for safety)."""
    print("=" * 60)
    print("SINGLE PROTOCOL SYNC EXAMPLE")
    print("=" * 60)
    
    print("To sync a single protocol, uncomment the code below:")
    print()
    print("sync = SimpleELNSync()")
    print("try:")
    print("    success = sync.sync_single_protocol('1000129')")
    print("    print(f'Sync result: {success}')")
    print("finally:")
    print("    sync.cleanup()")
    print()
    
    # Uncomment below to actually sync a protocol
    # sync = SimpleELNSync()
    # try:
    #     protocol_id = "1000129"  # Replace with actual protocol ID
    #     success = sync.sync_single_protocol(protocol_id)
    #     print(f"Sync result for protocol {protocol_id}: {success}")
    # finally:
    #     sync.cleanup()


def example_full_sync():
    """Example of running a full sync (commented out for safety)."""
    print("=" * 60)
    print("FULL SYNC EXAMPLE")
    print("=" * 60)
    
    print("To run a full sync, uncomment the code below:")
    print()
    print("sync = SimpleELNSync()")
    print("try:")
    print("    results = sync.sync_all_protocols()")
    print("    print(f'Sync completed: {results}')")
    print("finally:")
    print("    sync.cleanup()")
    print()
    
    # Uncomment below to actually run full sync
    # sync = SimpleELNSync()
    # try:
    #     results = sync.sync_all_protocols()
    #     print(f"Full sync results: {results}")
    # finally:
    #     sync.cleanup()


def main():
    """Run all examples."""
    print("🚀 SIMPLE ELN SYNCHRONIZATION SYSTEM EXAMPLES")
    print("=" * 60)
    print()
    
    try:
        # Run safe examples (no actual sync operations)
        example_experiment_normalization()
        example_mapping_lookup()
        example_database_operations()
        example_sync_status()
        example_validate_your_experiments()
        
        # Show sync examples (commented out for safety)
        example_single_protocol_sync()
        example_full_sync()
        
        print("✅ All examples completed successfully!")
        print()
        print("Next steps:")
        print("1. Review the validation results for your 8 molecules")
        print("2. Update any missing mappings in eln_mapper.py")
        print("3. Test with a single protocol sync first")
        print("4. Run full sync when ready")
        
    except Exception as e:
        print(f"❌ Examples failed: {e}")
        logger.error(f"Examples failed: {e}")


if __name__ == "__main__":
    main()
