"""
Simple Data Transformation Module

This module handles the transformation of ELN data to ChemStack format
with improved experiment number normalization using regex patterns.
"""

import re
import logging
from typing import Dict, Optional, Any

from .eln_helpers import transform_payload_fixed
from .eln_mapper import ELN_MAPPING

logger = logging.getLogger(__name__)


class DataTransformer:
    """
    Simple data transformer for ELN to ChemStack conversion.

    Features:
    - Experiment number normalization with regex
    - Improved mapping logic
    - Clean transformation pipeline
    """

    def __init__(self):
        """Initialize the transformer."""
        # Regex patterns for experiment number normalization
        self.experiment_patterns = [
            r'^(MS\d{5})-(\d{3})-R(\d+)-Step-(\d+)$',  # MS25012-010-R1-Step-1
            r'^(MS\d{5})-(\d{3})-R(\d+)-S(\d+)$',      # MS25012-010-R1-S1
            r'^(MS\d{5})-R(\d+)-S(\d+)-(\d{3})$',      # MS25012-R1-S1-010
            r'^(MS\d{5})-(\d{3})-R(\d+)$',             # MS25012-010-R1
            r'^(MS\d{5})$'                             # MS25012
        ]

        # Project code to normalized experiment mapping
        self.project_mapping = {
            'MS25012': '007',
            'MS25013': '007',
            'MS25005': '007',
            'MS25007': '007',
            'MS25016': '007',
            'MS25010': '007',
            'MS25017': '007',
            'MS25018': '007'
        }

    def normalize_experiment_number(self, experiment_number: str) -> Optional[str]:
        """
        Normalize experiment number to standard format using regex.

        Args:
            experiment_number: Raw experiment number

        Returns:
            Normalized experiment number in format MS25012-007-R1-S1
        """
        if not experiment_number:
            return None

        experiment_number = experiment_number.strip().upper()

        # Try each pattern
        for pattern in self.experiment_patterns:
            match = re.match(pattern, experiment_number)
            if match:
                groups = match.groups()
                project_code = groups[0]

                # Check if project is supported
                if project_code not in self.project_mapping:
                    logger.warning(f"Project code {project_code} not supported")
                    return None

                # Get normalized experiment number
                normalized_exp = self.project_mapping[project_code]

                # Extract route and step based on pattern
                if len(groups) >= 4:  # Has route and step
                    if 'Step' in experiment_number:
                        route, step = groups[2], groups[3]
                    else:
                        route, step = groups[2], groups[3]
                elif len(groups) >= 3:  # Has route
                    route, step = groups[2], '1'
                else:  # Just project code
                    route, step = '1', '1'

                normalized = f"{project_code}-{normalized_exp}-R{route}-S{step}"
                logger.info(f"Normalized {experiment_number} -> {normalized}")
                return normalized

        logger.warning(f"Could not normalize experiment number: {experiment_number}")
        return None

    def get_best_mapping(self, experiment_number: str, source_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Get the best mapping for an experiment number.

        Args:
            experiment_number: Original experiment number
            source_data: Additional source data for context

        Returns:
            Mapping dictionary or empty dict if not found
        """
        # First try to normalize the experiment number
        normalized = self.normalize_experiment_number(experiment_number)
        if normalized:
            experiment_number = normalized

        # Try direct lookup in ELN_MAPPING
        if experiment_number in ELN_MAPPING:
            return ELN_MAPPING[experiment_number]

        # Try project code only
        project_code = experiment_number.split('-')[0] if '-' in experiment_number else experiment_number
        if project_code in ELN_MAPPING:
            return ELN_MAPPING[project_code]

        # Try fuzzy matching with source data
        if source_data:
            route_info = source_data.get("route_information", {})
            project_code = route_info.get("project_code", "").strip()
            route_number = route_info.get("route_number", "").strip()
            step_name = route_info.get("step_name", "").strip()

            # Build candidate keys
            candidates = []
            if project_code and step_name:
                candidates.extend([
                    f"{project_code}-Step-{step_name}",
                    f"{project_code}-S{step_name}",
                    f"{project_code}-{step_name}"
                ])

            if project_code and route_number:
                candidates.extend([
                    f"{project_code}-R{route_number}",
                    f"{project_code}-{route_number}"
                ])

            if project_code:
                candidates.append(project_code)

            # Try each candidate
            for candidate in candidates:
                if candidate in ELN_MAPPING:
                    return ELN_MAPPING[candidate]

        logger.warning(f"No mapping found for experiment: {experiment_number}")
        return {}

    def transform_eln_to_chemstack(self, source_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform ELN data to ChemStack format.

        Args:
            source_data: Raw ELN data

        Returns:
            Transformed ChemStack payload
        """
        try:
            # Extract experiment number
            experiment_number = self.extract_experiment_number(source_data)
            if not experiment_number:
                logger.error("Could not extract experiment number from source data")
                return {}

            # Get mapping
            mapping = self.get_best_mapping(experiment_number, source_data)
            if not mapping:
                logger.error(f"No mapping found for experiment: {experiment_number}")
                return {}

            # Create mapping dict for transform function
            mapping_dict = {experiment_number: mapping}

            # Transform using existing function
            transformed = transform_payload_fixed(source_data, mapping_dict)

            logger.info(f"Successfully transformed ELN data for experiment: {experiment_number}")
            return transformed

        except Exception as e:
            logger.error(f"Error transforming ELN data: {e}")
            return {}

    def extract_experiment_number(self, source_data: Dict[str, Any]) -> Optional[str]:
        """
        Extract experiment number from source data.

        Args:
            source_data: Raw ELN data

        Returns:
            Experiment number or None if not found
        """
        # Try multiple locations for experiment number
        locations = [
            ("experiment_overview", "experiment_number"),
            ("route_information", "project_code"),
            ("protocol_name",),
        ]

        for location in locations:
            value = source_data
            for key in location:
                value = value.get(key) if isinstance(value, dict) else None
                if not value:
                    break

            if value and isinstance(value, str):
                return value.strip()

        return None

    def validate_experiment_number(self, experiment_number: str) -> tuple[bool, str]:
        """
        Validate experiment number format.

        Args:
            experiment_number: Experiment number to validate

        Returns:
            Tuple of (is_valid, message)
        """
        if not experiment_number:
            return False, "Experiment number is empty"

        normalized = self.normalize_experiment_number(experiment_number)
        if normalized:
            return True, f"Valid experiment number. Normalized to: {normalized}"
        else:
            return False, f"Could not normalize experiment number: {experiment_number}"
