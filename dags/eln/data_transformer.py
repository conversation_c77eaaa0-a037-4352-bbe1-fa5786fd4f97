"""
Data Transformation Service Module

This module handles the transformation of ELN data to ChemStack format.
It uses the helper functions from eln_helpers.py and integrates with
the mapper service for proper ID resolution.
"""

import uuid
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

from .eln_helpers import (
    transform_payload_fixed,
    as_iso,
    extract_date,
    combine_date_and_time,
    parse_temperature_value,
    convert_to_utc,
    build_observation_item,
    map_analytical_reports_to_observation,
    map_reactants_to_inputs,
    to_identifiers,
    get_tlc_images,
    build_procedure_text,
    safe_uuid
)
from .mapper_service import EnhancedMapperService, MappingResult

logger = logging.getLogger(__name__)


class DataTransformationError(Exception):
    """Custom exception for data transformation errors."""
    pass


class DataTransformationService:
    """
    Service for transforming ELN data to ChemStack format.
    
    This service:
    1. Transforms ELN data structure to ChemStack payload format
    2. Handles mapping resolution using the enhanced mapper service
    3. Validates and cleans data during transformation
    4. Provides detailed logging and error handling
    """
    
    def __init__(self, mapper_service: EnhancedMapperService = None):
        """
        Initialize the transformation service.
        
        Args:
            mapper_service: Enhanced mapper service instance
        """
        self.mapper_service = mapper_service or EnhancedMapperService()
    
    def transform_eln_to_chemstack(self, source_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform ELN data to ChemStack format.
        
        Args:
            source_data: Raw ELN data
            
        Returns:
            Transformed ChemStack payload
            
        Raises:
            DataTransformationError: If transformation fails
        """
        try:
            # Extract experiment number for mapping
            experiment_number = self._extract_experiment_number(source_data)
            if not experiment_number:
                raise DataTransformationError("Could not extract experiment number from source data")
            
            # Get mapping information
            mapping_result = self.mapper_service.get_mapping(experiment_number, source_data)
            if not self.mapper_service.validate_mapping(mapping_result):
                logger.warning(f"Incomplete mapping for experiment {experiment_number}, using fallback")
                mapping_result = self._create_fallback_mapping(experiment_number, source_data)
            
            # Transform the payload
            transformed_payload = self._transform_payload(source_data, mapping_result)
            
            # Validate the transformed payload
            self._validate_payload(transformed_payload)
            
            logger.info(f"Successfully transformed ELN data for experiment: {experiment_number}")
            return transformed_payload
            
        except Exception as e:
            logger.error(f"Error transforming ELN data: {e}")
            raise DataTransformationError(f"Transformation failed: {e}")
    
    def _extract_experiment_number(self, source_data: Dict[str, Any]) -> Optional[str]:
        """
        Extract experiment number from source data.
        
        Args:
            source_data: Raw ELN data
            
        Returns:
            Experiment number or None if not found
        """
        # Try multiple locations for experiment number
        locations = [
            ("experiment_overview", "experiment_number"),
            ("route_information", "project_code"),
            ("protocol_name",),
        ]
        
        for location in locations:
            value = source_data
            for key in location:
                value = value.get(key) if isinstance(value, dict) else None
                if not value:
                    break
            
            if value and isinstance(value, str):
                return value.strip()
        
        return None
    
    def _create_fallback_mapping(self, experiment_number: str, source_data: Dict[str, Any]) -> MappingResult:
        """
        Create a fallback mapping when primary mapping fails.
        
        Args:
            experiment_number: Experiment number
            source_data: Source data for context
            
        Returns:
            Fallback MappingResult
        """
        logger.warning(f"Creating fallback mapping for experiment: {experiment_number}")
        
        # Use default values or extract from source data
        route_info = source_data.get("route_information", {})
        
        return MappingResult(
            project_id=str(uuid.uuid4()),  # Generate fallback ID
            route_id=str(uuid.uuid4()),    # Generate fallback ID
            step_id=str(uuid.uuid4()),     # Generate fallback ID
            tenant_id="6cd50e19-6a16-45ac-9c4e-cb8c908eb72e",  # Default tenant
            experiment_number=experiment_number,
            step_number=route_info.get("step_name", "1"),
            project_name=route_info.get("project_code", "Unknown"),
            source="fallback",
            confidence=0.1
        )
    
    def _transform_payload(self, source_data: Dict[str, Any], mapping_result: MappingResult) -> Dict[str, Any]:
        """
        Transform the payload using mapping information.
        
        Args:
            source_data: Raw ELN data
            mapping_result: Mapping information
            
        Returns:
            Transformed payload
        """
        # Create mapping dict for compatibility with existing transform function
        mapping_dict = {
            mapping_result.experiment_number or "default": {
                "project_id": mapping_result.project_id,
                "route_id": mapping_result.route_id,
                "step_id": mapping_result.step_id,
                "tenant_id": mapping_result.tenant_id,
                "step_number": mapping_result.step_number,
                "project_name": mapping_result.project_name,
                "experiment_number": mapping_result.experiment_number
            }
        }
        
        # Use the existing transformation function
        transformed = transform_payload_fixed(source_data, mapping_dict)
        
        # Override with mapping result values
        transformed.update({
            "project_id": mapping_result.project_id,
            "route_id": mapping_result.route_id,
            "step_id": mapping_result.step_id,
            "tenant_id": mapping_result.tenant_id,
        })
        
        # Add transformation metadata
        transformed["_transformation_metadata"] = {
            "mapping_source": mapping_result.source,
            "mapping_confidence": mapping_result.confidence,
            "transformed_at": datetime.now().isoformat(),
            "transformer_version": "2.0"
        }
        
        return transformed
    
    def _validate_payload(self, payload: Dict[str, Any]) -> None:
        """
        Validate the transformed payload.
        
        Args:
            payload: Transformed payload to validate
            
        Raises:
            DataTransformationError: If validation fails
        """
        required_fields = [
            "id", "tenant_id", "project_id", "route_id", "step_id",
            "experiment_number", "experiment_date", "title"
        ]
        
        missing_fields = []
        for field in required_fields:
            if not payload.get(field):
                missing_fields.append(field)
        
        if missing_fields:
            raise DataTransformationError(f"Missing required fields: {missing_fields}")
        
        # Validate data types
        if not isinstance(payload.get("inputs"), list):
            raise DataTransformationError("inputs must be a list")
        
        if not isinstance(payload.get("observations"), list):
            raise DataTransformationError("observations must be a list")
        
        # Validate UUIDs
        uuid_fields = ["id", "tenant_id", "project_id", "route_id", "step_id"]
        for field in uuid_fields:
            value = payload.get(field)
            if value:
                try:
                    uuid.UUID(str(value))
                except ValueError:
                    logger.warning(f"Invalid UUID format for field {field}: {value}")
    
    def transform_batch(self, source_data_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Transform multiple ELN data records.
        
        Args:
            source_data_list: List of raw ELN data records
            
        Returns:
            List of transformed payloads
        """
        results = []
        errors = []
        
        for i, source_data in enumerate(source_data_list):
            try:
                transformed = self.transform_eln_to_chemstack(source_data)
                results.append(transformed)
            except Exception as e:
                error_info = {
                    "index": i,
                    "error": str(e),
                    "experiment_number": self._extract_experiment_number(source_data)
                }
                errors.append(error_info)
                logger.error(f"Failed to transform record {i}: {e}")
        
        if errors:
            logger.warning(f"Batch transformation completed with {len(errors)} errors out of {len(source_data_list)} records")
        
        return results
    
    def get_transformation_summary(self, source_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get a summary of what would be transformed without actually transforming.
        
        Args:
            source_data: Raw ELN data
            
        Returns:
            Transformation summary
        """
        try:
            experiment_number = self._extract_experiment_number(source_data)
            mapping_result = self.mapper_service.get_mapping(experiment_number, source_data)
            
            summary = {
                "experiment_number": experiment_number,
                "mapping_found": self.mapper_service.validate_mapping(mapping_result),
                "mapping_source": mapping_result.source if mapping_result else "none",
                "mapping_confidence": mapping_result.confidence if mapping_result else 0.0,
                "project_id": mapping_result.project_id if mapping_result else None,
                "route_id": mapping_result.route_id if mapping_result else None,
                "step_id": mapping_result.step_id if mapping_result else None,
                "has_reactants": bool(source_data.get("reactant_table")),
                "has_observations": bool(source_data.get("procedure_and_observations")),
                "has_results": bool(source_data.get("result_summary")),
                "protocol_id": source_data.get("protocol_id")
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating transformation summary: {e}")
            return {"error": str(e)}
    
    def validate_source_data(self, source_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate source ELN data before transformation.
        
        Args:
            source_data: Raw ELN data to validate
            
        Returns:
            Validation result with issues and recommendations
        """
        issues = []
        warnings = []
        
        # Check for required sections
        required_sections = [
            "experiment_overview",
            "route_information",
            "reactant_table",
            "procedure_and_observations"
        ]
        
        for section in required_sections:
            if not source_data.get(section):
                issues.append(f"Missing required section: {section}")
        
        # Check experiment number
        experiment_number = self._extract_experiment_number(source_data)
        if not experiment_number:
            issues.append("Could not extract experiment number")
        
        # Check reactant table
        reactants = source_data.get("reactant_table", [])
        if not reactants:
            warnings.append("No reactants found")
        elif not any(r.get("reactant_name") for r in reactants):
            warnings.append("Reactant table has no valid reactant names")
        
        # Check observations
        observations = source_data.get("procedure_and_observations", [])
        if not observations:
            warnings.append("No procedure observations found")
        
        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "warnings": warnings,
            "experiment_number": experiment_number
        }
