"""
Test Database Connection

This script tests the database connection using your existing MongoDB configuration.
"""

import logging
import sys
import os

# Add paths for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, '/opt/airflow/dag_dependencies')
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_existing_mongo_operations():
    """Test using the existing mongo_operations.py"""
    print("=" * 60)
    print("TESTING EXISTING MONGO OPERATIONS")
    print("=" * 60)
    
    try:
        from db.mongo_operations import MongoOperations
        from config.db_config import DatabaseConfig
        
        # Get database config
        config = DatabaseConfig.get_chem_stack_mongo_params()
        print(f"Connection string: {config['connection_string'][:20]}...")
        print(f"Database name: {config['database_name']}")
        
        # Initialize mongo operations
        mongo_ops = MongoOperations(
            connection_string=config['connection_string'],
            database_name=config['database_name']
        )
        
        # Test basic operations
        print("\nTesting basic operations:")
        
        # Test reading from a collection (this should work even if collection is empty)
        try:
            test_data = mongo_ops.read_data("test_collection", limit=1)
            print(f"✓ Read operation successful (found {len(test_data)} records)")
        except Exception as e:
            print(f"✗ Read operation failed: {e}")
        
        # Test getting collections (this will show what collections exist)
        try:
            collections = mongo_ops.db.list_collection_names()
            print(f"✓ Available collections: {collections[:5]}...")  # Show first 5
        except Exception as e:
            print(f"✗ Could not list collections: {e}")
        
        print("\n✅ Existing mongo operations test completed")
        return True
        
    except Exception as e:
        print(f"❌ Existing mongo operations test failed: {e}")
        return False


def test_new_db_operations():
    """Test the new db_operations.py"""
    print("=" * 60)
    print("TESTING NEW DB OPERATIONS")
    print("=" * 60)
    
    try:
        from db_operations import DatabaseOperations
        
        # Initialize with automatic config detection
        db_ops = DatabaseOperations()
        
        print(f"Connection string: {db_ops.connection_string[:20]}...")
        print(f"Database name: {db_ops.database_name}")
        
        # Test health check
        health = db_ops.health_check()
        print(f"Health check: {'✓ PASSED' if health else '✗ FAILED'}")
        
        if health:
            # Test getting stats
            stats = db_ops.get_stats()
            print(f"Database stats: {stats}")
            
            # Test getting last sync date
            last_sync = db_ops.get_last_sync_date()
            print(f"Last sync date: {last_sync}")
            
            # Test getting run logs
            logs = db_ops.get_run_logs(3)
            print(f"Recent logs: {len(logs)} entries")
            
        db_ops.close_connection()
        print("\n✅ New db operations test completed")
        return True
        
    except Exception as e:
        print(f"❌ New db operations test failed: {e}")
        return False


def test_simple_eln_sync():
    """Test the simple ELN sync with database"""
    print("=" * 60)
    print("TESTING SIMPLE ELN SYNC WITH DATABASE")
    print("=" * 60)
    
    try:
        from simple_eln_sync import SimpleELNSync
        
        sync = SimpleELNSync()
        
        # Test getting sync status
        status = sync.get_sync_status()
        
        if "error" in status:
            print(f"⚠️  Sync status error: {status['error']}")
        else:
            print("Sync status:")
            print(f"  Last sync: {status.get('last_sync_date', 'Never')}")
            print(f"  Database health: {'✓' if status.get('database_health') else '✗'}")
            
            db_stats = status.get('database_stats', {})
            if db_stats:
                print("  Database collections:")
                for collection, stats in db_stats.items():
                    print(f"    {collection}: {stats.get('count', 0)} records")
        
        sync.cleanup()
        print("\n✅ Simple ELN sync test completed")
        return True
        
    except Exception as e:
        print(f"❌ Simple ELN sync test failed: {e}")
        return False


def main():
    """Run all database tests."""
    print("🔌 TESTING DATABASE CONNECTIONS")
    print("=" * 60)
    print()
    
    tests = [
        ("Existing Mongo Operations", test_existing_mongo_operations),
        ("New DB Operations", test_new_db_operations),
        ("Simple ELN Sync", test_simple_eln_sync)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
            print()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
            print()
    
    # Summary
    print("=" * 60)
    print("DATABASE TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name:<25} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All database tests passed! Your MongoDB connection is working.")
        print("\nYour simplified ELN system is ready to use with:")
        print("- Your existing MongoDB configuration")
        print("- Clean database operations")
        print("- Experiment number normalization")
        print("- Simple sync workflow")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Check your MongoDB connection.")
        print("\nTroubleshooting:")
        print("1. Verify MongoDB is running")
        print("2. Check connection string in Airflow variables")
        print("3. Verify database permissions")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
