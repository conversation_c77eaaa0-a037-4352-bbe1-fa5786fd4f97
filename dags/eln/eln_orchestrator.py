"""
ELN Orchestrator Module

This module provides the main orchestrator class that coordinates all services
and handles the complete ELN synchronization workflow. It integrates all the
components: database operations, ChemStack API client, data transformation,
mapping services, and experiment normalization.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple

from .db_operations import DatabaseOperations
from .chemstack_client import ChemStackClient
from .data_transformer import DataTransformationService
from .mapper_service import EnhancedMapperService
from .experiment_normalizer import ExperimentNumberNormalizer
from .eln_parser import ELNParser

logger = logging.getLogger(__name__)


class ELNSyncResult:
    """Data class to hold synchronization results."""
    
    def __init__(self):
        self.total_protocols = 0
        self.processed_protocols = 0
        self.successful_syncs = 0
        self.failed_syncs = 0
        self.skipped_protocols = 0
        self.errors = []
        self.start_time = datetime.now()
        self.end_time = None
        self.last_modified_date = None
    
    def add_error(self, protocol_id: str, error: str):
        """Add an error to the result."""
        self.errors.append({
            "protocol_id": protocol_id,
            "error": error,
            "timestamp": datetime.now()
        })
    
    def finish(self, last_modified_date: datetime = None):
        """Mark the sync as finished."""
        self.end_time = datetime.now()
        self.last_modified_date = last_modified_date or self.end_time
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of the sync results."""
        duration = (self.end_time - self.start_time).total_seconds() if self.end_time else 0
        
        return {
            "total_protocols": self.total_protocols,
            "processed_protocols": self.processed_protocols,
            "successful_syncs": self.successful_syncs,
            "failed_syncs": self.failed_syncs,
            "skipped_protocols": self.skipped_protocols,
            "error_count": len(self.errors),
            "success_rate": (self.successful_syncs / max(self.processed_protocols, 1)) * 100,
            "duration_seconds": duration,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "last_modified_date": self.last_modified_date.isoformat() if self.last_modified_date else None
        }


class ELNOrchestrator:
    """
    Main orchestrator for ELN data synchronization.
    
    This class coordinates all the services and handles the complete workflow:
    1. Fetches protocols from ELN API
    2. Parses and transforms data
    3. Maps experiment numbers to ChemStack IDs
    4. Syncs data to ChemStack
    5. Tracks synchronization state
    """
    
    def __init__(self, 
                 db_operations: DatabaseOperations = None,
                 chemstack_client: ChemStackClient = None,
                 data_transformer: DataTransformationService = None,
                 mapper_service: EnhancedMapperService = None,
                 normalizer: ExperimentNumberNormalizer = None,
                 eln_parser: ELNParser = None):
        """
        Initialize the orchestrator with all required services.
        
        Args:
            db_operations: Database operations service
            chemstack_client: ChemStack API client
            data_transformer: Data transformation service
            mapper_service: Enhanced mapper service
            normalizer: Experiment number normalizer
            eln_parser: ELN parser service
        """
        self.db_ops = db_operations or DatabaseOperations()
        self.chemstack_client = chemstack_client or ChemStackClient()
        self.mapper_service = mapper_service or EnhancedMapperService(self.chemstack_client)
        self.data_transformer = data_transformer or DataTransformationService(self.mapper_service)
        self.normalizer = normalizer or ExperimentNumberNormalizer()
        self.eln_parser = eln_parser or ELNParser()
    
    def run_full_sync(self, force_full_sync: bool = False) -> ELNSyncResult:
        """
        Run a complete ELN synchronization.
        
        Args:
            force_full_sync: If True, sync all data regardless of last sync date
            
        Returns:
            ELNSyncResult with sync statistics and results
        """
        result = ELNSyncResult()
        
        try:
            logger.info("Starting ELN synchronization")
            
            # Get last sync date
            if force_full_sync:
                last_sync_date = datetime.now() - timedelta(days=365)  # Full year
                logger.info("Force full sync requested")
            else:
                last_sync_date = self.db_ops.get_last_sync_date()
                logger.info(f"Last sync date: {last_sync_date}")
            
            # Fetch protocols from ELN
            protocols = self._fetch_protocols(last_sync_date)
            result.total_protocols = len(protocols)
            logger.info(f"Fetched {result.total_protocols} protocols from ELN")
            
            # Process each protocol
            last_modified_date = last_sync_date
            for protocol in protocols:
                try:
                    protocol_result = self._process_protocol(protocol)
                    result.processed_protocols += 1
                    
                    if protocol_result["success"]:
                        result.successful_syncs += 1
                    else:
                        result.failed_syncs += 1
                        result.add_error(protocol.get("protocolordercode"), protocol_result["error"])
                    
                    # Update last modified date
                    protocol_modified = protocol.get("modifieddate")
                    if protocol_modified and isinstance(protocol_modified, datetime):
                        last_modified_date = max(last_modified_date, protocol_modified)
                        
                except Exception as e:
                    result.failed_syncs += 1
                    result.add_error(protocol.get("protocolordercode"), str(e))
                    logger.error(f"Error processing protocol {protocol.get('protocolordercode')}: {e}")
            
            # Record sync completion
            result.finish(last_modified_date)
            error_summary = "; ".join([f"{e['protocol_id']}: {e['error']}" for e in result.errors[:5]])
            self.db_ops.insert_run_log(last_modified_date, error_summary if result.errors else "")
            
            logger.info(f"ELN synchronization completed: {result.get_summary()}")
            return result
            
        except Exception as e:
            logger.error(f"ELN synchronization failed: {e}")
            result.add_error("SYSTEM", str(e))
            result.finish()
            self.db_ops.insert_run_log(datetime.now(), str(e))
            return result
    
    def _fetch_protocols(self, last_sync_date: datetime) -> List[Dict[str, Any]]:
        """
        Fetch protocols from ELN API.
        
        Args:
            last_sync_date: Date to fetch protocols from
            
        Returns:
            List of protocol data
        """
        try:
            protocols_response = self.eln_parser.get_all_protocols(last_sync_date)
            return protocols_response.get("protocols", [])
        except Exception as e:
            logger.error(f"Error fetching protocols: {e}")
            return []
    
    def _process_protocol(self, protocol: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single protocol.
        
        Args:
            protocol: Protocol data from ELN
            
        Returns:
            Dict with processing result
        """
        protocol_id = protocol.get("protocolordercode")
        
        try:
            # Fetch detailed protocol data
            protocol_data = self.eln_parser.get_protocol_by_id(protocol_id)
            if not protocol_data:
                return {"success": False, "error": "Could not fetch protocol details"}
            
            # Parse the protocol
            parsed_data = self._parse_protocol(protocol_data)
            if not parsed_data:
                return {"success": False, "error": "Could not parse protocol data"}
            
            # Store in database
            parsed_data["protocol_id"] = protocol_id
            self.db_ops.upsert_eln_data(protocol_id, parsed_data)
            
            # Transform and sync to ChemStack
            chemstack_result = self._sync_to_chemstack(parsed_data)
            
            return {
                "success": chemstack_result["success"],
                "error": chemstack_result.get("error"),
                "reaction_id": chemstack_result.get("reaction_id")
            }
            
        except Exception as e:
            logger.error(f"Error processing protocol {protocol_id}: {e}")
            return {"success": False, "error": str(e)}
    
    def _parse_protocol(self, protocol_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Parse protocol data using ELN parser.
        
        Args:
            protocol_data: Raw protocol data
            
        Returns:
            Parsed data or None if parsing fails
        """
        try:
            self.eln_parser.raw = protocol_data
            self.eln_parser.structured = self.eln_parser._initialize_structure()
            self.eln_parser.content = self.eln_parser._parse_content()
            return self.eln_parser.parse()
        except Exception as e:
            logger.error(f"Error parsing protocol: {e}")
            return None
    
    def _sync_to_chemstack(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sync parsed data to ChemStack.
        
        Args:
            parsed_data: Parsed ELN data
            
        Returns:
            Dict with sync result
        """
        try:
            # Extract experiment number
            experiment_number = parsed_data.get("experiment_overview", {}).get("experiment_number")
            if not experiment_number:
                return {"success": False, "error": "No experiment number found"}
            
            # Check if this experiment is supported
            if not self.normalizer.is_supported_project(experiment_number):
                logger.info(f"Skipping unsupported experiment: {experiment_number}")
                return {"success": True, "skipped": True, "reason": "unsupported_project"}
            
            # Transform data
            transformed_payload = self.data_transformer.transform_eln_to_chemstack(parsed_data)
            
            # Get mapping information
            mapping_result = self.mapper_service.get_mapping(experiment_number, parsed_data)
            if not self.mapper_service.validate_mapping(mapping_result):
                return {"success": False, "error": "Could not resolve mapping for experiment"}
            
            # Check if reaction already exists
            existing_reaction_id = self.db_ops.get_reaction_id(experiment_number)
            
            # Create or update reaction
            if existing_reaction_id:
                # Update existing reaction
                result = self.chemstack_client.update_reaction(
                    mapping_result.project_id,
                    mapping_result.route_id,
                    mapping_result.step_id,
                    existing_reaction_id,
                    transformed_payload
                )
                operation = "updated"
            else:
                # Create new reaction
                result = self.chemstack_client.create_reaction(
                    mapping_result.project_id,
                    mapping_result.route_id,
                    mapping_result.step_id,
                    transformed_payload
                )
                operation = "created"
                
                # Store the new reaction ID
                if result and result.get("id"):
                    self.db_ops.upsert_reaction_experiment(experiment_number, result["id"])
            
            if result:
                logger.info(f"Successfully {operation} reaction for experiment: {experiment_number}")
                return {
                    "success": True,
                    "operation": operation,
                    "reaction_id": result.get("id"),
                    "experiment_number": experiment_number
                }
            else:
                return {"success": False, "error": f"Failed to {operation.rstrip('d')} reaction in ChemStack"}
                
        except Exception as e:
            logger.error(f"Error syncing to ChemStack: {e}")
            return {"success": False, "error": str(e)}
    
    def sync_single_protocol(self, protocol_id: str) -> Dict[str, Any]:
        """
        Sync a single protocol by ID.
        
        Args:
            protocol_id: Protocol ID to sync
            
        Returns:
            Dict with sync result
        """
        try:
            # Create a mock protocol object
            protocol = {"protocolordercode": protocol_id}
            result = self._process_protocol(protocol)
            
            logger.info(f"Single protocol sync completed for {protocol_id}: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Error syncing single protocol {protocol_id}: {e}")
            return {"success": False, "error": str(e)}
    
    def get_sync_status(self) -> Dict[str, Any]:
        """
        Get current synchronization status.
        
        Returns:
            Dict with sync status information
        """
        try:
            last_sync_date = self.db_ops.get_last_sync_date()
            recent_logs = self.db_ops.get_run_logs(5)
            db_stats = self.db_ops.get_stats()
            mapping_stats = self.mapper_service.get_mapping_stats()
            
            return {
                "last_sync_date": last_sync_date.isoformat(),
                "recent_logs": recent_logs,
                "database_stats": db_stats,
                "mapping_stats": mapping_stats,
                "supported_projects": list(self.normalizer.get_all_supported_projects().keys()),
                "chemstack_health": self.chemstack_client.health_check(),
                "database_health": self.db_ops.health_check()
            }
            
        except Exception as e:
            logger.error(f"Error getting sync status: {e}")
            return {"error": str(e)}
    
    def validate_experiment_batch(self, experiment_numbers: List[str]) -> Dict[str, Any]:
        """
        Validate a batch of experiment numbers.
        
        Args:
            experiment_numbers: List of experiment numbers to validate
            
        Returns:
            Dict with validation results
        """
        try:
            normalization_results = self.normalizer.batch_normalize(experiment_numbers)
            stats = self.normalizer.get_normalization_stats(experiment_numbers)
            
            return {
                "normalization_results": normalization_results,
                "statistics": stats,
                "supported_projects": list(self.normalizer.get_all_supported_projects().keys())
            }
            
        except Exception as e:
            logger.error(f"Error validating experiment batch: {e}")
            return {"error": str(e)}
    
    def cleanup_resources(self):
        """Clean up resources and close connections."""
        try:
            self.db_ops.close_connection()
            self.mapper_service.clear_cache()
            logger.info("Resources cleaned up successfully")
        except Exception as e:
            logger.error(f"Error cleaning up resources: {e}")
