"""
ELN DAG Runner and Test Utility

This script helps you test and run the ELN synchronization system
both as individual functions and as a complete DAG.
"""

import logging
import sys
import os
from datetime import datetime

# Add paths for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, '/opt/airflow/dag_dependencies')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_individual_functions():
    """Test individual DAG functions without Airflow context."""
    print("=" * 60)
    print("TESTING INDIVIDUAL DAG FUNCTIONS")
    print("=" * 60)
    
    # Mock context for testing
    mock_context = {
        'execution_date': datetime.now(),
        'dag_run': type('MockDagRun', (), {'external_trigger': False})()
    }
    
    try:
        # Import DAG functions
        sys.path.insert(0, '/opt/airflow/dags')
        from eln_sync_dag import check_sync_status, validate_experiments, run_eln_sync
        
        print("\n1. Testing sync status check...")
        try:
            status_result = check_sync_status(**mock_context)
            print(f"✓ Sync status check passed: {status_result.get('database_health', 'Unknown')}")
        except Exception as e:
            print(f"✗ Sync status check failed: {e}")
        
        print("\n2. Testing experiment validation...")
        try:
            validation_result = validate_experiments(**mock_context)
            valid_count = validation_result.get('valid_count', 0)
            total_count = validation_result.get('total_count', 0)
            print(f"✓ Experiment validation passed: {valid_count}/{total_count} valid")
        except Exception as e:
            print(f"✗ Experiment validation failed: {e}")
        
        print("\n3. Testing ELN sync (dry run)...")
        print("⚠️  Skipping actual sync to avoid data changes")
        print("   To test sync, uncomment the code below:")
        print("   # sync_result = run_eln_sync(**mock_context)")
        
        # Uncomment to test actual sync
        # try:
        #     sync_result = run_eln_sync(**mock_context)
        #     print(f"✓ ELN sync passed: {sync_result}")
        # except Exception as e:
        #     print(f"✗ ELN sync failed: {e}")
        
        print("\n✅ Individual function tests completed")
        
    except Exception as e:
        print(f"❌ Individual function tests failed: {e}")
        logger.exception("Individual function tests failed")


def test_simple_eln_sync():
    """Test the SimpleELNSync class directly."""
    print("=" * 60)
    print("TESTING SIMPLE ELN SYNC CLASS")
    print("=" * 60)
    
    try:
        from simple_eln_sync import SimpleELNSync
        
        sync = SimpleELNSync()
        
        try:
            print("1. Testing sync status...")
            status = sync.get_sync_status()
            if "error" in status:
                print(f"✗ Sync status failed: {status['error']}")
            else:
                print(f"✓ Sync status: Database health = {status.get('database_health')}")
            
            print("\n2. Testing experiment validation...")
            test_experiments = ["MS25012-010-R1-Step-1", "MS25018-013-R1-S1"]
            validation_results = sync.validate_experiments(test_experiments)
            
            for exp, result in validation_results.items():
                status_icon = "✓" if result['valid'] else "✗"
                print(f"   {status_icon} {exp} -> {result.get('normalized', 'FAILED')}")
            
            print("\n3. Testing single protocol sync (dry run)...")
            print("⚠️  Skipping actual protocol sync to avoid data changes")
            print("   To test protocol sync, uncomment the code below:")
            print("   # success = sync.sync_single_protocol('TEST_PROTOCOL_ID')")
            
            # Uncomment to test actual protocol sync
            # try:
            #     success = sync.sync_single_protocol('1000129')  # Replace with actual protocol ID
            #     print(f"✓ Single protocol sync: {success}")
            # except Exception as e:
            #     print(f"✗ Single protocol sync failed: {e}")
            
            print("\n✅ SimpleELNSync class tests completed")
            
        finally:
            sync.cleanup()
            
    except Exception as e:
        print(f"❌ SimpleELNSync class tests failed: {e}")
        logger.exception("SimpleELNSync class tests failed")


def show_dag_info():
    """Show information about the DAG."""
    print("=" * 60)
    print("ELN SYNC DAG INFORMATION")
    print("=" * 60)
    
    print("DAG ID: eln_sync_dag")
    print("Schedule: Daily at 2 AM (0 2 * * *)")
    print("Max Active Runs: 1")
    print("Catchup: False")
    print()
    print("Tasks:")
    print("1. check_sync_status - Verify database health and sync status")
    print("2. validate_experiments - Validate experiment number formats")
    print("3. run_eln_sync - Execute the main ELN synchronization")
    print()
    print("Task Dependencies:")
    print("check_sync_status >> validate_experiments >> run_eln_sync")
    print()
    print("Configuration:")
    print("- Retries: 2 with 15-minute delay")
    print("- Timeout: 2 hours")
    print("- Email notifications on failure")
    print()
    print("To run the DAG:")
    print("1. Copy eln_sync_dag.py to your Airflow dags folder")
    print("2. Ensure all ELN files are in dags/eln/ folder")
    print("3. Update email configuration in the DAG file")
    print("4. Trigger the DAG from Airflow UI or CLI")
    print()
    print("Airflow CLI commands:")
    print("  airflow dags list | grep eln_sync")
    print("  airflow dags trigger eln_sync_dag")
    print("  airflow dags state eln_sync_dag")


def run_manual_sync():
    """Run a manual ELN sync (use with caution)."""
    print("=" * 60)
    print("MANUAL ELN SYNC")
    print("=" * 60)
    
    response = input("⚠️  This will run actual ELN synchronization. Continue? (y/N): ")
    if response.lower() != 'y':
        print("Manual sync cancelled.")
        return
    
    try:
        from simple_eln_sync import SimpleELNSync
        
        sync = SimpleELNSync()
        
        try:
            print("Starting manual ELN synchronization...")
            
            # Ask for sync type
            force_full = input("Force full sync? (y/N): ").lower() == 'y'
            
            results = sync.sync_all_protocols(force_full_sync=force_full)
            
            print(f"\n📊 Sync Results:")
            print(f"   Total Protocols: {results['total_protocols']}")
            print(f"   Processed: {results['processed']}")
            print(f"   Successful: {results['successful']}")
            print(f"   Failed: {results['failed']}")
            
            if results['errors']:
                print(f"\n❌ Errors ({len(results['errors'])}):")
                for error in results['errors'][:5]:  # Show first 5 errors
                    print(f"   {error}")
                if len(results['errors']) > 5:
                    print(f"   ... and {len(results['errors']) - 5} more errors")
            
            if results['successful'] > 0:
                print(f"\n✅ Manual sync completed successfully!")
            else:
                print(f"\n⚠️  Manual sync completed with issues.")
            
        finally:
            sync.cleanup()
            
    except Exception as e:
        print(f"❌ Manual sync failed: {e}")
        logger.exception("Manual sync failed")


def main():
    """Main function to run tests and utilities."""
    print("🔄 ELN DAG RUNNER AND TEST UTILITY")
    print("=" * 60)
    
    options = {
        '1': ('Test Individual DAG Functions', test_individual_functions),
        '2': ('Test SimpleELNSync Class', test_simple_eln_sync),
        '3': ('Show DAG Information', show_dag_info),
        '4': ('Run Manual Sync (CAUTION)', run_manual_sync),
        '5': ('Run All Tests', lambda: [test_individual_functions(), test_simple_eln_sync()]),
        'q': ('Quit', lambda: None)
    }
    
    while True:
        print("\nSelect an option:")
        for key, (description, _) in options.items():
            print(f"  {key}. {description}")
        
        choice = input("\nEnter your choice: ").strip().lower()
        
        if choice == 'q':
            print("Goodbye!")
            break
        elif choice in options:
            print()
            try:
                options[choice][1]()
            except KeyboardInterrupt:
                print("\n\nOperation cancelled by user.")
            except Exception as e:
                print(f"\nOperation failed: {e}")
                logger.exception("Operation failed")
        else:
            print("Invalid choice. Please try again.")


if __name__ == "__main__":
    main()
