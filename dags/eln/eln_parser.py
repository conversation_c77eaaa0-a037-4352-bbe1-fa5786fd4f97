import sys
import json
import re
import requests
import logging
import difflib
from datetime import datetime
from typing import Dict, List, Optional, Any
from common.utils import AzureUtils
from eln.eln_constants import AZURE_BLOB_CONTAINER_NAME

sys.path.insert(0, '/opt/airflow/dag_dependencies')

from eln.eln_constants import (
    GET_ALL_PROTOCALS,
    GET_PROTOCOLS_PAYLOAD,
    GET_PROTOCOL_BY_ID,
    ELN_HEADERS,
    CHEMSTACK_GET_REACTION_BY_EXPERIMENT_NUMBER
)

logger = logging.getLogger(__name__)




class ELNParser:
    """
    ELN Parser class focused only on parsing ELN protocol data.
    Database operations are handled separately in db_operations.py
    """

    def __init__(self):
        """Initialize the parser."""
        self.raw = None
        self.structured = None
        self.content = None
        self.azure_utils = AzureUtils(AZURE_BLOB_CONTAINER_NAME)
        logger.info("ELNParser initialized")
        

    def _parse_content(self) -> Dict:
        """Parse the embedded content JSON"""
        content_raw = self.raw.get('protocols', {}).get("content")
        content = None

        # Try parsing if string
        if isinstance(content_raw, str):
            try:
                content = json.loads(content_raw)
            except Exception:
                pass
        elif isinstance(content_raw, dict):
            content = content_raw

        # Try alternative source if needed
        if not content:
            proto_data = self.raw.get("protocols", {}).get("protocoldatainfo")
            if proto_data:
                try:
                    content = json.loads(proto_data)
                except Exception:
                    pass

        if not content:
            raise ValueError("Could not find valid experimental content in JSON file")

        return content
    
    def parse_final_results(self, sheets):
        """Extract final results"""
        try:
            summary = {
                "target_material": None,
                "cas_number": None,
                "theoretical_yield_g": None,
                "actual_yield_g": None,
                "yield_percent": None,
                "parameters": {}
            }

            param_section = False
            rows = []

            # Flatten all rows
            for sheet in sheets:
                rows.extend(sheet.get("rows", []))

            i = 1
            while i < len(rows):
                cells = rows[i].get("cells", [])
                col1 = self.clean_value(cells[1].get("value")) if len(cells) > 1 else None
                col2 = self.clean_value(cells[2].get("value")) if len(cells) > 2 else None
                col3 = self.clean_value(cells[3].get("value")) if len(cells) > 3 else None

                if not col1:
                    i += 1
                    continue

                # Detect target material header row
                if "target material" in col1.lower() and "cas" in (col2 or "").lower():
                    # next row has actual values
                    if i + 1 < len(rows):
                        next_cells = rows[i+1].get("cells", [])
                        summary["target_material"] = self.clean_value(next_cells[1].get("value"))
                        summary["cas_number"] = self.clean_value(next_cells[2].get("value"))
                        i += 2
                        continue

                # Detect parameter header
                if col1.lower() == "parameter":
                    param_section = True
                    i += 1
                    continue

                if param_section:
                    if col1.lower() != "other results":
                        summary["parameters"][col1] = {
                            "target": col2,
                            "actual": col3
                        }
                    i += 1
                    continue

                # Normal metadata rows
                if "theoretical yield" in col1.lower():
                    summary["theoretical_yield_g"] = col2
                elif "actual yield" in col1.lower():
                    summary["actual_yield_g"] = col2
                elif "yield" in col1.lower():
                    summary["yield_percent"] = col2
                else:
                    # catch extras like Melting, Moisture, LoD
                    summary["parameters"][col1] = {
                        "target": col2,
                        "actual": col3
                    }

                i += 1
            return summary

        except Exception as e:
            return {}


    def _initialize_structure(self) -> Dict:
        """Initialize the structured output skeleton"""
        return {
            "protocol_name": self.raw.get("protocols", {}).get("protoclordername"),
            "experiment_overview": {
                "experiment_number": None,
                "title": None,
                "aim": None,
                "conclusion": None,
            },
            "personnel_and_timeline": None,
            "route_information": None,
            "reaction_scheme": None,
            "reactant_table": [],
            "procedure_and_observations": [],
            "result_summary": {
                "target_material": None,
                "cas_number": None,
                "theoretical_yield_g": None,
                "actual_yield_g": None,
                "yield_percent": None,
                "parameters": {}
            },
            "abstract": None
        }

    # --- Helper Methods ---
    @staticmethod
    def clean_value(val: Any) -> Optional[str]:
        """Remove placeholders and normalize"""
        if isinstance(val, dict) and "code" in val:
            return None
        if isinstance(val, str):
            v = val.strip()
            if v in ("NA", "N/A", ""):
                return None
            if v.startswith("[") and v.endswith("]"):  # [Manual Field], [Numeric Field]
                return None
            return v
        return val

    def parse_table(self, value: Dict, step: Dict) -> List[Dict]:
        """Extract spreadsheet-like tables"""
        try:
            rows = value["data"]["sheets"][0]["rows"]
            headers = [c.get("value", "") for c in rows[1]["cells"]]
            expected_headers_list = ['Reactnant name', "Molecular wt.", "Strength (%)", "Sample qty. (gm)", "Reactant qty. (gm)", "No. of moles", "Mol. eq./Rel. wt.", "Source", "Category"]
            if headers != expected_headers_list:
                # logger.error(f"Headers not as expected: {headers} != {expected_headers_list}")
                headers = expected_headers_list
            renamed_headers = {
                "Reactnant name": "reactant_name",
                "Molecular wt.": "molecular_weight",
                "Strength (%)": "strength_percent",
                "Sample qty. (gm)": "sample_qty_gm",
                "Reactant qty. (gm)": "reactant_qty_gm",
                "No. of moles": "no_of_moles",
                "Mol. eq./Rel. wt.": "mol_eq_rel_wt",
                "Source": "source",
                "Category": "category"
            }
            headers = [renamed_headers.get(h, h) for h in headers]
            entries = []
            for row in rows[0:]:
                cells = row.get("cells", [])
                # print(cells)
                entry = {}
                entry['eln_created_at'] = self.parse_date(step.get('createddate', ''))
                entry['created_by'] = step.get('createdby', '')
                eee = 0
                for i, cell in enumerate(cells):
                    if not cell.get("value"):
                        continue
                    if i < len(headers) and headers[eee]:
                        entry[headers[eee]] = self.clean_value(cell.get("value"))
                    eee += 1
                if any(v is not None for v in entry.values()):
                    entries.append(entry)
            cleaned_entries = []
            for entry in entries:
                if entry.get("reactant_name") and entry.get("reactant_name") != "Manual entry" and entry.get("reactant_name") != "Target product name" and entry.get("reactant_name") != "Reactnant name":
                    cleaned_entries.append(entry)

            return cleaned_entries
        except Exception:
            return []
    
    

    def fuzzy_match_header(self, header: str, expected_headers: List[str], cutoff: float = 0.6) -> str:
        """Return closest matching header from expected list, or original if none match."""
        if not header:
            return ""
        matches = difflib.get_close_matches(header.strip(), expected_headers, n=1, cutoff=cutoff)
        return matches[0] if matches else header.strip()

    def parse_precautions_and_observation(self, value: Dict, step: Dict) -> List[Dict]:
        """Extract spreadsheet-like tables"""
        try:
            column_mapping = {
                'column3': 'activity',
                'column2': 'date',
                'column11': 'start_time',
                'column12': 'end_time',
                'column4': 'temperature_in_c',
                'column8': 'observation',
                'column9': 'analytical_sample',
                'column10': 'analytical_result'
            }
            table_mapping_list = []
            for dt in value.get("data", []):
                table_mapping = {}
                for key, val in column_mapping.items():
                    table_mapping[val] = self.clean_value(dt.get(key, ''))
                
                # check all values are none
                if all(v is None for v in table_mapping.values()):
                    continue
                table_mapping_list.append(table_mapping)
            return table_mapping_list
        except Exception:
            return []

    def get_personnel_and_timeline(self, value: Dict, step: Dict) -> Optional[Dict]:
        """Extract personnel and timeline information"""
        try:
            dt = {}
            mapping_keys = [
                'experiment_led_by',
                'experiment_done_by',
                'dummy',
                'dummy2',
                'experiment_start_date',
                'experiment_end_date'
            ]
            for idx, val in enumerate(value.get('controldetails', [])):
                if idx < len(mapping_keys):
                    dt[mapping_keys[idx]] = self.clean_value(val.get("value"))
            
            dt['eln_created_at'] = self.parse_date(step.get('createddate', ''))
            dt['created_by'] = step.get('createdby', '')
            del dt['dummy']
            del dt['dummy2']

            dt['experiment_start_date'] = self.parse_date(dt.get('experiment_start_date', ''))
            dt['experiment_end_date'] = self.parse_date(dt.get('experiment_end_date', ''))
            return dt
        except Exception:
            return None

    def get_route_information(self, value: Dict, step: Dict) -> Optional[Dict]:
        """Extract route information"""
        try:
            dt = {}
            mapping_keys = ['project_code', 'route_number', 'step_name']
            for idx, val in enumerate(value.get('controldetails', [])):
                if idx < len(mapping_keys):
                    dt[mapping_keys[idx]] = self.clean_value(val.get("value"))
            dt['eln_created_at'] = self.parse_date(step.get('createddate', ''))
            dt['created_by'] = step.get('createdby', '')
            return dt
        except Exception:
            return None

    def get_reaction_schema(self, value: Dict, step: Dict) -> Optional[Dict]:
        """Extract reaction schema"""
        try:
            image_data = value.get("data", {}).get('mol', '{}')
            image_data = json.loads(image_data)
            child_items_data = image_data.get('root', {}).get('children', {}).get('items', [])
            if child_items_data:
                dt = {}
                dt['image'] = child_items_data[0].get('src', '')
                dt['eln_created_at'] = self.parse_date(step.get('createddate', ''))
                dt['created_by'] = step.get('createdby', '')
                return dt
        except Exception:
            return ''
    
    def get_pdf_data(self, url):
        """Download PDFs"""
        try:
            response = requests.get(url)
            response.raise_for_status()
            return response.content
        except Exception:
            return ''
        
    def get_pdfs_list(self, values):
        """Extract PDFs"""
        try:
            final_list = []
            for val in values:
                mapper = {}
                mapper['url'] = val.get('url')
                mapper['file_name'] = val.get('filename')
                mapper['pdf_path'] = self.azure_utils.upload_file(mapper['file_name'], self.get_pdf_data(mapper['url'])) + ".pdf"
                final_list.append(mapper)
            return final_list

        except Exception:
            return []
    
    def tlc_images(self, values) -> Optional[Dict]:
        """Extract TLC images"""
        try:
            final_list = []
            for val in values:
                mapper = {}
                mapper['image'] = val.get('url')
                mapper['eln_created_at'] = self.parse_date(val.get('createddate', ''))
                mapper['created_by'] = val.get('createdby', '')
                final_list.append(mapper)
            return final_list
        except Exception:
            return ''
        
    def parse_results(self) -> Dict:
        for sheet_data in self.content.get('result', {}).get('editors', []):
            if sheet_data.get('editordisplayname') == 'Labsheet':
                sheet_list = sheet_data.get('value', {}).get('data', {}).get('sheets', [])
            
                self.structured["result_summary"] = self.parse_final_results(sheet_list)
            elif sheet_data.get('editorname') == 'Analytical reports':
                pdfs_list = self.get_pdfs_list(sheet_data.get('value', []))
                self.structured["analytical_reports"] = pdfs_list
            
            elif sheet_data.get('editorname') == 'TLC images':
                tlc_image = self.tlc_images(sheet_data.get('value', []))
                self.structured["tlc_images"] = tlc_image
    
    def parse_date(self, text: str) -> datetime:
        """
        Parse various date formats into datetime objects.
        
        Args:
            text (str): Date string to parse
            
        Returns:
            datetime: Parsed datetime object, or original text if parsing fails
        """
        if not text:
            return text
            
        try:
            # Clean and standardize timezone format
            cleaned = text.replace("GMT", "").strip()
            
            cleaned = re.sub(
                r'([+-]\d{1,2}):(\d{2})',
                lambda m: f"{int(m.group(1)):02d}{m.group(2)}" if m.group(1).startswith('-')
                else f"+{int(m.group(1)):02d}{m.group(2)}",
                cleaned
            )
            
            # Try primary date format
            return datetime.strptime(cleaned, '%b-%d-%Y %H:%M:%S %z')
            
        except Exception:
            try:
                # Fallback to secondary date format
                return datetime.strptime(text, '%Y-%m-%dT%H:%M')
            except Exception:
                # Return original if parsing fails
                return text

    def parse(self) -> Dict:
        """Parse the ELN content and return structured data"""
        self.structured["abstract"] = self.content.get("abstract", {}).get('value', '')
        self.parse_results()
        # Traverse sections
        for section in self.content.get("sections", []):
            for step in section.get("steps", []):
                stepname = step.get("stepname", "").lower()
                for editor in step.get("editors", []):
                    ename = editor.get("editorname", "").lower()
                    value = editor.get("value", {})

                    # Experiment overview
                    if stepname == "experiment overview":
                        if "experiment number" in ename:
                            self.structured["experiment_overview"]["experiment_number"] = self.clean_value(value["controldetails"][0]["value"])
                        elif "title" in ename:
                            self.structured["experiment_overview"]["title"] = self.clean_value(value["controldetails"][0]["value"])
                        elif "aim" in ename:
                            self.structured["experiment_overview"]["aim"] = self.clean_value(value["controldetails"][0]["value"])
                        elif "conclusion" in ename:
                            self.structured["experiment_overview"]["conclusion"] = self.clean_value(value["controldetails"][0]["value"])

                        self.structured['eln_created_at'] = self.parse_date(step.get('createddate', ''))
                        self.structured['created_by'] = step.get('createdby', '')
                    # Personnel
                    elif "personnel" in stepname:
                        self.structured["personnel_and_timeline"] = self.get_personnel_and_timeline(value, step)

                    # Route
                    elif "route" in stepname:
                        self.structured["route_information"] = self.get_route_information(value, step)

                    # Reaction scheme
                    elif "reaction scheme" in stepname:
                        self.structured["reaction_scheme"] = self.get_reaction_schema(value, step)

                    # Reactant table
                    elif "reactant table" in stepname:
                        self.structured["reactant_table"] = self.parse_table(value, step)

                    # Procedure & Observations
                    elif "procedure" in stepname and "observation" in stepname:
                        self.structured["procedure_and_observations"] = self.parse_precautions_and_observation(value, step)

        return self.structured

    def __str__(self) -> str:
        """Return a string representation of the parsed data"""
        return json.dumps(self.structured, indent=2)

    def get_all_protocols(self, last_run_date : datetime):
        """Get all protocols"""
        from_date = last_run_date.strftime('%Y-%m-%d')
        to_date = datetime.now().strftime('%Y-%m-%d')
        # GET_PROTOCOLS_PAYLOAD.update({"fromdate": from_date, "todate": to_date})
        response = requests.post(GET_ALL_PROTOCALS, data=json.dumps(GET_PROTOCOLS_PAYLOAD), headers=ELN_HEADERS)
        response.raise_for_status()
        return response.json()
    
    def get_protocol_by_id(self, protocol_id):
        """Get protocol by id"""
        response = requests.get(GET_PROTOCOL_BY_ID.format(protocol_id=protocol_id), headers=ELN_HEADERS)
        response.raise_for_status()
        return response.json()
