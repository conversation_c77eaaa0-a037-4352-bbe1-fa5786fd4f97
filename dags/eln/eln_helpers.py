"""
ELN Helpers Module

Simple helpers for ELN data transformation and processing.
Contains utility functions for date/time processing, data transformation,
payload generation, and observation mapping.
"""

import uuid
import json
import re
from datetime import datetime, timezone

def as_iso(dt_str):
    """
    Normalize a datetime string (or None) to ISO 8601.
    Leaves 'Z' if present; otherwise returns as-is.
    Accepts strings like '2025-08-19T09:29:00.000Z' or '2025-08-19T09:29:00Z'.
    """
    if not dt_str:
        return None
    # already ISO enough; just normalize milliseconds to 3 digits if present
    return dt_str

def extract_date(obj_or_str):
    """Return $date string if dict like {'$date': '...'}, else return str, else None."""
    if obj_or_str is None:
        return None
    if isinstance(obj_or_str, dict) and "$date" in obj_or_str:
        return as_iso(obj_or_str["$date"])
    if isinstance(obj_or_str, str):
        return as_iso(obj_or_str)
    return None

def combine_date_and_time(date_iso, time_str):
    """
    Combine an ISO date string (YYYY-MM-DDTHH:MM:SSZ/...) and a 'HH:MM' time string into a new ISO in UTC.
    If date or time missing, return whichever is present (normalized).
    """
    try:
        if date_iso and time_str:
            # take date part up to 'T', then append time and 'Z' if original had Z
            z = 'Z' if date_iso.endswith('Z') else ''
            date_part = date_iso.split('T')[0]
            # ensure seconds present
            hh, mm = time_str.split(':')
            return f"{date_part}T{hh}:{mm}:00{z}"
        return date_iso or None
    except Exception:
        return None

def parse_temperature_value(s):
    """
    Convert a temperature_in_c field into value + optional qualifier ('<' or '>')
    Returns dict like {"value": 10.0, "unit": "°C"} and optionally adds "qualifier": "<"
    """
    if s is None:
        return None
    if isinstance(s, (int, float)):
        return {"value": float(s), "unit": "°C"}
    if isinstance(s, str):
        s = s.strip()
        m = re.match(r'^\s*([<>])\s*(\d+(\.\d+)?)\s*$', s)
        if m:
            q, val = m.group(1), float(m.group(2))
            return {"value": val, "unit": "°C", "qualifier": q}
        # ranges like '2-3°C' or '2-3'
        m2 = re.match(r'^\s*(\d+(\.\d+)?)\s*-\s*(\d+(\.\d+)?)', s)
        if m2:
            lo, hi = float(m2.group(1)), float(m2.group(3))
            return {"value": (lo+hi)/2.0, "unit": "°C", "range": [lo, hi]}
        # plain number string
        m3 = re.match(r'^\s*(\d+(\.\d+)?)', s)
        if m3:
            return {"value": float(m3.group(1)), "unit": "°C"}
    return None

def convert_to_utc(date_str):
    """
    Convert a datetime IST TIME string to UTC.
    """
    if not date_str:
        return None
    try:
        dt = datetime.fromisoformat(date_str.replace('Z', '+05:30'))
        return dt.astimezone(timezone.utc).isoformat()
    except Exception:
        return date_str
    

def build_observation_item(step, current_date_iso):
    """
    Map one source step to a target observation item.
    Uses current_date_iso when step['date'] is None and start_time exists.
    Returns (obs_dict, updated_current_date_iso).
    """
    date_iso = extract_date(step.get("date")) or current_date_iso
    started_at = combine_date_and_time(date_iso, step.get("start_time"))
    ended_at = combine_date_and_time(date_iso, step.get("end_time"))

    notes_parts = []
    if step.get("observation"):
        notes_parts.append(step["observation"])
    if step.get("analytical_sample"):
        notes_parts.append(f"Analytical sample: {step['analytical_sample']}")
    if step.get("analytical_result"):
        notes_parts.append(f"Analytical result: {step['analytical_result']}")
    notes = " | ".join(notes_parts) if notes_parts else None

    temp_block = parse_temperature_value(step.get("temperature_in_c"))
    obs = {
        "procedure": step.get("activity"),
        "temperature_intermediate": temp_block,
        "temperature_jac": None,
        "sampling_details": {
            "tests": [],
            "observation": step.get("observation"),
            "quantity": None,
            "concentration": None,
            "comments": None
        },
        "started_at": convert_to_utc(started_at),
        "ended_at": convert_to_utc(ended_at),
        "notes": notes
    }
    # If analytical_result hints a technique (e.g., LC-MS/HPLC), add a test stub
    ar = (step.get("analytical_result") or "") + " " + (step.get("observation") or "")
    tech = None
    if re.search(r'\bLC[-\s]?MS\b', ar, re.IGNORECASE):
        tech = "LC-MS"
    elif re.search(r'\bHPLC\b', ar, re.IGNORECASE):
        tech = "HPLC"
    elif re.search(r'\bGC[-\s]?MS\b', ar, re.IGNORECASE):
        tech = "GC-MS"
    if tech:
        obs["sampling_details"]["tests"].append({
            "name": tech,
            "supporting_files": [],
            "comments": step.get("analytical_result") or ""
        })

    # Update rolling date if this step provided an explicit date
    explicit_date = extract_date(step.get("date"))
    if explicit_date:
        current_date_iso = explicit_date
    return obs, current_date_iso

def map_analytical_reports_to_observation(reports):
    """Wrap analytical_reports files into one observation with tests/supporting_files."""
    if not reports:
        return None
    tests = []
    files = []
    for r in reports:
        url = r.get("url")
        name = r.get("file_name")
        mime = "application/pdf" if (url and url.lower().endswith(".pdf")) else None
        files.append({
            "id": None,
            "created_at": None,
            "updated_at": None,
            "name": name,
            "url": str(url),
            "path": '/'.join(url.split("/")[3:]),  # best-effort; no separate path provided
            "type": mime or "application/octet-stream",
            "size": None,
            "uploaded_at": None,
            "uploaded_by": None,
            "mime_type": mime,
            "is_public": False
        })
    tests.append({
        "name": "Analytical Report",
        "supporting_files": files,
        "comments": ""
    })
    return {
        "procedure": "Analytical reports attached",
        "temperature_intermediate": None,
        "temperature_jac": None,
        "sampling_details": {
            "tests": tests,
            "observation": None,
            "quantity": None,
            "concentration": None,
            "comments": None
        },
        "started_at": None,
        "ended_at": None,
        "notes": None
    }

def map_reactants_to_inputs(reactant_table):
    inputs = []

    # find limiting reagent (smallest mol_eq_rel_wt > 0)
    valid_eq = [r.get("mol_eq_rel_wt") for r in reactant_table if r.get("mol_eq_rel_wt")]
    min_eq = min(valid_eq) if valid_eq else None

    for reactant in reactant_table:
        component = {
            "name": reactant.get("reactant_name", ""),
            "role": "reactant" if reactant.get("molecular_weight") else "solvent",
            "cas_number": "",  # if you have a lookup table, fill this
            "molecular_weight": reactant.get("molecular_weight", 0),
            "smiles": ""  # fill if you have SMILES data
        }

        quantity = {
            "value": reactant.get("reactant_qty_gm", 0) or 0,
            "unit": "g"
        }

        sample_quantity = {
            "value": reactant.get("sample_qty_gm", 0) or 0,
            "unit": "g"
        }

        mapped = {
            "component": component,
            "quantity": quantity,
            "sample_quantity": sample_quantity,
            "strength": reactant.get("strength_percent", 0) or 0,
            "moles": reactant.get("no_of_moles", 0) or 0,
            "mole_equivalence": reactant.get("mol_eq_rel_wt", 0) or 0,
            "active": True,
            "is_limiting": (reactant.get("mol_eq_rel_wt") == min_eq if min_eq else False)
        }

        inputs.append(mapped)

    return inputs


def best_mapping_for(source, mapping):
    """
    Try to resolve a mapping key based on source route/project/step fields.
    Tries a few reasonable variants; returns mapping dict or {}.
    """
    proj = (source.get("route_information", {}).get("project_code") or "").strip()
    route_num = (source.get("route_information", {}).get("route_number") or "").strip()
    step_name = (source.get("route_information", {}).get("step_name") or "").strip()
    candidates = []
    if proj and step_name:
        candidates.append(f"{proj}-Step-{step_name}")
        candidates.append(f"{proj}-S{step_name}")
        candidates.append(f"{proj} Step {step_name}")
    # direct project_code alone
    if proj:
        candidates.append(proj)
    # route variants
    if proj and route_num:
        candidates.append(f"{proj}-R{route_num}")
    # try all case-insensitive matches
    lower_map = {k.lower(): v for k, v in mapping.items()}
    for c in candidates:
        if c.lower() in lower_map:
            return lower_map[c.lower()]
    return {}

def to_identifiers(source):
    ids = []
    if "protocol_id" in source:
        ids.append({"type": "protocol_id", "value": source["protocol_id"]})
    if "protocol_name" in source:
        ids.append({"type": "protocol_name", "value": source["protocol_name"]})
    return ids

def get_tlc_images(source):
    tlc_images = []
    for image in source.get("tlc_images", []):
        tlc_images.append({
            "id": None,
            "created_at": convert_to_utc(image.get("eln_created_at")),
            "updated_at": convert_to_utc(image.get("eln_created_at")),
            "name": "test-image.jpg",
            "url": image.get("image"),
            "path": image.get("image"),
            "type": "experimentation",
            "size": 114563,
            "uploaded_at": convert_to_utc(image.get("eln_created_at")),
            "uploaded_by": image.get("created_by"),
            "mime_type": "image/jpeg",
            "is_public": False
        })
    return tlc_images

def build_procedure_text(steps):
    """Create a human-readable procedure timeline."""
    lines = []
    for s in steps:
        t = s.get("start_time") or ""
        d = extract_date(s.get("date")) or ""
        act = s.get("activity") or ""
        obs = s.get("observation") or ""
        temp = s.get("temperature_in_c")
        temp_str = f" @ {temp}°C" if isinstance(temp, (int, float)) else (f" @ {temp}°C" if isinstance(temp, str) and temp else "")
        bits = [b for b in [d, t, act] if b]
        line = " — ".join(bits)
        if obs or temp:
            extras = []
            if temp:
                extras.append(f"Temp: {temp}")
            if obs:
                extras.append(f"Obs: {obs}")
            line += f" ({'; '.join(extras)})"
        lines.append(line)
    return "\n".join([ln for ln in lines if ln])

# ---------- Core transform ----------

def transform_payload(source: dict, mapping: dict) -> dict:
    exp_overview = source.get("experiment_overview", {})
    personnel = source.get("personnel_and_timeline", {})
    route_info = source.get("route_information", {})
    reaction_scheme = source.get("reaction_scheme", {})

    map_values = best_mapping_for(source, mapping)
    map_values = mapping.get("MS25018-013-R1-S1 --")

    created_at = extract_date(source.get("created_at"))
    updated_at = extract_date(source.get("updated_at"))
    started_at = extract_date(personnel.get("experiment_start_date"))
    ended_at = extract_date(personnel.get("experiment_end_date"))

    # Build observations
    observations = []
    steps = source.get("procedure_and_observations", []) or []
    rolling_date = (extract_date(steps[0].get("date")) if steps else None) or started_at
    for step in steps:
        obs_item, rolling_date = build_observation_item(step, rolling_date)
        observations.append(obs_item)

    reports_obs = map_analytical_reports_to_observation(source.get("analytical_reports"))
    if reports_obs:
        observations.append(reports_obs)

    # Reaction image meta (best-effort from data URI)
    img_mime = "image/jpeg" if str(reaction_scheme.get("image", "")).startswith("data:image/jpeg") else None

    payload = {
        "id": str(uuid.uuid4()),
        "created_at": created_at,
        "updated_at": updated_at,
        "tenant_id": map_values.get("tenant_id"),
        "step_id": map_values.get("step_id"),
        "project_id": map_values.get("project_id"),
        "route_id": map_values.get("route_id"),
        "experiment_number": map_values.get("step_number") or exp_overview.get("experiment_number") or route_info.get("project_code"),
        "experiment_date": started_at,
        "experiment_type": "Validation",
        "started_at": started_at,
        "ended_at": ended_at,
        "title": exp_overview.get("title"),
        "aim": exp_overview.get("aim"),
        "identifiers": to_identifiers(source),
        "inputs": map_reactants_to_inputs(source.get("reactant_table", [])),
        "conditions": {
            "temperature": None,
            "concentration": {"unit": "M", "value": None},
            "pressure": None,
            "time": {"unit": "hour", "value": None},
            "stirring": {"type": ""}
        },
        "setup": None,
        "procedure": build_procedure_text(steps),
        "lab_requirements": None,
        "reaction_image": {
            "id": None,
            "created_at": extract_date(reaction_scheme.get("eln_created_at")),
            "updated_at": extract_date(reaction_scheme.get("eln_created_at")),
            "name": "reaction-scheme.jpg",
            "url": None,
            "path": None,
            "type": "experimentation",
            "size": None,
            "uploaded_at": extract_date(reaction_scheme.get("eln_created_at")),
            "uploaded_by": reaction_scheme.get("created_by"),
            "mime_type": img_mime,
            "is_public": False
        },
        "lab_instructions": "",
        "safety_notes": "",
        "self_declaration": {
            "given_by": personnel.get("created_by"),
            "given_at": extract_date(source.get("eln_created_at"))
        },
        "products": [],  # result_summary lacks target material; keeping empty
        "observations": observations,
        "led_by": personnel.get("experiment_led_by"),
        "performed_by": personnel.get("experiment_done_by"),
        "status": "pending_approval",
        "yield_value": source.get("result_summary", {}).get("yield_percent") if source.get("result_summary", {}).get("yield_percent")  < 100 else 50,
        "purity": source.get("result_summary", {}).get("parameters", {}).get("Purity", {}).get("actual"),
        "summary": source.get("abstract", ""),
        "conclusion": exp_overview.get("conclusion", ""),
        "tlc_images": get_tlc_images(source),
        "active": True,
        "synced_to_ml_model": False,
        "created_by": source.get("created_by"),
        "updated_by": source.get("created_by"),
        "revision": 1,
        "route_number": route_info.get("route_number")
    }
    return payload


def safe_uuid(val=None):
    """Ensure we always return a UUID string instead of emails or None."""
    try:
        return str(uuid.UUID(str(val)))
    except Exception:
        return str(uuid.uuid4())

def transform_payload_fixed(source, mapping):
    payload = transform_payload(source, mapping)  # from earlier

    # identifiers must be string
    protocol_id = source.get("protocol_id")
    payload["identifiers"] = [{"type": "protocol_id", "value": str(protocol_id)}]

    # conditions: ensure numbers, not None
    if "conditions" not in payload:
        payload["conditions"] = {}
    payload["conditions"].setdefault("concentration", {"unit": "M", "value": 0.0})
    if payload["conditions"]["concentration"].get("value") is None:
        payload["conditions"]["concentration"]["value"] = 0.0

    payload["conditions"].setdefault("time", {"unit": "hour", "value": 0.0})
    if payload["conditions"]["time"].get("value") is None:
        payload["conditions"]["time"]["value"] = 0.0

    # reaction_image.uploaded_by → UUID
    if "reaction_image" in payload:
        payload["reaction_image"]["uploaded_by"] = safe_uuid(source.get("created_by"))

    # Observations → fix supporting_files timestamps & uploaded_by
    for obs in payload.get("observations", []):
        if "sampling_details" in obs:
            for test in obs["sampling_details"].get("tests", []):
                for f in test.get("supporting_files", []):
                    f["created_at"] = source["created_at"]["$date"]
                    f["updated_at"] = source["updated_at"]["$date"]
                    f["uploaded_at"] = source["created_at"]["$date"]
                    f["uploaded_by"] = safe_uuid(source.get("created_by"))

    # led_by should be string, performed_by should be list
    led = source.get("personnel_and_timeline", {}).get("experiment_led_by")
    done_by = source.get("personnel_and_timeline", {}).get("experiment_done_by")
    payload["led_by"] = led or ""
    payload["performed_by"] = [done_by] if done_by else []

    # created_by & updated_by → UUID
    payload["created_by"] = safe_uuid(source.get("created_by"))
    payload["updated_by"] = safe_uuid(source.get("created_by"))

    return payload


def hit_api(payload):
    import requests
    import json

    url = "https://api-stg.chemstack.ai/chemstack/api/v1/projects/3f452a1f-65b7-4928-8aac-da2c5780e7e8/routes/9312bdc3-91ff-4ada-ac68-a488bf81138e/steps/7b7c79f5-92eb-4265-8dba-c35f79f8ba34/reactions"

    headers = {
    'accept': 'application/json',
    'accept-language': 'en-US,en;q=0.9',
    'authorization': 'Bearer ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',  'content-type': 'application/json',
    'origin': 'https://stg.chemstack.ai',
    'priority': 'u=1, i',
    'referer': 'https://stg.chemstack.ai/',
    'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
    'x-tenant-id': '6cd50e19-6a16-45ac-9c4e-cb8c908eb72e',
    'x-user-id': '09b3ec46-0c97-46b0-9488-7af04e1d27f2'
    }

    response = requests.request("POST", url, headers=headers, data=payload)

    print(response.text)

# ---------- Example usage ----------
if __name__ == "__main__":
    # Paste your ELN JSON into source_data (truncated reaction image is fine)
    source_data = {
  "_id": {
    "$oid": "68be7cbada2c5ca36f0de4f1"
  },
  "protocol_id": 1000129,
  "protocol_name": "ELN1000129",
  "experiment_overview": {
    "experiment_number": "MS25018-013-R1-S1",
    "title": "To synthesis of BZOX",
    "aim": "To check the color and yield of 200.0g scale by reduced Acetonitrile - 2.7w/w  and filtered at 0-5°C .",
    "conclusion": "The process was feasible, 93.24% purity wet solid taken for next stage-2.\nThe recovery of pyridine have 8.75% of M.C in cut-1&2. Then combined to next stage-2 Crude (MS25018-013-R1-S2)  A/F ethyl acetate distillation for recovery of pyridine"
  },
  "personnel_and_timeline": {
    "experiment_led_by": "Shrikant",
    "experiment_done_by": "Mithun",
    "experiment_start_date": {
      "$date": "2025-07-25T13:40:00.000Z"
    },
    "experiment_end_date": {
      "$date": "2025-08-11T14:05:00.000Z"
    },
    "eln_created_at": {
      "$date": "2025-06-30T09:53:19.000Z"
    },
    "created_by": "<EMAIL>"
  },
  "route_information": {
    "project_code": "MS25018-013",
    "route_number": "1",
    "step_name": "1",
    "eln_created_at": {
      "$date": "2025-06-30T10:01:39.000Z"
    },
    "created_by": "<EMAIL>"
  },
  "reaction_scheme": {
    "image": "",
    "eln_created_at": {
      "$date": "2025-06-30T10:06:52.000Z"
    },
    "created_by": "<EMAIL>"
  },
  "reactant_table": [
    {
      "eln_created_at": {
        "$date": "2025-06-30T10:08:18.000Z"
      },
      "created_by": "<EMAIL>",
      "reactant_name": "K-acid",
      "molecular_weight": 302.51,
      "strength_percent": 98.967,
      "sample_qty_gm": 202.08756454172,
      "reactant_qty_gm": 200,
      "no_of_moles": 0.66113516908532,
      "mol_eq_rel_wt": 1,
      "source": "China HS-3912900000"
    },
    {
      "eln_created_at": {
        "$date": "2025-06-30T10:08:18.000Z"
      },
      "created_by": "<EMAIL>",
      "reactant_name": "ACMBA",
      "molecular_weight": 185.61,
      "strength_percent": 99.2,
      "sample_qty_gm": 129.88806821635,
      "reactant_qty_gm": 128.84896367062,
      "no_of_moles": 0.69419192753959,
      "mol_eq_rel_wt": 1.05,
      "source": "MS-A1-0525-006A"
    },
    {
      "eln_created_at": {
        "$date": "2025-06-30T10:08:18.000Z"
      },
      "created_by": "<EMAIL>",
      "reactant_name": "Pyridine",
      "molecular_weight": 79.1,
      "strength_percent": 99,
      "sample_qty_gm": 242.99054810443,
      "reactant_qty_gm": 240.56064262339,
      "no_of_moles": 3.0412217777925,
      "mol_eq_rel_wt": 4.6,
      "source": "Hyma"
    },
    {
      "eln_created_at": {
        "$date": "2025-06-30T10:08:18.000Z"
      },
      "created_by": "<EMAIL>",
      "reactant_name": "Mesyl chloride",
      "molecular_weight": 114.56,
      "strength_percent": 98,
      "sample_qty_gm": 177.75630962444,
      "reactant_qty_gm": 174.20118343195,
      "no_of_moles": 1.5206108888962,
      "mol_eq_rel_wt": 2.3,
      "source": "Hyma"
    },
    {
      "eln_created_at": {
        "$date": "2025-06-30T10:08:18.000Z"
      },
      "created_by": "<EMAIL>",
      "reactant_name": "Acetonitrile",
      "molecular_weight": None,
      "strength_percent": 100,
      "sample_qty_gm": 540,
      "reactant_qty_gm": None,
      "no_of_moles": None,
      "mol_eq_rel_wt": 2.7,
      "source": "Hyma"
    },
    {
      "eln_created_at": {
        "$date": "2025-06-30T10:08:18.000Z"
      },
      "created_by": "<EMAIL>",
      "reactant_name": "BZOX",
      "molecular_weight": 452.09,
      "strength_percent": 0.66113516908532,
      "sample_qty_gm": 298.89259859178
    }
  ],
  "procedure_and_observations": [
    {
      "activity": "Arranged neat and clean dry 500ml RBF at 25-30°C",
      "date": "2025-07-24T18:30:00.000Z",
      "start_time": "08:40",
      "end_time": None,
      "temperature_in_c": "27",
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "Charged Acetonitrile at 25-30°C",
      "date": None,
      "start_time": "08:45",
      "end_time": None,
      "temperature_in_c": "25",
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "Charged Br. Acid and ACMBA at 25-30°C",
      "date": None,
      "start_time": "08:50",
      "end_time": None,
      "temperature_in_c": "25",
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "Slowly charged Pyridine at 25-30°C",
      "date": None,
      "start_time": "08:55",
      "end_time": None,
      "temperature_in_c": "25",
      "observation": "RPM=400",
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "Raised temp. at 45-50°C",
      "date": None,
      "start_time": "09:00",
      "end_time": "09:20",
      "temperature_in_c": "45",
      "observation": "Clear solution observed",
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "Maintained for 30min",
      "date": None,
      "start_time": "09:25",
      "end_time": "09:55",
      "temperature_in_c": None,
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "Rm coolled to 2 to -2°C",
      "date": None,
      "start_time": "10:00",
      "end_time": "10:20",
      "temperature_in_c": "-2 to 2",
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "Slowly Added Mesyl chloride at 2 to -2°C during a period of 3hr",
      "date": None,
      "start_time": "10:25",
      "end_time": None,
      "temperature_in_c": "-2 to 2",
      "observation": "Temp. is critical",
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "Addition completed",
      "date": None,
      "start_time": None,
      "end_time": "13:25",
      "temperature_in_c": "-2 to 2",
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "Maintained the Rxn mass for 4 hr at 2to -2°C",
      "date": None,
      "start_time": "13:30",
      "end_time": None,
      "temperature_in_c": "-2 to 2",
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "Maintainance completed",
      "date": None,
      "start_time": None,
      "end_time": "17:30",
      "temperature_in_c": "-2 to 2",
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "Send sample to AR&D A/F 4hr",
      "date": None,
      "start_time": "17:30",
      "end_time": None,
      "temperature_in_c": "-2 to 2",
      "observation": None,
      "analytical_sample": "MS25018-013-R1-S1 IP-01 A/F 4hr",
      "analytical_result": "K-acid=0.6 ACMBA=1.02 RRT-0.735=1.08 RRT-0.785=1.26 RRT-0.838=0.9 stage-1=92.02"
    },
    {
      "activity": "Rxn mass cooled to 15°C",
      "date": None,
      "start_time": "17:35",
      "end_time": "17:55",
      "temperature_in_c": "15",
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "Maintained the Rxn mass for 1.5 hr",
      "date": None,
      "start_time": "18:00",
      "end_time": "19:00",
      "temperature_in_c": "15",
      "observation": None,
      "analytical_sample": "MS25018-013-R1-S1 IP-02 A/F 1.5hr at 15°C",
      "analytical_result": "K-acid=0.41 ACMBA=0.79 RRT-0.735=0.57 RRT-0.785=0.69 RRT-0.838=1.87 stage-1=92.45"
    },
    {
      "activity": "Inprocess sample submitted for analysis",
      "date": None,
      "start_time": "19:30",
      "end_time": None,
      "temperature_in_c": "15",
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "reaction mass cooled to 0-5 deg",
      "date": None,
      "start_time": "19:30",
      "end_time": "19:45",
      "temperature_in_c": "0-5",
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "Stirred for 30 mins at 0-5 deg",
      "date": None,
      "start_time": "19:45",
      "end_time": "20:15",
      "temperature_in_c": "0-5",
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "Filtered the solid and wash with small amount of same main MLR",
      "date": None,
      "start_time": "20:00",
      "end_time": "20:20",
      "temperature_in_c": "0-5",
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "Suck dry for for 1.5 hour",
      "date": None,
      "start_time": "20:30",
      "end_time": "21:00",
      "temperature_in_c": None,
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "Main MLR wt= 310.5 g",
      "date": None,
      "start_time": None,
      "end_time": None,
      "temperature_in_c": None,
      "observation": None,
      "analytical_sample": "MS25018-013-R1-S1 Main MLR",
      "analytical_result": "Pyridine=49.67 K-acid=ND ACMBA=4.84 RRT-0.735=ND RRT-0.785=17.24 RRT-0.838=4.34 stage-1=10.72 RRT-1.083=5.8 RRT-1.196=4.25"
    },
    {
      "activity": "Wet solid wt= 792.0g",
      "date": None,
      "start_time": None,
      "end_time": None,
      "temperature_in_c": None,
      "observation": None,
      "analytical_sample": "MS25018-013-R1-S1 wet wt solid",
      "analytical_result": "Pyridine=3.62 K-acid=0.12 ACMBA=0.59 RRT-0.735=ND RRT-0.785=0.03 RRT-0.838=0.5 stage-1=93..24 RRT-1.083=0.2 RRT-1.196=0.24"
    },
    {
      "activity": "Taken Above main MLR into RBF",
      "date": None,
      "start_time": None,
      "end_time": None,
      "temperature_in_c": None,
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "Distilled at 1 ATM pressure",
      "date": None,
      "start_time": None,
      "end_time": None,
      "temperature_in_c": None,
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "V.T",
      "date": None,
      "start_time": "Time",
      "end_time": "M.T",
      "temperature_in_c": "Fraction",
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "72",
      "date": None,
      "start_time": None,
      "end_time": "90",
      "temperature_in_c": "start",
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "74.5",
      "date": None,
      "start_time": None,
      "end_time": "90",
      "temperature_in_c": "Cut-1= 17.0g",
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "76",
      "date": None,
      "start_time": None,
      "end_time": "95",
      "temperature_in_c": None,
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "80",
      "date": None,
      "start_time": None,
      "end_time": "95",
      "temperature_in_c": None,
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "95",
      "date": None,
      "start_time": None,
      "end_time": "98",
      "temperature_in_c": "Cut-2 = 135.0g",
      "observation": None,
      "analytical_sample": "M.C = 1.15",
      "analytical_result": None
    },
    {
      "activity": None,
      "date": None,
      "start_time": None,
      "end_time": None,
      "temperature_in_c": "Residuev= 145.0g",
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "taken residue into RBF slowly added 48%NaOH solution upto pH 10-12",
      "date": None,
      "start_time": None,
      "end_time": None,
      "temperature_in_c": None,
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "pH obtained",
      "date": None,
      "start_time": None,
      "end_time": None,
      "temperature_in_c": None,
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "Total 48% NaOH consued = 65.0g",
      "date": None,
      "start_time": None,
      "end_time": None,
      "temperature_in_c": None,
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "ppt obtained",
      "date": None,
      "start_time": None,
      "end_time": None,
      "temperature_in_c": None,
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "filtered the solid",
      "date": None,
      "start_time": None,
      "end_time": None,
      "temperature_in_c": None,
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "Inorganic solid (salt) 52.5g",
      "date": None,
      "start_time": None,
      "end_time": None,
      "temperature_in_c": None,
      "observation": None,
      "analytical_sample": "MS25018-013-R1-S1 inorg.solid",
      "analytical_result": "Pyr. =ND RY-2.792=1.13 RT-3.085=61.9 RT-3.2=21.5 K-acid=1.26 RT-12.133=3.05 RT-12.836-4.74 RT-14.52=2.75"
    },
    {
      "activity": "seperated water layer = 60.5g",
      "date": None,
      "start_time": None,
      "end_time": None,
      "temperature_in_c": None,
      "observation": None,
      "analytical_sample": "MS25018-013-R1-S1 a/f NaOH water layer",
      "analytical_result": "Pyr.=26.7 RT-2.203=4.36 RT-3.146=33.33 RT-3.233=16.19 RT-9.793-0.98 K-Acid=1.68 RT-11.287=3.53 RT-11.631=1.32 ACMBA=ND RT-12.842=5.88 RT-14.526=1.64"
    },
    {
      "activity": "Pyridine layer = 93.5g",
      "date": None,
      "start_time": None,
      "end_time": None,
      "temperature_in_c": None,
      "observation": None,
      "analytical_sample": "MS25018-013-R1-S1 pyr. layer",
      "analytical_result": "Pyr.=35.16 RT-3.096=1.49 RT-8.199=3.11 RT-9.774=3.45 K-Acid=7.01 ACMBA=11.51 RT-12.845=17.59 RT-14.529=14.05"
    },
    {
      "activity": "Taken pyridine layer for downawrd distillation With vigreux column",
      "date": None,
      "start_time": None,
      "end_time": None,
      "temperature_in_c": None,
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "V.T",
      "date": None,
      "start_time": "Time",
      "end_time": "M.T",
      "temperature_in_c": "Pressure",
      "observation": "Fraction",
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "65",
      "date": None,
      "start_time": None,
      "end_time": "90",
      "temperature_in_c": "atm",
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "71.5",
      "date": None,
      "start_time": None,
      "end_time": "95",
      "temperature_in_c": None,
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "80",
      "date": None,
      "start_time": None,
      "end_time": "115",
      "temperature_in_c": None,
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "91.5",
      "date": None,
      "start_time": None,
      "end_time": "115",
      "temperature_in_c": None,
      "observation": "Cut-1 = 45.1g",
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "92",
      "date": None,
      "start_time": None,
      "end_time": "116",
      "temperature_in_c": None,
      "observation": None,
      "analytical_sample": None,
      "analytical_result": None
    },
    {
      "activity": "102",
      "date": None,
      "start_time": None,
      "end_time": "119.8",
      "temperature_in_c": None,
      "observation": "Cut-2 = 31.0g",
      "analytical_sample": "M.C=8.75",
      "analytical_result": None
    },
    {
      "activity": None,
      "date": None,
      "start_time": None,
      "end_time": None,
      "temperature_in_c": None,
      "observation": "Residue=16.2g",
      "analytical_sample": None,
      "analytical_result": None
    }
  ],
  "result_summary": {
    "target_material": "BZOX",
    "cas_number": None,
    "theoretical_yield_g": 298.8926,
    "actual_yield_g": 792,
    "yield_percent": 264.97812257647,
    "parameters": {
      "Purity": {
        "target": None,
        "actual": 93.24
      },
      "Melting (C)": {
        "target": None,
        "actual": None
      },
      "Moisture": {
        "target": None,
        "actual": None
      },
      "LoD": {
        "target": None,
        "actual": None
      }
    }
  },
  "abstract": "",
  "analytical_reports": [
    {
      "url": "https://logilabelnserviceprod.azurewebsites.net/protocol/downloadprotocolfile/096ca2e4-6e43-4abe-a8eb-dfe824bbcef2/mstack2269/MS25018-013-R1-S1 IP-01 4hrs_V1/pdf",
      "file_name": "MS25018-013-R1-S1 IP-01 4hrs_V1"
    },
    {
      "url": "https://logilabelnserviceprod.azurewebsites.net/protocol/downloadprotocolfile/671ee521-6f0c-4f72-b0ec-ac4f42172c76/mstack2269/MS25018-013-R1-S1 IP-02 @15°C 1.5hrs_V1/pdf",
      "file_name": "MS25018-013-R1-S1 IP-02 @15°C 1.5hrs_V1"
    },
    {
      "url": "https://logilabelnserviceprod.azurewebsites.net/protocol/downloadprotocolfile/c3b188d3-d548-4343-8c57-c1f1047d3e88/mstack2269/MS25018-013-R1-S1 Main MLR_V1/pdf",
      "file_name": "MS25018-013-R1-S1 Main MLR_V1"
    },
    {
      "url": "https://logilabelnserviceprod.azurewebsites.net/protocol/downloadprotocolfile/7323e575-3776-432f-b196-f2370689076e/mstack2269/MS25018-013-R1-S1 WET Cake_V1/pdf",
      "file_name": "MS25018-013-R1-S1 WET Cake_V1"
    },
    {
      "url": "https://logilabelnserviceprod.azurewebsites.net/protocol/downloadprotocolfile/8dccc548-9eab-4678-a7d2-7aeac88bad30/mstack2269/MS25018-013-R1-S1 Upper pyridine layer_V1/pdf",
      "file_name": "MS25018-013-R1-S1 Upper pyridine layer_V1"
    },
    {
      "url": "https://logilabelnserviceprod.azurewebsites.net/protocol/downloadprotocolfile/11ee5461-5f29-4d7e-b592-0c5f1d73ef46/mstack2269/MS25018-013-R1-S1 AF-NaOH H2O Layer_V1/pdf",
      "file_name": "MS25018-013-R1-S1 AF-NaOH H2O Layer_V1"
    },
    {
      "url": "https://logilabelnserviceprod.azurewebsites.net/protocol/downloadprotocolfile/ab7e69fe-a49b-4e63-8d90-4d741b0ac846/mstack2269/MS25018-013-R1-S1 Inorganic solid_V1/pdf",
      "file_name": "MS25018-013-R1-S1 Inorganic solid_V1"
    }
  ],
  "eln_created_at": {
    "$date": "2025-06-30T09:51:13.000Z"
  },
  "created_by": "<EMAIL>",
  "created_at": {
    "$date": "2025-09-08T07:13:11.340Z"
  },
  "updated_at": {
    "$date": "2025-09-08T07:13:11.340Z"
  }
}
    # Example usage commented out
    # from eln_mapper import ELN_MAPPING
    # transformed = transform_payload_fixed(source_data, ELN_MAPPING)
    # hit_api(json.dumps(transformed))
    # with open('output.json', 'w') as f:
    #     json.dump(transformed, f, indent=2)


class ELNHelpers:
    """
    Simple wrapper class for ELN helper functions.

    Provides a clean interface to all the helper functions while keeping
    the implementation simple and not over-engineered.
    """

    def __init__(self):
        """Initialize the helpers."""
        pass

    # Date/Time helpers
    def as_iso(self, dt_str):
        """Normalize datetime string to ISO format."""
        return as_iso(dt_str)

    def extract_date(self, obj_or_str):
        """Extract date from various formats."""
        return extract_date(obj_or_str)

    def combine_date_and_time(self, date_iso, time_str):
        """Combine date and time strings."""
        return combine_date_and_time(date_iso, time_str)

    def convert_to_utc(self, date_str):
        """Convert datetime to UTC."""
        return convert_to_utc(date_str)

    # Data transformation helpers
    def parse_temperature_value(self, s):
        """Parse temperature value with qualifiers."""
        return parse_temperature_value(s)

    def transform_payload_fixed(self, source, mapping):
        """Transform ELN payload to ChemStack format."""
        return transform_payload_fixed(source, mapping)

    def map_reactants_to_inputs(self, reactant_table):
        """Map reactant table to inputs format."""
        return map_reactants_to_inputs(reactant_table)

    def build_observation_item(self, step, current_date_iso):
        """Build observation item from step data."""
        return build_observation_item(step, current_date_iso)

    def map_analytical_reports_to_observation(self, reports):
        """Map analytical reports to observation format."""
        return map_analytical_reports_to_observation(reports)

    # Utility helpers
    def safe_uuid(self, val=None):
        """Generate safe UUID."""
        return safe_uuid(val)

    def to_identifiers(self, source):
        """Convert source to identifiers format."""
        return to_identifiers(source)

    def get_tlc_images(self, source):
        """Get TLC images from source."""
        return get_tlc_images(source)

    def build_procedure_text(self, steps):
        """Build procedure text from steps."""
        return build_procedure_text(steps)

    def best_mapping_for(self, source, mapping):
        """Find best mapping for source data."""
        return best_mapping_for(source, mapping)
