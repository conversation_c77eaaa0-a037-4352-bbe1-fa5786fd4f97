import uuid
import json
import re
from datetime import datetime, timezone
from rapidfuzz import process, fuzz
from airflow.sdk import Variable


def as_iso(dt_str):
    """
    Normalize a datetime string (or None) to ISO 8601.
    Leaves 'Z' if present; otherwise returns as-is.
    Accepts strings like '2025-08-19T09:29:00.000Z' or '2025-08-19T09:29:00Z'.
    """
    if not dt_str:
        return None
    # already ISO enough; just normalize milliseconds to 3 digits if present
    return dt_str

def extract_date(obj_or_str):
    """Return $date string if dict like {'$date': '...'}, else return str, else None."""
    if obj_or_str is None:
        return None
    if isinstance(obj_or_str, dict) and "$date" in obj_or_str:
        return as_iso(obj_or_str["$date"])
    if isinstance(obj_or_str, str):
        return as_iso(obj_or_str)
    return None


def get_id_by_fuzzy_name(name: str, records: list, threshold: int = 80):
    """
    Fuzzy match a name against a list of dicts with 'id' and 'name'.
    
    :param name: The name to search for.
    :param records: List of dicts with 'id' and 'name'.
    :param threshold: Minimum similarity score (0-100) to accept a match.
    :return: The id of the best matching name, or None if no good match found.
    """
    choices = {record["name"]: record["id"] for record in records}
    best_match = process.extractOne(name, choices.keys(), scorer=fuzz.WRatio)
    
    if best_match and best_match[1] >= threshold:
        return str(choices[best_match[0]])
    return safe_uuid(name)

def combine_date_and_time(date_iso, time_str):
    """
    Combine an ISO date string (YYYY-MM-DDTHH:MM:SSZ/...) and a 'HH:MM' time string into a new ISO in UTC.
    If date or time missing, return whichever is present (normalized).
    """
    try:
        if date_iso and time_str:
            # take date part up to 'T', then append time and 'Z' if original had Z
            z = 'Z' if date_iso.endswith('Z') else ''
            date_part = date_iso.split('T')[0]
            # ensure seconds present
            hh, mm = time_str.split(':')
            return f"{date_part}T{hh}:{mm}:00{z}"
        return date_iso or None
    except Exception:
        return None

def parse_temperature_value(s):
    """
    Convert a temperature_in_c field into value + optional qualifier ('<' or '>')
    Returns dict like {"value": 10.0, "unit": "°C"} and optionally adds "qualifier": "<"
    """
    if s is None:
        return None
    if isinstance(s, (int, float)):
        return {"value": float(s), "unit": "°C"}
    if isinstance(s, str):
        s = s.strip()
        m = re.match(r'^\s*([<>])\s*(\d+(\.\d+)?)\s*$', s)
        if m:
            q, val = m.group(1), float(m.group(2))
            return {"value": val, "unit": "°C", "qualifier": q}
        # ranges like '2-3°C' or '2-3'
        m2 = re.match(r'^\s*(\d+(\.\d+)?)\s*-\s*(\d+(\.\d+)?)', s)
        if m2:
            lo, hi = float(m2.group(1)), float(m2.group(3))
            return {"value": (lo+hi)/2.0, "unit": "°C", "range": [lo, hi]}
        # plain number string
        m3 = re.match(r'^\s*(\d+(\.\d+)?)', s)
        if m3:
            return {"value": float(m3.group(1)), "unit": "°C"}
    return None

def convert_to_utc(date_str):
    """
    Convert a datetime IST TIME string to UTC.
    """
    if not date_str:
        return None
    try:
        dt = datetime.fromisoformat(date_str.replace('Z', '+05:30'))
        return dt.astimezone(timezone.utc).isoformat()
    except Exception:
        return date_str
    

def build_observation_item(step, current_date_iso):
    """
    Map one source step to a target observation item.
    Uses current_date_iso when step['date'] is None and start_time exists.
    Returns (obs_dict, updated_current_date_iso).
    """
    date_iso = extract_date(step.get("date")) or current_date_iso
    started_at = combine_date_and_time(date_iso, step.get("start_time"))
    ended_at = combine_date_and_time(date_iso, step.get("end_time"))

    notes_parts = []
    if step.get("observation"):
        notes_parts.append(step["observation"])
    if step.get("analytical_sample"):
        notes_parts.append(f"Analytical sample: {step['analytical_sample']}")
    if step.get("analytical_result"):
        notes_parts.append(f"Analytical result: {step['analytical_result']}")
    notes = " | ".join(notes_parts) if notes_parts else None

    temp_block = parse_temperature_value(step.get("temperature_in_c"))
    obs = {
        "observation_type": "reaction_procedure",
        "procedure": step.get("activity"),
        "temperature_intermediate": temp_block,
        "temperature_jac": None,
        "sampling_details": {
            "tests": [],
            "observation": step.get("observation"),
            "quantity": None,
            "concentration": None,
            "comments": None,
        },
        "started_at": convert_to_utc(started_at),
        "ended_at": convert_to_utc(ended_at),
        "notes": notes
    }
    # If analytical_result hints a technique (e.g., LC-MS/HPLC), add a test stub
    ar = (step.get("analytical_result") or "") + " " + (step.get("observation") or "")
    tech = None
    if re.search(r'\bLC[-\s]?MS\b', ar, re.IGNORECASE):
        tech = "LC-MS"
    elif re.search(r'\bHPLC\b', ar, re.IGNORECASE):
        tech = "HPLC"
    elif re.search(r'\bGC[-\s]?MS\b', ar, re.IGNORECASE):
        tech = "GC-MS"
    if tech:
        obs["sampling_details"]["tests"].append({
            "name": tech,
            "supporting_files": [],
            "comments": step.get("analytical_result") or ""
        })

    # Update rolling date if this step provided an explicit date
    explicit_date = extract_date(step.get("date"))
    if explicit_date:
        current_date_iso = explicit_date
    return obs, current_date_iso

def map_analytical_reports_to_observation(reports):
    """Wrap analytical_reports files into one observation with tests/supporting_files."""
    if not reports:
        return None
    tests = []
    files = []
    for r in reports:
        url = r.get("url")
        name = r.get("file_name")
        mime = "application/pdf" if (url and url.lower().endswith(".pdf")) else None
        files.append({
            "id": None,
            "created_at": get_iso_date(),
            "updated_at": get_iso_date(),
            "name": name,
            "url": str(url),
            "path": r.get("pdf_path"),
            "type": mime or "application/octet-stream",
            "size": None,
            "uploaded_at": get_iso_date(),
            "uploaded_by": safe_uuid(r.get("created_by")),
            "mime_type": mime,
            "is_public": False
        })
    tests.append({
        "name": "Analytical Report",
        "supporting_files": files,
        "comments": ""
    })
    return {
        "procedure": "Analytical reports attached",
        "temperature_intermediate": None,
        "temperature_jac": None,
        "sampling_details": {
            "tests": tests,
            "observation": None,
            "quantity": None,
            "concentration": None,
            "comments": None
        },
        "started_at": None,
        "ended_at": None,
        "notes": None
    }

def map_reactants_to_inputs(reactant_table):
    inputs = []

    # find limiting reagent (smallest mol_eq_rel_wt > 0)
    valid_eq = [r.get("mol_eq_rel_wt") for r in reactant_table if r.get("mol_eq_rel_wt")]
    min_eq = min(valid_eq) if valid_eq else None

    for reactant in reactant_table:
        component = {
            "name": reactant.get("reactant_name", ""),
            "role": "reactant" if reactant.get("molecular_weight") else "solvent",
            "cas_number": "",  # if you have a lookup table, fill this
            "molecular_weight": reactant.get("molecular_weight", 0),
            "smiles": ""  # fill if you have SMILES data
        }

        quantity = {
            "value": reactant.get("reactant_qty_gm", 0) or 0,
            "unit": "g"
        }

        sample_quantity = {
            "value": reactant.get("sample_qty_gm", 0) or 0,
            "unit": "g"
        }

        mapped = {
            "component": component,
            "quantity": quantity,
            "sample_quantity": sample_quantity,
            "strength": reactant.get("strength_percent", 0) or 0,
            "moles": reactant.get("no_of_moles", 0) or 0,
            "mole_equivalence": reactant.get("mol_eq_rel_wt", 0) or 0,
            "active": True,
            "is_limiting": (reactant.get("mol_eq_rel_wt") == min_eq if min_eq else False)
        }

        inputs.append(mapped)

    return inputs


def best_mapping_for(source, mapping):
    """
    Try to resolve a mapping key based on source route/project/step fields.
    Tries a few reasonable variants; returns mapping dict or {}.
    """
    proj = (source.get("route_information", {}).get("project_code") or "").strip()
    route_num = (source.get("route_information", {}).get("route_number") or "").strip()
    step_name = (source.get("route_information", {}).get("step_name") or "").strip()
    candidates = []
    if proj and step_name:
        candidates.append(f"{proj}-Step-{step_name}")
        candidates.append(f"{proj}-S{step_name}")
        candidates.append(f"{proj} Step {step_name}")
    # direct project_code alone
    if proj:
        candidates.append(proj)
    # route variants
    if proj and route_num:
        candidates.append(f"{proj}-R{route_num}")
    # try all case-insensitive matches
    lower_map = {k.lower(): v for k, v in mapping.items()}
    for c in candidates:
        if c.lower() in lower_map:
            return lower_map[c.lower()]
    return {}

def to_identifiers(source):
    ids = []
    if "protocol_id" in source:
        ids.append({"type": "protocol_id", "value": source["protocol_id"]})
    if "protocol_name" in source:
        ids.append({"type": "protocol_name", "value": source["protocol_name"]})
    return ids

def get_tlc_images(source):
    tlc_images = []
    for image in source.get("tlc_images", []):
        tlc_images.append({
            "id": None,
            "created_at": convert_to_utc(image.get("eln_created_at")),
            "updated_at": convert_to_utc(image.get("eln_created_at")),
            "name": "test-image.jpg",
            "url": image.get("image"),
            "path": image.get("image"),
            "type": "experimentation",
            "size": 114563,
            "uploaded_at": convert_to_utc(image.get("eln_created_at")),
            "uploaded_by": image.get("created_by"),
            "mime_type": "image/jpeg",
            "is_public": False
        })
    return tlc_images

def build_procedure_text(steps):
    """Create a human-readable procedure timeline."""
    lines = []
    for s in steps:
        t = s.get("start_time") or ""
        d = extract_date(s.get("date")) or ""
        act = s.get("activity") or ""
        obs = s.get("observation") or ""
        temp = s.get("temperature_in_c")
        temp_str = f" @ {temp}°C" if isinstance(temp, (int, float)) else (f" @ {temp}°C" if isinstance(temp, str) and temp else "")
        bits = [b for b in [d, t, act] if b]
        line = " — ".join(bits)
        if obs or temp:
            extras = []
            if temp:
                extras.append(f"Temp: {temp}")
            if obs:
                extras.append(f"Obs: {obs}")
            line += f" ({'; '.join(extras)})"
        lines.append(line)
    return "\n".join([ln for ln in lines if ln])

def get_iso_date():
    return datetime.now(tz=timezone.utc).isoformat()

def transform_payload(source: dict, mapping: dict = None) -> dict:
    """
    Transform ELN source data to ChemStack payload format.

    Combines the original transform_payload and transform_payload_fixed
    into a single function for simplicity.
    """
    user_data = Variable.get('USER_DATA', deserialize_json=True)
    exp_overview = source.get("experiment_overview", {})
    personnel = source.get("personnel_and_timeline", {})
    route_info = source.get("route_information", {})
    reaction_scheme = source.get("reaction_scheme", {}) if source.get("reaction_scheme") else {}

    map_values = mapping

    created_at = extract_date(source.get("eln_created_at"))
    updated_at = extract_date(source.get("eln_updated_at"))
    started_at = extract_date(personnel.get("experiment_start_date"))
    ended_at = extract_date(personnel.get("experiment_end_date"))

    # Build observations
    observations = []
    steps = source.get("procedure_and_observations", []) or []
    rolling_date = (extract_date(steps[0].get("date")) if steps else None) or started_at
    for step in steps:
        obs_item, rolling_date = build_observation_item(step, rolling_date)
        observations.append(obs_item)

    reports_obs = map_analytical_reports_to_observation(source.get("analytical_reports"))
    if reports_obs:
        observations.append(reports_obs)

    # Reaction image meta (best-effort from data URI)
    img_mime = "image/jpeg" if str(reaction_scheme.get("image", "")).startswith("data:image/jpeg") else None

    payload = {
        "created_at": created_at or get_iso_date(),
        "updated_at": updated_at or get_iso_date(),
        "tenant_id": map_values.get("tenant_id"),
        "step_id": map_values.get("step_id"),
        "project_id": map_values.get("project_id"),
        "route_id": map_values.get("route_id"),
        "experiment_number": map_values.get("step_number") or exp_overview.get("experiment_number") or route_info.get("project_code"),
        "experiment_date": started_at,
        "experiment_type": "Validation",
        "started_at": started_at,
        "ended_at": ended_at,
        "title": exp_overview.get("title"),
        "aim": exp_overview.get("aim"),
        "identifiers": [{"type": "protocol_id", "value": str(source.get("protocol_id"))}],
        "inputs": map_reactants_to_inputs(source.get("reactant_table", [])),
        "conditions": {
            "temperature": None,
            "concentration": {"unit": "M", "value": 0.0},
            "pressure": None,
            "time": {"unit": "hour", "value": 0.0}, 
            "stirring": {"type": ""}
        },
        "setup": None,
        # "procedure": build_procedure_text(steps),
        "lab_requirements": None,
        "reaction_image": {
            "id": None,
            "created_at": extract_date(reaction_scheme.get("eln_updated_at")) or get_iso_date(),
            "updated_at": extract_date(reaction_scheme.get("eln_updated_at")) or get_iso_date(),
            "name": "reaction-scheme.jpg",
            "url": None,
            "path": None,
            "type": "experimentation",
            "size": None,
            "uploaded_at": extract_date(reaction_scheme.get("eln_updated_at")) or get_iso_date(),
            "uploaded_by": safe_uuid(source.get("created_by")),
            "mime_type": img_mime,
            "is_public": False
        },
        "lab_instructions": "",
        "safety_notes": "",
        "self_declaration": {
            "given_by": personnel.get("created_by"),
            "given_at": extract_date(source.get("eln_updated_at")) or get_iso_date()
        },
        "products": [],  # result_summary lacks target material; keeping empty
        "observations": observations,
        "led_by": get_id_by_fuzzy_name(personnel.get("experiment_led_by") or "", user_data), 
        "performed_by": [personnel.get("experiment_done_by")] if personnel.get("experiment_done_by") else [],  # Fixed: ensure list
        "status": "pending_approval",
        "yield_value": source.get("result_summary", {}).get("yield_percent") if source.get("result_summary", {}).get("yield_percent") and source.get("result_summary", {}).get("yield_percent")  < 100 else 50,
        "purity": source.get("result_summary", {}).get("parameters", {}).get("Purity", {}).get("actual"),
        "summary": source.get("abstract", ""),
        "conclusion": exp_overview.get("conclusion", ""),
        "tlc_images": get_tlc_images(source),
        "active": True,
        "synced_to_ml_model": False,
        "created_by": safe_uuid(source.get("created_by")),
        "updated_by": safe_uuid(source.get("created_by")),
        "revision": 1,
        "route_number": route_info.get("route_number"),
        "external_eln_id": str(source.get("protocol_id"))
    }

    # # Fix supporting files timestamps & uploaded_by
    for obs in payload.get("observations", []):
        if "sampling_details" in obs:
            for test in obs["sampling_details"].get("tests", []):
                for f in test.get("supporting_files", []):
                    if source.get("created_at") and isinstance(source["created_at"], dict):
                        f["created_at"] = created_at
                        f["updated_at"] = updated_at
                        f["uploaded_at"] = created_at
                    f["uploaded_by"] = safe_uuid(source.get("created_by"))

    
    return payload


def safe_uuid(val=None):
    """Ensure we always return a UUID string instead of emails or None."""
    try:
        return str(uuid.UUID(str(val)))
    except Exception:
        return str(uuid.uuid4())
