"""
Test Database Integration

This script tests that the new db_operations.py works correctly with your existing
mongo_operations.py and database configuration.
"""

import logging
import sys
import os

# Add paths for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, '/opt/airflow/dag_dependencies')
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_db_operations():
    """Test the updated db_operations.py with your existing mongo setup."""
    print("=" * 60)
    print("TESTING DB OPERATIONS WITH EXISTING MONGO SETUP")
    print("=" * 60)
    
    try:
        from db_operations import DatabaseOperations
        
        # Initialize database operations
        db_ops = DatabaseOperations()
        print("✓ DatabaseOperations initialized successfully")
        
        # Test health check
        health = db_ops.health_check()
        print(f"Health check: {'✓ PASSED' if health else '✗ FAILED'}")
        
        if not health:
            print("⚠️  Database not accessible - check MongoDB connection")
            return False
        
        # Test getting stats
        stats = db_ops.get_stats()
        print(f"Database stats:")
        for collection, info in stats.items():
            print(f"  {collection}: {info.get('count', 0)} records")
        
        # Test getting last sync date
        last_sync = db_ops.get_last_sync_date()
        print(f"Last sync date: {last_sync}")
        
        # Test getting run logs
        logs = db_ops.get_run_logs(3)
        print(f"Recent logs: {len(logs)} entries")
        
        # Test inserting a run log (this will actually insert data)
        from datetime import datetime
        success = db_ops.insert_run_log(datetime.now(), "Test log entry")
        print(f"Insert run log: {'✓ SUCCESS' if success else '✗ FAILED'}")
        
        # Test upserting ELN data (this will actually insert data)
        test_data = {
            "test_field": "test_value",
            "experiment_overview": {
                "experiment_number": "TEST-001"
            }
        }
        success = db_ops.upsert_eln_data("TEST_PROTOCOL_001", test_data)
        print(f"Upsert ELN data: {'✓ SUCCESS' if success else '✗ FAILED'}")
        
        # Test getting the data back
        retrieved_data = db_ops.get_eln_data("TEST_PROTOCOL_001")
        if retrieved_data:
            print(f"✓ Retrieved ELN data: protocol_id = {retrieved_data.get('protocol_id')}")
        else:
            print("✗ Could not retrieve ELN data")
        
        # Test reaction experiment operations
        success = db_ops.upsert_reaction_experiment("TEST-001", "test-reaction-id-123")
        print(f"Upsert reaction experiment: {'✓ SUCCESS' if success else '✗ FAILED'}")
        
        reaction_id = db_ops.get_reaction_id("TEST-001")
        if reaction_id:
            print(f"✓ Retrieved reaction ID: {reaction_id}")
        else:
            print("✗ Could not retrieve reaction ID")
        
        # Clean up
        db_ops.close_connection()
        print("\n✅ All database operations tests completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Database operations test failed: {e}")
        logger.exception("Database operations test failed")
        return False


def test_simple_eln_sync_with_db():
    """Test the simple ELN sync with the updated database operations."""
    print("=" * 60)
    print("TESTING SIMPLE ELN SYNC WITH UPDATED DB")
    print("=" * 60)
    
    try:
        from simple_eln_sync import SimpleELNSync
        
        sync = SimpleELNSync()
        print("✓ SimpleELNSync initialized successfully")
        
        # Test getting sync status
        status = sync.get_sync_status()
        
        if "error" in status:
            print(f"⚠️  Sync status error: {status['error']}")
            return False
        
        print("Sync status:")
        print(f"  Last sync: {status.get('last_sync_date', 'Never')}")
        print(f"  Database health: {'✓' if status.get('database_health') else '✗'}")
        
        db_stats = status.get('database_stats', {})
        if db_stats:
            print("  Database collections:")
            for collection, stats in db_stats.items():
                print(f"    {collection}: {stats.get('count', 0)} records")
        
        # Test experiment validation
        test_experiments = [
            "MS25012-010-R1-Step-1",
            "MS25018-013-R1-S1"
        ]
        
        validation_results = sync.validate_experiments(test_experiments)
        print(f"\nExperiment validation results:")
        for exp, result in validation_results.items():
            status_icon = "✓" if result['valid'] else "✗"
            print(f"  {status_icon} {exp} -> {result.get('normalized', 'FAILED')}")
        
        sync.cleanup()
        print("\n✅ Simple ELN sync with database test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Simple ELN sync test failed: {e}")
        logger.exception("Simple ELN sync test failed")
        return False


def main():
    """Run all integration tests."""
    print("🔗 TESTING DATABASE INTEGRATION")
    print("=" * 60)
    print("This test verifies that the new db_operations.py works correctly")
    print("with your existing mongo_operations.py and database configuration.")
    print("=" * 60)
    print()
    
    tests = [
        ("Database Operations", test_db_operations),
        ("Simple ELN Sync with DB", test_simple_eln_sync_with_db)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
            print()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            logger.exception(f"{test_name} test crashed")
            results[test_name] = False
            print()
    
    # Summary
    print("=" * 60)
    print("INTEGRATION TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name:<25} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All integration tests passed!")
        print("\nYour simplified ELN system is now properly integrated with:")
        print("✓ Your existing mongo_operations.py")
        print("✓ Your existing database configuration")
        print("✓ Clean ELN-specific database operations")
        print("✓ Experiment number normalization")
        print("✓ Simple sync workflow")
        print("\nYou can now use:")
        print("- python simple_usage_example.py (for examples)")
        print("- SimpleELNSync class for actual synchronization")
    else:
        print(f"\n⚠️  {total - passed} tests failed.")
        print("\nTroubleshooting:")
        print("1. Check that MongoDB is running")
        print("2. Verify your Airflow variables are set correctly")
        print("3. Check database permissions")
        print("4. Review the error messages above")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
