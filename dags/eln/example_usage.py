"""
Example Usage of the Enhanced ELN Synchronization System

This script demonstrates how to use the new organized ELN synchronization system
with all the improved components: database operations, ChemStack client,
data transformation, mapping services, and experiment normalization.
"""

import logging
from datetime import datetime

from eln_orchestrator import ELNOrchestrator
from experiment_normalizer import ExperimentNumberNormalizer
from mapper_service import EnhancedMapperService
from chemstack_client import ChemStackClient
from db_operations import DatabaseOperations

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def example_full_sync():
    """Example of running a full ELN synchronization."""
    logger.info("Starting example full sync")
    
    # Initialize the orchestrator (it will create all required services)
    orchestrator = ELNOrchestrator()
    
    try:
        # Run the synchronization
        result = orchestrator.run_full_sync(force_full_sync=False)
        
        # Print results
        summary = result.get_summary()
        logger.info(f"Sync completed: {summary}")
        
        if result.errors:
            logger.warning(f"Errors encountered: {len(result.errors)}")
            for error in result.errors[:5]:  # Show first 5 errors
                logger.error(f"Protocol {error['protocol_id']}: {error['error']}")
        
        return summary
        
    finally:
        # Clean up resources
        orchestrator.cleanup_resources()


def example_single_protocol_sync():
    """Example of syncing a single protocol."""
    logger.info("Starting example single protocol sync")
    
    orchestrator = ELNOrchestrator()
    
    try:
        # Sync a specific protocol (replace with actual protocol ID)
        protocol_id = "1000129"
        result = orchestrator.sync_single_protocol(protocol_id)
        
        logger.info(f"Single protocol sync result: {result}")
        return result
        
    finally:
        orchestrator.cleanup_resources()


def example_experiment_normalization():
    """Example of experiment number normalization."""
    logger.info("Starting experiment normalization example")
    
    normalizer = ExperimentNumberNormalizer()
    
    # Test various experiment number formats
    test_experiments = [
        "MS25012-010-R1-Step-1",
        "MS25012-012-R1-Step-1", 
        "MS25013-005-R2-Step-2",
        "MS25018-013-R1-S1",
        "MS25007-R1-010",
        "MS25005",
        "INVALID-123"
    ]
    
    logger.info("Testing experiment number normalization:")
    for exp in test_experiments:
        normalized = normalizer.normalize_experiment_number(exp)
        is_valid, message = normalizer.validate_experiment_number(exp)
        
        logger.info(f"  {exp} -> {normalized} (Valid: {is_valid})")
        if not is_valid:
            logger.warning(f"    {message}")
    
    # Get batch normalization stats
    stats = normalizer.get_normalization_stats(test_experiments)
    logger.info(f"Normalization stats: {stats}")
    
    return stats


def example_mapping_service():
    """Example of using the enhanced mapping service."""
    logger.info("Starting mapping service example")
    
    # Initialize services
    chemstack_client = ChemStackClient()
    mapper_service = EnhancedMapperService(chemstack_client)
    
    # Test mapping for different experiment numbers
    test_experiments = [
        "MS25012-007-R1-S1",
        "MS25018-013-R1-S1",
        "MS25013-005-R2-S2"
    ]
    
    logger.info("Testing mapping service:")
    for exp in test_experiments:
        mapping_result = mapper_service.get_mapping(exp)
        
        logger.info(f"  {exp}:")
        logger.info(f"    Source: {mapping_result.source}")
        logger.info(f"    Confidence: {mapping_result.confidence}")
        logger.info(f"    Project ID: {mapping_result.project_id}")
        logger.info(f"    Route ID: {mapping_result.route_id}")
        logger.info(f"    Step ID: {mapping_result.step_id}")
        
        # Construct ChemStack URL
        if mapper_service.validate_mapping(mapping_result):
            url = mapper_service.construct_chemstack_url(mapping_result)
            logger.info(f"    ChemStack URL: {url}")
        else:
            logger.warning(f"    Invalid mapping for {exp}")
    
    # Get mapping statistics
    stats = mapper_service.get_mapping_stats()
    logger.info(f"Mapping service stats: {stats}")


def example_database_operations():
    """Example of database operations."""
    logger.info("Starting database operations example")
    
    db_ops = DatabaseOperations()
    
    try:
        # Check database health
        health = db_ops.health_check()
        logger.info(f"Database health: {health}")
        
        if not health:
            logger.error("Database is not accessible")
            return
        
        # Get database statistics
        stats = db_ops.get_stats()
        logger.info(f"Database stats: {stats}")
        
        # Get last sync date
        last_sync = db_ops.get_last_sync_date()
        logger.info(f"Last sync date: {last_sync}")
        
        # Get recent run logs
        logs = db_ops.get_run_logs(3)
        logger.info(f"Recent run logs: {len(logs)} entries")
        for log in logs:
            logger.info(f"  {log.get('created_at')}: {log.get('status')} - {log.get('error', 'No error')}")
        
        # Example of inserting test data (commented out to avoid actual insertion)
        # test_data = {
        #     "experiment_overview": {"experiment_number": "TEST-001"},
        #     "test_field": "test_value"
        # }
        # db_ops.upsert_eln_data("TEST_PROTOCOL", test_data)
        # logger.info("Test data inserted")
        
    finally:
        db_ops.close_connection()


def example_sync_status():
    """Example of getting sync status."""
    logger.info("Getting sync status")
    
    orchestrator = ELNOrchestrator()
    
    try:
        status = orchestrator.get_sync_status()
        
        logger.info("Current sync status:")
        logger.info(f"  Last sync: {status.get('last_sync_date')}")
        logger.info(f"  ChemStack health: {status.get('chemstack_health')}")
        logger.info(f"  Database health: {status.get('database_health')}")
        logger.info(f"  Supported projects: {status.get('supported_projects')}")
        
        db_stats = status.get('database_stats', {})
        for collection, stats in db_stats.items():
            logger.info(f"  {collection}: {stats.get('count', 0)} records")
        
        return status
        
    finally:
        orchestrator.cleanup_resources()


def example_validate_experiments():
    """Example of validating experiment numbers."""
    logger.info("Validating experiment numbers")
    
    orchestrator = ELNOrchestrator()
    
    # Test experiments from your 8 molecules
    test_experiments = [
        "MS25012-010-R1-Step-1",
        "MS25013-005-R2-Step-2", 
        "MS25005-003-R1-Step-1",
        "MS25007-008-R1-Step-1",
        "MS25016-001-R1-Step-1",
        "MS25010-002-R1-Step-1",
        "MS25017-004-R1-Step-1",
        "MS25018-013-R1-S1"
    ]
    
    try:
        validation_result = orchestrator.validate_experiment_batch(test_experiments)
        
        logger.info("Experiment validation results:")
        
        normalization_results = validation_result.get('normalization_results', {})
        for original, normalized in normalization_results.items():
            if normalized:
                logger.info(f"  ✓ {original} -> {normalized}")
            else:
                logger.warning(f"  ✗ {original} -> Failed to normalize")
        
        stats = validation_result.get('statistics', {})
        logger.info(f"Validation statistics: {stats}")
        
        return validation_result
        
    finally:
        orchestrator.cleanup_resources()


if __name__ == "__main__":
    logger.info("Starting ELN system examples")
    
    try:
        # Run different examples
        logger.info("=" * 50)
        example_experiment_normalization()
        
        logger.info("=" * 50)
        example_mapping_service()
        
        logger.info("=" * 50)
        example_database_operations()
        
        logger.info("=" * 50)
        example_sync_status()
        
        logger.info("=" * 50)
        example_validate_experiments()
        
        # Uncomment these for actual synchronization
        # logger.info("=" * 50)
        # example_single_protocol_sync()
        
        # logger.info("=" * 50)
        # example_full_sync()
        
        logger.info("All examples completed successfully")
        
    except Exception as e:
        logger.error(f"Example execution failed: {e}")
        raise
