import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
from eln.eln_parser import <PERSON><PERSON>NParser
from airflow import DAG
import json
from airflow.operators.python import PythonOperator
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, List, Optional
import logging

def parse_eln_data(**context):
    parser = ELNParser()
    last_modified_date = parser.get_all_data(dag_running_date=context['data_interval_start'])
    parser.insert_last_inserted_data(last_modified_date=last_modified_date)
    logging.info("Parsed ELN data")

with DAG(
    'eln_to_mstack_dag',
    default_args={
        'owner': 'airflow',
        'depends_on_past': False,
        'email_on_failure': False,
        'email_on_retry': False,
        'retries': 1,
        'retry_delay': timedelta(minutes=5),
    },
    description='Parse ELN data and store in MongoDB',
    schedule=timedelta(days=1),  # Run every 1 day
    catchup=False,
    start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
    tags=['eln', 'mstack', 'mongo'],
) as dag:
    parse_eln_data_task = PythonOperator(
        task_id='parse_eln_data',
        python_callable=parse_eln_data,
        dag=dag,
        execution_timeout=timedelta(minutes=50),
    )
