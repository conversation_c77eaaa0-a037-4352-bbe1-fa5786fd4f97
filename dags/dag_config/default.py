import datetime
from typing import Dict, Any

# Default DAG arguments
DEFAULT_DAG_ARGS: Dict[str, Any] = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': 5  # minutes

}

# 1. Get the current UTC time (timezone-aware)
current_utc_time = datetime.datetime.now(datetime.timezone.utc)

# 2. Define a duration of 5 minutes
five_minutes = datetime.timedelta(minutes=120)


# 3. Subtract the duration from the current UTC time
QueryRun = current_utc_time - five_minutes

# Default sync configuration
DEFAULT_SYNC_CONFIG: Dict[str, Any] = {
    'postgres_table': 'your_table',
    'mongo_collection': 'your_collection',
    'sync_interval_days': 1,
    'field_mappings': {
        'field1': 'field1',
        'field2': 'field2'
    }
} 
