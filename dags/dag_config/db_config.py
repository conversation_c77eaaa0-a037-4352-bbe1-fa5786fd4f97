from airflow.sdk import Variable
from typing import Dict, Any, <PERSON><PERSON>
import os
from dag_config.default import DEFAULT_SYNC_CONFIG
import logging
from dotenv import load_dotenv

load_dotenv()

class DatabaseConfig:
    
    
    @staticmethod
    def get_chem_stack_mongo_params() -> Tuple[str, str]:
        """
        Get MongoDB connection parameters from Airflow variables with defaults from environment
        :return: Tuple containing (connection_string, database_name)
        """

        return {
            'connection_string': Variable.get('chem_stack_mongo_connection_string', default=os.getenv('CHEM_STACK_MONGO_CONNECTION_STRING')),
            'database_name': Variable.get('chem_stack_mongo_db_name', default=os.getenv('CHEM_STACK_MONGO_DB_NAME'))
        }
