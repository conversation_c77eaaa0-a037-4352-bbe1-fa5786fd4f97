

from azure.storage.blob import BlobServiceClient
from airflow.sdk import Variable
import logging
azure_logger = logging.getLogger("azure")
azure_logger.setLevel(logging.WARNING)

class AzureUtils:
    """
    Utility class for Azure Blob Storage operations
    """
    def __init__(self , container_name: str):
        self.connection_string = Variable.get('AZURE_STORAGE_CONNECTION_STRING', '')
        self.container_name = container_name
        self.blob_service_client = BlobServiceClient.from_connection_string(self.connection_string)
    
    def upload_file(self, blob_path: str , file_data: bytes):
        """
        Upload a file to Azure Blob Storage
        :param blob_name: Name of the blob
        :param file_data: File data to upload
        :return: None
        """
        try:
            blob_client = self.blob_service_client.get_blob_client(container=self.container_name, blob=blob_path)
            blob_client.upload_blob(file_data, overwrite=True)
            return blob_client.blob_name
        except Exception as e:
            return blob_path
    
    def download_file(self, blob_path: str) -> bytes:
        """
        Download a file from Azure Blob Storage
        :param blob_name: Name of the blob
        :return: File data
        """
        blob_client = self.blob_service_client.get_blob_client(container=self.container_name, blob=blob_path)
        return blob_client.download_blob().readall()
        
        
