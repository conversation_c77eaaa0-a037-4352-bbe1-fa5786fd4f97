"""
ELN Synchronization DAG

This DAG runs the ELN synchronization system to sync data from ELN to ChemStack.
It uses the cleaned up ELN sync system with proper error handling and monitoring.
"""

import logging
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import <PERSON><PERSON>perator
import sys
import os
from eln.eln_sync import ELNSync
from datetime import timezone

sys.path.insert(0, '/opt/airflow/dag_dependencies')

logger = logging.getLogger(__name__)

# DAG Configuration
DAG_ID = 'eln_sync_dag'
SCHEDULE_INTERVAL = '0 2 * * *'  # Daily at 2 AM
MAX_ACTIVE_RUNS = 1
CATCHUP = False

# Default arguments for the DAG
default_args = {
    'owner': 'data-team',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1, tzinfo=timezone.utc),
    'retries': 2,
    'retry_delay': timedelta(minutes=15),
    'execution_timeout': timedelta(hours=2),
}


def run_eln_sync(**context):
    """
    Main function to run ELN synchronization.
    
    Args:
        context: Airflow context containing execution date, etc.
    """
    try:
        
        logger.info("Starting ELN synchronization")
        
        sync = ELNSync()
        
        try:

            force_full_sync = False
            results = sync.sync_all_protocols(force_full_sync=force_full_sync)
            
            logger.info(f"ELN sync completed: {results}")
            
            if results['failed'] > 0:
                error_msg = f"ELN sync completed with {results['failed']} failures out of {results['total_protocols']} protocols"
                logger.warning(error_msg)
                
                failure_rate = results['failed'] / max(results['total_protocols'], 1)
                if failure_rate > 0.5:
                    raise Exception(f"High failure rate: {failure_rate:.1%} of protocols failed")
            
            return {
                'status': 'success',
                'total_protocols': results['total_protocols'],
                'successful': results['successful'],
                'failed': results['failed'],
                'errors': results['errors'][:5]
            }
            
        finally:
            sync.cleanup()
            
    except Exception as e:
        logger.error(f"ELN sync failed: {e}")
        raise

dag = DAG(
    DAG_ID,
    default_args=default_args,
    description='ELN to ChemStack synchronization pipeline',
    catchup=CATCHUP,
    schedule=timedelta(days=1),  # Run every 1 day
    tags=['eln', 'chemstack', 'sync', 'data-pipeline'],
)

run_sync_task = PythonOperator(
    task_id='run_eln_sync',
    python_callable=run_eln_sync,
    dag=dag,
)

run_sync_task