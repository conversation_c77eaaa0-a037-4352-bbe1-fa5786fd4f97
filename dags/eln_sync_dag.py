"""
ELN Synchronization DAG

This DAG runs the ELN synchronization system to sync data from ELN to ChemStack.
It uses the cleaned up ELN sync system with proper error handling and monitoring.
"""

import logging
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.email import EmailOperator
from airflow.utils.dates import days_ago
import sys
import os

# Add paths for ELN imports
sys.path.insert(0, '/opt/airflow/dags/eln')
sys.path.insert(0, '/opt/airflow/dag_dependencies')

logger = logging.getLogger(__name__)

# DAG Configuration
DAG_ID = 'eln_sync_dag'
SCHEDULE_INTERVAL = '0 2 * * *'  # Daily at 2 AM
MAX_ACTIVE_RUNS = 1
CATCHUP = False

# Email configuration for notifications
EMAIL_ON_FAILURE = True
EMAIL_ON_RETRY = False
EMAIL_LIST = ['<EMAIL>']  # Update with actual email

# Default arguments for the DAG
default_args = {
    'owner': 'data-team',
    'depends_on_past': False,
    'start_date': days_ago(1),
    'email_on_failure': EMAIL_ON_FAILURE,
    'email_on_retry': EMAIL_ON_RETRY,
    'email': EMAIL_LIST,
    'retries': 2,
    'retry_delay': timedelta(minutes=15),
    'execution_timeout': timedelta(hours=2),
}


def run_eln_sync(**context):
    """
    Main function to run ELN synchronization.
    
    Args:
        context: Airflow context containing execution date, etc.
    """
    try:
        from simple_eln_sync import SimpleELNSync
        
        logger.info("Starting ELN synchronization")
        
        # Initialize the sync system
        sync = SimpleELNSync()
        
        try:
            # Check if this is a manual run or scheduled run
            execution_date = context['execution_date']
            is_manual = context.get('dag_run').external_trigger if context.get('dag_run') else False
            
            logger.info(f"Execution date: {execution_date}, Manual trigger: {is_manual}")
            
            # Run the synchronization
            force_full_sync = is_manual  # Force full sync for manual runs
            results = sync.sync_all_protocols(force_full_sync=force_full_sync)
            
            # Log results
            logger.info(f"ELN sync completed: {results}")
            
            # Check for failures
            if results['failed'] > 0:
                error_msg = f"ELN sync completed with {results['failed']} failures out of {results['total_protocols']} protocols"
                logger.warning(error_msg)
                
                # If more than 50% failed, raise an exception
                failure_rate = results['failed'] / max(results['total_protocols'], 1)
                if failure_rate > 0.5:
                    raise Exception(f"High failure rate: {failure_rate:.1%} of protocols failed")
            
            # Return results for downstream tasks
            return {
                'status': 'success',
                'total_protocols': results['total_protocols'],
                'successful': results['successful'],
                'failed': results['failed'],
                'errors': results['errors'][:5]  # First 5 errors only
            }
            
        finally:
            # Always cleanup resources
            sync.cleanup()
            
    except Exception as e:
        logger.error(f"ELN sync failed: {e}")
        raise


def validate_experiments(**context):
    """
    Validate experiment numbers for the 8 molecules.
    
    Args:
        context: Airflow context
    """
    try:
        from simple_eln_sync import SimpleELNSync
        
        logger.info("Validating experiment numbers")
        
        sync = SimpleELNSync()
        
        try:
            # Your 8 molecules test cases
            test_experiments = [
                "MS25012-010-R1-Step-1",
                "MS25013-005-R2-Step-2",
                "MS25005-003-R1-Step-1",
                "MS25007-008-R1-Step-1",
                "MS25016-001-R1-Step-1",
                "MS25010-002-R1-Step-1",
                "MS25017-004-R1-Step-1",
                "MS25018-013-R1-S1"
            ]
            
            validation_results = sync.validate_experiments(test_experiments)
            
            # Log validation results
            valid_count = sum(1 for r in validation_results.values() if r['valid'])
            total_count = len(validation_results)
            
            logger.info(f"Experiment validation: {valid_count}/{total_count} experiments are valid")
            
            for exp, result in validation_results.items():
                if result['valid']:
                    logger.info(f"✓ {exp} -> {result.get('normalized')}")
                else:
                    logger.warning(f"✗ {exp} -> {result.get('message')}")
            
            return {
                'valid_count': valid_count,
                'total_count': total_count,
                'validation_results': validation_results
            }
            
        finally:
            sync.cleanup()
            
    except Exception as e:
        logger.error(f"Experiment validation failed: {e}")
        raise


def check_sync_status(**context):
    """
    Check the current sync status and database health.
    
    Args:
        context: Airflow context
    """
    try:
        from simple_eln_sync import SimpleELNSync
        
        logger.info("Checking sync status")
        
        sync = SimpleELNSync()
        
        try:
            status = sync.get_sync_status()
            
            if "error" in status:
                logger.error(f"Sync status error: {status['error']}")
                raise Exception(f"Could not get sync status: {status['error']}")
            
            # Log status information
            logger.info(f"Last sync: {status.get('last_sync_date', 'Never')}")
            logger.info(f"Database health: {'OK' if status.get('database_health') else 'FAILED'}")
            
            db_stats = status.get('database_stats', {})
            for collection, stats in db_stats.items():
                logger.info(f"{collection}: {stats.get('count', 0)} records")
            
            # Check database health
            if not status.get('database_health'):
                raise Exception("Database health check failed")
            
            return status
            
        finally:
            sync.cleanup()
            
    except Exception as e:
        logger.error(f"Sync status check failed: {e}")
        raise


def send_success_notification(**context):
    """Send success notification with sync results."""
    sync_results = context['task_instance'].xcom_pull(task_ids='run_eln_sync')
    
    if sync_results:
        subject = f"ELN Sync Completed Successfully - {sync_results['successful']} protocols synced"
        html_content = f"""
        <h3>ELN Synchronization Results</h3>
        <ul>
            <li><strong>Total Protocols:</strong> {sync_results['total_protocols']}</li>
            <li><strong>Successful:</strong> {sync_results['successful']}</li>
            <li><strong>Failed:</strong> {sync_results['failed']}</li>
        </ul>
        
        <p><strong>Execution Date:</strong> {context['execution_date']}</p>
        
        {f"<p><strong>Errors:</strong><br>{'<br>'.join(sync_results['errors'])}</p>" if sync_results['errors'] else ""}
        """
        
        return {
            'subject': subject,
            'html_content': html_content
        }
    
    return {
        'subject': 'ELN Sync Completed',
        'html_content': 'ELN synchronization completed successfully.'
    }


# Create the DAG
dag = DAG(
    DAG_ID,
    default_args=default_args,
    description='ELN to ChemStack synchronization pipeline',
    schedule_interval=SCHEDULE_INTERVAL,
    max_active_runs=MAX_ACTIVE_RUNS,
    catchup=CATCHUP,
    tags=['eln', 'chemstack', 'sync', 'data-pipeline'],
)

# Task 1: Check sync status and database health
check_status_task = PythonOperator(
    task_id='check_sync_status',
    python_callable=check_sync_status,
    dag=dag,
)

# Task 2: Validate experiment numbers
validate_experiments_task = PythonOperator(
    task_id='validate_experiments',
    python_callable=validate_experiments,
    dag=dag,
)

# Task 3: Run ELN synchronization
run_sync_task = PythonOperator(
    task_id='run_eln_sync',
    python_callable=run_eln_sync,
    dag=dag,
)

# Task 4: Send success notification (optional)
# Uncomment if you want email notifications
# success_notification_task = EmailOperator(
#     task_id='send_success_notification',
#     to=EMAIL_LIST,
#     subject='ELN Sync Completed Successfully',
#     html_content='{{ ti.xcom_pull(task_ids="send_success_notification")["html_content"] }}',
#     dag=dag,
#     trigger_rule='all_success',
# )

# Set task dependencies
check_status_task >> validate_experiments_task >> run_sync_task
# Uncomment if using email notifications
# run_sync_task >> success_notification_task
