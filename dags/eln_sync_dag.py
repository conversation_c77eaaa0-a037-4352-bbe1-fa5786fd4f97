"""
ELN Synchronization DAG

This DAG runs the ELN synchronization system to sync data from ELN to ChemStack.
It uses the cleaned up ELN sync system with proper error handling and monitoring.
"""

import logging
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.email import EmailOperator
from airflow.utils.dates import days_ago
import sys
import os
from dags.eln.eln_sync import *

# Add paths for ELN imports
sys.path.insert(0, '/opt/airflow/dags/eln')
sys.path.insert(0, '/opt/airflow/dag_dependencies')

logger = logging.getLogger(__name__)

# DAG Configuration
DAG_ID = 'eln_sync_dag'
SCHEDULE_INTERVAL = '0 2 * * *'  # Daily at 2 AM
MAX_ACTIVE_RUNS = 1
CATCHUP = False

# Email configuration for notifications
EMAIL_ON_FAILURE = True
EMAIL_ON_RETRY = False
EMAIL_LIST = ['<EMAIL>']  # Update with actual email addresses

# Default arguments for the DAG
default_args = {
    'owner': 'data-team',
    'depends_on_past': False,
    'start_date': days_ago(1),
    'email_on_failure': EMAIL_ON_FAILURE,
    'email_on_retry': EMAIL_ON_RETRY,
    'email': EMAIL_LIST,
    'retries': 2,
    'retry_delay': timedelta(minutes=15),
    'execution_timeout': timedelta(hours=2),
}


def run_eln_sync(**context):
    """
    Main function to run ELN synchronization.
    
    Args:
        context: Airflow context containing execution date, etc.
    """
    try:
        
        logger.info("Starting ELN synchronization")
        
        sync = ELNSync()
        
        try:
            execution_date = context['execution_date']
            is_manual = context.get('dag_run').external_trigger if context.get('dag_run') else False
            
            logger.info(f"Execution date: {execution_date}, Manual trigger: {is_manual}")
            
            force_full_sync = is_manual
            results = sync.sync_all_protocols(force_full_sync=force_full_sync)
            
            logger.info(f"ELN sync completed: {results}")
            
            if results['failed'] > 0:
                error_msg = f"ELN sync completed with {results['failed']} failures out of {results['total_protocols']} protocols"
                logger.warning(error_msg)
                
                failure_rate = results['failed'] / max(results['total_protocols'], 1)
                if failure_rate > 0.5:
                    raise Exception(f"High failure rate: {failure_rate:.1%} of protocols failed")
            
            return {
                'status': 'success',
                'total_protocols': results['total_protocols'],
                'successful': results['successful'],
                'failed': results['failed'],
                'errors': results['errors'][:5]
            }
            
        finally:
            sync.cleanup()
            
    except Exception as e:
        logger.error(f"ELN sync failed: {e}")
        raise

dag = DAG(
    DAG_ID,
    default_args=default_args,
    description='ELN to ChemStack synchronization pipeline',
    schedule_interval=SCHEDULE_INTERVAL,
    max_active_runs=MAX_ACTIVE_RUNS,
    catchup=CATCHUP,
    tags=['eln', 'chemstack', 'sync', 'data-pipeline'],
)

run_sync_task = PythonOperator(
    task_id='run_eln_sync',
    python_callable=run_eln_sync,
    dag=dag,
)

run_sync_task