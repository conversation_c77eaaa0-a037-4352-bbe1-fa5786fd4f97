import logging
import sys,os
sys.path.insert(0, '/opt/airflow/dag_dependencies')
from typing import List, Dict, Any, Tuple
from pymongo import MongoClient
from pymongo.collection import Collection
from pymongo.errors import ConnectionFailure
from functools import lru_cache
import certifi

# --- MongoClient Caching ---
# @lru_cache(maxsize=4) # Cache up to 4 different MongoDB connection strings/clients
def get_mongo_client(connection_string: str) -> MongoClient:
    """
    Creates and caches MongoClient instances based on the connection string.
    Uses default connection pooling managed by PyMongo.
    Includes basic pool configuration and connection check.
    """
    logging.info(f"Creating or retrieving cached MongoClient for URI ending in ...{connection_string[-20:]}")
    try:
        # Configure pool options (adjust as needed)
        # - maxPoolSize: Max connections (default 100)
        # - minPoolSize: Min connections kept open (default 0)
        # - waitQueueTimeoutMS: Timeout (ms) waiting for a connection from pool
        client = MongoClient(
            connection_string,
            # maxPoolSize=50,       # Default 100 is often high, adjust based on load/server limits
            # minPoolSize=5,        # Keep 5 connections warm
            # waitQueueTimeoutMS=15000 # Wait 15 seconds for a connection
            # Add other options like TLS/SSL if needed, e.g., tls=True, tlsCAFile=...
            # ,tlsCAFile=certifi.where()
        )
        # Test connection - ismaster is cheap and performs implicit auth
        client.admin.command('ismaster')
        logging.info("MongoClient connected and verified successfully.")
        return client
    except ConnectionFailure as e:
        logging.error(f"Failed to connect to MongoDB at {connection_string[-20:]}: {e}")
        raise # Re-raise the exception so the caller knows connection failed

class MongoOperations:
    def __init__(self, connection_string: str, database_name: str):
        """
        Initialize MongoDB operations using a shared, cached MongoClient.
        :param connection_string: MongoDB connection string URI
        :param database_name: Name of the database to use
        """
        # Get the cached client based on the connection string
        self.client = get_mongo_client(connection_string)
        # Select the target database using the shared client
        self.db = self.client[database_name]
        logging.info(f"MongoOperations initialized for database '{database_name}' using shared client for ...{connection_string[-20:]}")

    def get_collection(self, collection_name: str) -> Collection:
        """
        Get MongoDB collection from the selected database.
        :param collection_name: Name of the collection
        :return: MongoDB collection object
        """
        return self.db[collection_name]

    def read_data(self, collection_name: str, query: Dict[str, Any] = None, 
                  projection: Dict[str, Any] = None,
                  sort: List[Tuple[str, int]] = None) -> List[Dict[str, Any]]:
        """
        Read data from MongoDB
        :param collection_name: Name of the collection
        :param query: MongoDB query dictionary
        :param projection: Dictionary specifying the fields to return. Example: {"field1": 1, "field2": 1, "_id": 0}
                         where 1 means include and 0 means exclude
        :param sort: List of tuples for sorting (field, direction). Example: [("field1", 1), ("field2", -1)]
                    where 1 is ascending and -1 is descending
        :return: List of documents
        """
        collection = self.get_collection(collection_name)
        cursor = collection.find(query or {}, projection or None)
        if sort:
            cursor = cursor.sort(sort)
        results = list(cursor)
        return results

    def read_data_paginated(self, collection_name: str, page: int = 1, page_size: int = 100, 
                          query: Dict[str, Any] = None, sort: List[Tuple[str, int]] = None) -> Tuple[List[Dict[str, Any]], int]:
        """
        Read data from MongoDB with pagination
        :param collection_name: Name of the collection
        :param page: Page number (1-based)
        :param page_size: Number of records per page
        :param query: MongoDB query dictionary
        :param sort: List of tuples for sorting (field, direction)
        :return: Tuple of (list of documents, total count)
        """
        collection = self.get_collection(collection_name)
        skip = (page - 1) * page_size

        # Get total count
        total_count = collection.count_documents(query or {})

        # Get paginated data
        cursor = collection.find(query or {})
        if sort:
            cursor = cursor.sort(sort)
        cursor = cursor.skip(skip).limit(page_size)
        
        results = list(cursor)
        return results, total_count

    def write_data(self, collection_name: str, data: Dict[str, Any]) -> str:
        """
        Write data to MongoDB
        :param collection_name: Name of the collection
        :param data: Document to insert
        :return: ID of the inserted document
        """
        collection = self.get_collection(collection_name)
        result = collection.insert_one(data)
        return str(result.inserted_id)

    def insert_many(self, collection_name: str, documents: List[Dict[str, Any]]) -> List[str]:
        """
        Insert multiple documents into MongoDB
        :param collection_name: Name of the collection
        :param documents: List of documents to insert
        :return: List of inserted document IDs
        """
        if not documents:
            return []

        collection = self.get_collection(collection_name)
        result = collection.insert_many(documents)
        return [str(id) for id in result.inserted_ids]

    def update_one(self, collection_name: str, query: Dict[str, Any], update: Dict[str, Any]) -> bool:
        """
        Update a single document in MongoDB
        :param collection_name: Name of the collection
        :param query: Query to find the document to update
        :param update:  operations to perform
        :return: True if a document was updated, False otherwise
        """
        collection = self.get_collection(collection_name)
        result = collection.update_one(query, update)
        return result.modified_count > 0

    def update_data(self, collection_name: str, query: Dict[str, Any], update: Dict[str, Any]) -> int:
        """
        Update data in MongoDB
        :param collection_name: Name of the collection
        :param query: Query to find documents to update
        :param update: operations to perform
        :return: Number of documents modified
        """
        collection = self.get_collection(collection_name)
        result = collection.update_many(query, update)
        return result.modified_count
    
    def upsert_data(self, collection_name: str, query: Dict[str, Any], update: Dict[str, Any]) -> int:
        """
        Upsert data in MongoDB
        :param collection_name: Name of the collection
        :param query: Query to find documents to update
        :param update: operations to perform
        :return: Number of documents modified
        """
        collection = self.get_collection(collection_name)
        result = collection.update_one(query, update, upsert=True)
        return result.modified_count

    def close(self):
        """
        NO LONGER closes the client.
        The shared MongoClient and its pool should persist globally.
        Closing it here would affect all users of the cached client in this process.
        """
        logging.debug("MongoOperations.close() called - NO-OP for shared MongoClient.")
        # Intentionally do not call self.client.close()
        pass

    def aggregate(self, pipeline: List[Dict[str, Any]], collection_name: str = None, allow_disk_use: bool = True) -> List[Dict[str, Any]]:
        """
        Run a raw MongoDB aggregation pipeline
        :param pipeline: List of aggregation pipeline stages (raw MongoDB aggregation query)
        :param collection_name: Optional name of the collection to start aggregation from. If not provided, 
                              the pipeline should start with $collection stage
        :param allow_disk_use: Allow disk usage for large aggregation operations
        :return: List of aggregated documents
        :raises: Exception if aggregation fails
        """
        try:
            if collection_name:
                collection = self.get_collection(collection_name)
                return list(collection.aggregate(
                    pipeline,
                    allowDiskUse=allow_disk_use
                ))
            else:
                # For database-level operations
                # Connection from pool used implicitly
                # Ensure the command format is correct for your PyMongo version if issues arise
                # This syntax looks standard
                cursor = self.db.aggregate(pipeline, allowDiskUse=allow_disk_use)
                return list(cursor)
        except Exception as e:
            # Add more specific logging
            logging.error(f"MongoDB aggregation failed. Pipeline: {pipeline}, Collection: {collection_name}, Error: {e}")
            raise Exception(f"Aggregation failed: {str(e)}") 